# CONFIG_LOCALVERSION_AUTO is not set
CONFIG_KERNEL_XZ=y
CONFIG_DEFAULT_HOSTNAME="localhost"
CONFIG_SYSVIPC=y
CONFIG_NO_HZ_IDLE=y
CONFIG_HIGH_RES_TIMERS=y
CONFIG_LOG_BUF_SHIFT=14
CONFIG_CC_OPTIMIZE_FOR_SIZE=y
CONFIG_LD_DEAD_CODE_DATA_ELIMINATION=y
# CONFIG_BUG is not set
# CONFIG_ELF_CORE is not set
# CONFIG_BASE_FULL is not set
# CONFIG_IO_URING is not set
CONFIG_EMBEDDED=y
CONFIG_ARCH_ROCKCHIP=y
# CONFIG_HARDEN_BRANCH_PREDICTOR is not set
# CONFIG_HARDEN_BRANCH_HISTORY is not set
# CONFIG_VDSO is not set
# CONFIG_CACHE_L2X0 is not set
# CONFIG_ARM_ERRATA_643719 is not set
CONFIG_SMP=y
CONFIG_SCHED_MC=y
CONFIG_VMSPLIT_3G_OPT=y
CONFIG_VIDEO_TP2855=y
CONFIG_HOTPLUG_CPU=y
CONFIG_ARM_PSCI=y
CONFIG_THUMB2_KERNEL=y
CONFIG_HIGHMEM=y
# Rockchip ISP1 driver
CONFIG_VIDEO_ROCKCHIP_ISP1=y
# CONFIG_CPU_SW_DOMAIN_PAN is not set
CONFIG_ARCH_FORCE_MAX_ORDER=9
CONFIG_UACCESS_WITH_MEMCPY=y
# CONFIG_STACKPROTECTOR_PER_TASK is not set
CONFIG_CMDLINE="user_debug=31"
CONFIG_CMDLINE_EXTEND=y
CONFIG_CPU_FREQ=y
CONFIG_CPU_FREQ_DEFAULT_GOV_ONDEMAND=y
CONFIG_CPU_FREQ_GOV_USERSPACE=y
CONFIG_CPUFREQ_DT=y
CONFIG_ARM_ROCKCHIP_CPUFREQ=y
CONFIG_CPU_IDLE=y
CONFIG_ARM_CPUIDLE=y
CONFIG_ARM_PSCI_CPUIDLE=y
# CONFIG_ARM_PSCI_CPUIDLE_DOMAIN is not set
CONFIG_VFP=y
CONFIG_NEON=y
# CONFIG_SUSPEND is not set
CONFIG_JUMP_LABEL=y
# CONFIG_SECCOMP is not set
# CONFIG_STACKPROTECTOR_STRONG is not set
# CONFIG_STRICT_KERNEL_RWX is not set
CONFIG_MODULES=y
CONFIG_MODULE_UNLOAD=y
CONFIG_PARTITION_ADVANCED=y
# CONFIG_MSDOS_PARTITION is not set
CONFIG_CMDLINE_PARTITION=y
# CONFIG_MQ_IOSCHED_KYBER is not set
CONFIG_IOSCHED_BFQ=y
# CONFIG_SWAP is not set
CONFIG_KSM=y
CONFIG_DEFAULT_MMAP_MIN_ADDR=32768
CONFIG_CMA=y
CONFIG_CMA_INACTIVE=y
# CONFIG_VM_EVENT_COUNTERS is not set
CONFIG_LRU_GEN=y
CONFIG_LRU_GEN_ENABLED=y
CONFIG_NET=y
CONFIG_PACKET=y
CONFIG_UNIX=y
CONFIG_INET=y
CONFIG_INET_TABLE_PERTURB_ORDER=8
# CONFIG_INET_DIAG is not set
# CONFIG_IPV6 is not set
# CONFIG_WIRELESS is not set
CONFIG_DEVTMPFS=y
CONFIG_DEVTMPFS_MOUNT=y
# CONFIG_ALLOW_DEV_COREDUMP is not set
CONFIG_ROCKCHIP_SIP=y
CONFIG_MTD=y
CONFIG_MTD_CMDLINE_PARTS=y
# CONFIG_MTD_OF_PARTS is not set
CONFIG_NETDEVICES=y
# CONFIG_NET_CORE is not set
CONFIG_STMMAC_ETH=y
# CONFIG_DWMAC_GENERIC is not set
CONFIG_ROCKCHIP_FEPHY=y
# CONFIG_WLAN is not set
# CONFIG_INPUT is not set
# CONFIG_SERIO is not set
# CONFIG_VT is not set
# CONFIG_LEGACY_PTYS is not set
CONFIG_SERIAL_8250=y
# CONFIG_SERIAL_8250_DEPRECATED_OPTIONS is not set
# CONFIG_SERIAL_8250_16550A_VARIANTS is not set
CONFIG_SERIAL_8250_CONSOLE=y
CONFIG_SERIAL_8250_NR_UARTS=8
CONFIG_SERIAL_8250_RUNTIME_UARTS=8
CONFIG_SERIAL_8250_DW=y
CONFIG_HW_RANDOM=y
CONFIG_HW_RANDOM_ROCKCHIP=y
# CONFIG_RANDOM_TRUST_BOOTLOADER is not set
CONFIG_I2C=y
CONFIG_I2C_CHARDEV=y
CONFIG_I2C_RK3X=y
# CONFIG_PTP_1588_CLOCK is not set
CONFIG_GPIO_SYSFS=y
CONFIG_POWER_RESET=y
CONFIG_POWER_RESET_RESTART=y
CONFIG_SYSCON_REBOOT_MODE=y
CONFIG_POWER_SUPPLY=y
# CONFIG_HWMON is not set
CONFIG_THERMAL=y
CONFIG_THERMAL_WRITABLE_TRIPS=y
CONFIG_THERMAL_GOV_USER_SPACE=y
CONFIG_CPU_THERMAL=y
CONFIG_DEVFREQ_THERMAL=y
CONFIG_ROCKCHIP_THERMAL=y
CONFIG_WATCHDOG=y
CONFIG_DW_WATCHDOG=y
CONFIG_MFD_RK808=y
CONFIG_REGULATOR_FIXED_VOLTAGE=y
CONFIG_REGULATOR_GPIO=y
CONFIG_REGULATOR_PWM=y
CONFIG_REGULATOR_RK801=y
CONFIG_REGULATOR_RK808=y
CONFIG_MEDIA_SUPPORT=y
CONFIG_MEDIA_SUPPORT_FILTER=y
# CONFIG_MEDIA_SUBDRV_AUTOSELECT is not set
CONFIG_MEDIA_CAMERA_SUPPORT=y
CONFIG_MEDIA_PLATFORM_SUPPORT=y
CONFIG_V4L_PLATFORM_DRIVERS=y
CONFIG_VIDEO_ROCKCHIP_AIISP=y
CONFIG_VIDEO_ROCKCHIP_AVSP=y
CONFIG_VIDEO_ROCKCHIP_CIF=y
CONFIG_VIDEO_ROCKCHIP_ISP=y
CONFIG_VIDEO_ROCKCHIP_VPSS=y
CONFIG_VIDEOBUF2_CMA_SG=y
CONFIG_VIDEOBUF2_VMALLOC=y
CONFIG_VIDEO_RK_IRCUT=y
CONFIG_VIDEO_IMX415=y
CONFIG_VIDEO_OS04A10=y
CONFIG_ROCKCHIP_MULTI_RGA=y
CONFIG_ROCKCHIP_MPP_OSAL=y
CONFIG_ROCKCHIP_DVBM=y
CONFIG_SOUND=y
CONFIG_SND=y
# CONFIG_SND_PCM_TIMER is not set
# CONFIG_SND_SUPPORT_OLD_API is not set
# CONFIG_SND_DRIVERS is not set
# CONFIG_SND_ARM is not set
CONFIG_SND_SOC=y
CONFIG_SND_SOC_ROCKCHIP=y
CONFIG_SND_SOC_ROCKCHIP_SAI=y
CONFIG_SND_SOC_RK3506=y
CONFIG_SND_SIMPLE_CARD=y
# CONFIG_USB_SUPPORT is not set
CONFIG_RTC_CLASS=y
CONFIG_RTC_DRV_RK808=y
CONFIG_RTC_DRV_ROCKCHIP=y
CONFIG_DMADEVICES=y
CONFIG_ROCKCHIP_DMA=y
CONFIG_SYNC_FILE=y
CONFIG_DMABUF_HEAPS=y
CONFIG_DMABUF_HEAPS_SYSTEM=y
CONFIG_DMABUF_HEAPS_ROCKCHIP=y
CONFIG_DMABUF_HEAPS_ROCKCHIP_CMA_HEAP=y
CONFIG_DMABUF_HEAPS_ROCKCHIP_CMA_ALIGNMENT=0
CONFIG_DMABUF_RK_HEAPS_DEBUG=y
# CONFIG_VIRTIO_MENU is not set
# CONFIG_VHOST_MENU is not set
CONFIG_STAGING=y
CONFIG_COMMON_CLK_PROCFS=y
CONFIG_COMMON_CLK_RK808=y
CONFIG_ROCKCHIP_CLK_OUT=y
CONFIG_ROCKCHIP_CLK_PVTPLL=y
# CONFIG_ARM_ARCH_TIMER_EVTSTREAM is not set
CONFIG_ROCKCHIP_IOMMU=y
CONFIG_CPU_RV1126B=y
CONFIG_ROCKCHIP_AMP=y
CONFIG_ROCKCHIP_CPUINFO=y
CONFIG_ROCKCHIP_PM_DOMAINS=y
CONFIG_ROCKCHIP_PVTM=y
CONFIG_ROCKCHIP_SYSTEM_MONITOR=y
CONFIG_FIQ_DEBUGGER=y
CONFIG_FIQ_DEBUGGER_NO_SLEEP=y
CONFIG_FIQ_DEBUGGER_CONSOLE=y
CONFIG_FIQ_DEBUGGER_CONSOLE_DEFAULT_ENABLE=y
CONFIG_RK_CONSOLE_THREAD=y
CONFIG_ROCKCHIP_MINI_KERNEL=y
CONFIG_ROCKCHIP_NPOR_POWERGOOD=y
CONFIG_PM_DEVFREQ=y
CONFIG_DEVFREQ_GOV_SIMPLE_ONDEMAND=y
CONFIG_DEVFREQ_GOV_USERSPACE=y
CONFIG_IIO=y
CONFIG_ROCKCHIP_SARADC=y
CONFIG_PWM=y
CONFIG_PWM_ROCKCHIP=y
CONFIG_PHY_ROCKCHIP_CSI2_DPHY=m
CONFIG_PHY_ROCKCHIP_MIPI_RX=y
# CONFIG_NVMEM_SYSFS is not set
CONFIG_NVMEM_ROCKCHIP_OTP=y
CONFIG_ROCKCHIP_RKNPU=m
CONFIG_ROCKCHIP_RKNPU_PROC_FS=y
# CONFIG_FILE_LOCKING is not set
# CONFIG_DNOTIFY is not set
CONFIG_TMPFS=y
CONFIG_SQUASHFS=y
# CONFIG_SQUASHFS_ZLIB is not set
CONFIG_SQUASHFS_XZ=y
CONFIG_SQUASHFS_4K_DEVBLK_SIZE=y
# CONFIG_NETWORK_FILESYSTEMS is not set
CONFIG_NLS=y
# CONFIG_XZ_DEC_X86 is not set
# CONFIG_XZ_DEC_POWERPC is not set
# CONFIG_XZ_DEC_IA64 is not set
# CONFIG_XZ_DEC_SPARC is not set
CONFIG_DMA_CMA=y
CONFIG_CMA_SIZE_MBYTES=0
CONFIG_PRINTK_TIME=y
CONFIG_PRINTK_TIME_FROM_ARM_ARCH_TIMER=y
# CONFIG_DEBUG_MISC is not set
CONFIG_DEBUG_INFO_DWARF_TOOLCHAIN_DEFAULT=y
# CONFIG_SLUB_DEBUG is not set
# CONFIG_SCHED_DEBUG is not set
# CONFIG_RCU_TRACE is not set
# CONFIG_FTRACE is not set
# CONFIG_RUNTIME_TESTING_MENU is not set
