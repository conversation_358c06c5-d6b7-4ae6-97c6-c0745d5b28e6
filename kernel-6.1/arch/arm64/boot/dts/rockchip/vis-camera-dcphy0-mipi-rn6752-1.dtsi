/**
 * mipi csi to rn6752 config
 */
 

&i2c1 {
        status = "okay";
        pinctrl-names = "default";
        pinctrl-0 = <&i2c1m2_pins>;

       RN6752_1: RN6752@2c {
		compatible = "richnex,rn6752v1";
		reg = <0x2c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
         pinctrl-names = "rockchip,camera_default";
		 pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "RN6752-4CH";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&mipi0_csi2_input>;
				data-lanes = <1 2>;
			};
		};
	};
	
       
};


&cru {
    assigned-clocks = <&cru PLL_GPLL>;
    assigned-clock-rates = <594000000>;  // 37.125 MHz × 16 = 594 MHz
};


&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2>;
			};
		};
	};
};


&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};


&rkcif {
    status = "okay";
};

&rkcif_mmu {
    status = "okay";
};

