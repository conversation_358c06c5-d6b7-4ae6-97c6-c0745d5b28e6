// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

#include "rv1126bp-evb-v14-rn6752.dtsi"

&uart2 {
    /* Disable UART2 to free up pin 104 for GPIO use */
    status = "disabled";
};

/* 
 * Alternative approach: If you need UART2, you can modify the pinctrl
 * to use a different pin mux configuration
 */
/*
&pinctrl {
    uart2 {
        uart2m1_xfer_pins: uart2m1-xfer-pins {
            rockchip,pins =
                // Use alternative pins for UART2
                <3 RK_PA0 2 &pcfg_pull_none>,
                <3 RK_PA1 2 &pcfg_pull_none>;
        };
    };
};

&uart2 {
    pinctrl-names = "default";
    pinctrl-0 = <&uart2m1_xfer_pins>;
    status = "okay";
};
*/