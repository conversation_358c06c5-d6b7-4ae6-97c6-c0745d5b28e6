// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

/*
 * MIPI CSI2 DPHY mapping (aligned with rv1126-evb-v10.dtsi):
 * csi2_dphy0 -> mipi_csi2 (2 lane mode)
 * csi2_dphy1 -> direct to ISP (2 lane mode)
 */

#include "rv1126bp-evb-v14.dtsi"

&csi2_dphy0 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
		
			mipi_in_ucam0_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};
			mipi_in_ucam0_2815: endpoint@3 {
				reg = <3>;
				remote-endpoint = <&ucam_out0_2815>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_csi2_input>;
				data-lanes = <1 2>;
			};
		};
	};
};

&csi2_dphy1 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csi2_dphy1_input_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csi2_dphy1_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_in>;
				data-lanes = <1 2>;
			};
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;
	clock-frequency = <100000>;
	ar0230: ar0230@10 {
		compatible = "aptina,ar0230";
		reg = <0x10>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		/* power-domains = <&power RV1126_PD_VI>; */
		/* pwdn-gpios = <&gpio5 RK_PA2 GPIO_ACTIVE_HIGH>; */
		/*reset-gpios = <&gpio5 RK_PC1 GPIO_ACTIVE_HIGH>;*/
		rockchip,grf = <&grf>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "CMK-OT0836-PT2";
		rockchip,camera-module-lens-name = "YT-2929";
		port {
			cam_para_out1: endpoint {
				/* remote-endpoint = <&cif_para_in>; */
			};
		};
	};
	RN6752_1: RN6752@2c {
		compatible = "rn6752,rn6752";
		reg = <0x2c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";

		// pinctrl-names = "default";
		// pinctrl-0 = <&cam_clk0_pins>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "RN6752";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&mipi_in_ucam0_6752>;
				/* Fix lane mapping - use lanes 1 and 2 to match rv1126-evb-v10.dtsi */
				data-lanes = <1 2>;
			};
		};
	};
	
	RN6752_2: RN6752@2d {
		compatible = "rn6752,rn6752";
		reg = <0x2d>;
		clocks = <&cru CLK_MIPI1_OUT2IO>;
		clock-names = "xvclk";
		// pinctrl-names = "default";
		// pinctrl-0 = <&cam_clk0_pins>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "RN6752-4CH";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out1_6752: endpoint {
				remote-endpoint = <&csi2_dphy1_input_6752>;
				/* Fix lane mapping - use lanes 1 and 2 to match rv1126-evb-v10.dtsi */
				data-lanes = <1 2>;
			};
		};
	};
	
	tp2815: tp2815@44 {
		compatible = "techpoint,tp2815";
		reg = <0x44>;
		status = "disabled";
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		/* power-domains = <&power RV1126_PD_VI>; */
		pinctrl-names = "rockchip,camera_default";
		pinctrl-0 = <&cam_clk0_pins>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		 power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "YT10092";
		rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
		port {
			ucam_out0_2815: endpoint {
				remote-endpoint = <&mipi_in_ucam0_2815>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

};

&mipi0_csi2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
				data-lanes = <1 2>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2>;
			};
		};
	};
};



&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";
	// rockchip,cif-monitor = <2 2 5 100 1>;
	rockchip,cif-monitor = <3 2 10 10 5>;
	port {
		/* MIPI CSI-2 endpoint */
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";
	
	port {
		/* MIPI CSI-2 endpoint */
		mipi_lvds_sditf: endpoint {
			// remote-endpoint = <&isp_in>;
			data-lanes = <1 2>;
		};
	};
};



&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "okay";
};

&rkisp_mmu {
	status = "okay";
};

&rkisp_vir0 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			isp_in: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&csi2_dphy1_output>;
				data-lanes = <1 2>;
			};
		};
	};
};


&rkvpss_vir0 {
	status = "okay";
};

&rkvpss_vir1 {
	status = "okay";
};