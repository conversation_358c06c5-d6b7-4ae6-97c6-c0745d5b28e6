// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

&pinctrl {


	mipicsi_clk0: mipicsi-clk0 {
		rockchip,pins =
			/* mipicsi clk0 */
			<4 RK_PB1 3 &pcfg_pull_none>;
	};
};
&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			// csi_dphy_input0: endpoint@1 {
			// 	reg = <1>;
			// 	remote-endpoint = <&tp2815_out>;
			// 	data-lanes = <1 2 3 4>;
			// };
			csi_dphy_input_6752: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};
			csi_dphy_input1_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};

			
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;

	RN6752_1: RN6752@2c {
		compatible = "rn6752,rn6752";
		reg = <0x2c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";

		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		 power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "RN6752";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&csi_dphy_input_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	RN6752_2: RN6752@2d {
		status = "okay";
		compatible = "rn6752,rn6752";
		reg = <0x2d>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";

		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "RN6752";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out1_6752: endpoint {
				remote-endpoint = <&csi_dphy_input1_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in0>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in0: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";

	port {
		mipi_lvds_sditf: endpoint {
			remote-endpoint = <&isp_vir0>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "okay";
};

&rkisp_mmu {
	status = "okay";
};

&rkisp_vir0 {
	status = "okay";

	port {
		#address-cells = <1>;
		#size-cells = <0>;

		isp_vir0: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf>;
		};
	};
};

&rkisp_vir0_sditf {
	status = "okay";
};

&rkvpss {
	status = "okay";
	dvbm = <&rkdvbm>;
};

&rkvpss_mmu {
	status = "okay";
};

&rkvpss_vir0 {
	status = "okay";
};
