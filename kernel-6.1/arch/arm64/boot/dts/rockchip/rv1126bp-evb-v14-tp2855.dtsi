// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 * Device tree configuration for Techpoint TP2855 camera sensor
 * This replaces the RN6752 configuration to properly support TP2855
 * 
 * TP2855 Configuration:
 * - I2C Address: 0x44
 * - Chip ID: 0x28 0x55
 * - Supports 2-lane and 4-lane MIPI modes
 * - Output format: UYVY8_2X8
 * - Resolutions: 720p, 1080p
 */

/ {
	pinctrl {
		mipicsi {
			/omit-if-no-ref/
			mipicsi_clk0: mipicsi-clk0 {
				rockchip,pins =
					/* mipicsi_clk0 */
					<4 RK_PB1 1 &pcfg_pull_none>;
			};
		};
	};
};

/* Enable csi2_dphy0 for 2-lane mode (TP2855) */
&csi2_dphy0 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
		
			mipi_in_ucam0_tp2855: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&ucam_out0_tp2855>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_csi2_input>;
				data-lanes = <1 2>;
			};
		};
	};
};

/* Disable other dphy ports to avoid conflicts */
&csi2_dphy1 {
	status = "disabled";
};

&csi2_dphy2 {
	status = "disabled";
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;
	clock-frequency = <100000>;
	
	/* TP2855 Camera Sensor Configuration */
	tp2855: tp2855@44 {
		compatible = "techpoint,tp2855";
		reg = <0x44>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "TP2855-4CH";
		rockchip,camera-module-lens-name = "TP2855-CVBS";
		status = "okay";
		port {
			ucam_out0_tp2855: endpoint {
				remote-endpoint = <&mipi_in_ucam0_tp2855>;
				data-lanes = <1 2>;
			};
		};
	};
};

&mipi0_csi2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			/* Input from csi2_dphy0 (2-lane mode) */
			mipi_csi2_input: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&csidphy0_out>;
				data-lanes = <1 2>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2>;
			};
		};
	};
};

/* Disable mipi1_csi2 since we're using single sensor */
&mipi1_csi2 {
	status = "disabled";
};

&rkcif_mipi_lvds {
	status = "okay";
	
	port {
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";
	
	port {
		mipi_lvds_sditf: endpoint {
			remote-endpoint = <&isp_in>;
			data-lanes = <1 2>;
		};
	};
};

&rkisp_vir0 {
	status = "okay";
	
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		
		isp_in: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf>;
		};
	};
};