// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

/*
 * csi2_dphy0 -> csi0(rx0) clk0 + 4 lane
 * csi2_dphy1 -> csi0(rx0) clk0 + 2 lane 0/1
 * csi2_dphy2 -> csi0(rx0) clk1 + 2 lane 2/3
 * csi2_dphy3 -> csi1(rx1) clk0 + 4 lane
 * csi2_dphy4 -> csi1(rx1) clk0 + 2 lane 0/1
 * csi2_dphy5 -> csi1(rx1) clk1 + 2 lane 2/3
 */


&csi2_dphy1 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_in_ucam0_2815: endpoint@3 {
				reg = <3>;
				remote-endpoint = <&ucam_out0_2815>;
				data-lanes = <1 2 3 4>; // TP2815保持4通道
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy1_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

&csi2_dphy2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csi_dphy2_input_6752: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy2_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_input>;
			};
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;
	clock-frequency = <100000>;
	ar0230: ar0230@10 {
		compatible = "aptina,ar0230";
		reg = <0x10>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		/* power-domains = <&power RV1126_PD_VI>; */
		/* pwdn-gpios = <&gpio5 RK_PA2 GPIO_ACTIVE_HIGH>; */
		/*reset-gpios = <&gpio5 RK_PC1 GPIO_ACTIVE_HIGH>;*/
		rockchip,grf = <&grf>;
		// pinctrl-names = "default";
		// pinctrl-0 = <&cifm0_dvp_ctl>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "CMK-OT0836-PT2";
		rockchip,camera-module-lens-name = "YT-2929";
		port {
			cam_para_out1: endpoint {
				/* remote-endpoint = <&cif_para_in>; */
			};
		};
	};
	RN6752_1: rn6752@2c {
		compatible = "sony,imx415";
		reg = <0x2c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		//power-domains = <&power RV1126B_PD_VI>;
		 pinctrl-names = "default";
		 pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&csi_dphy0_input_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	RN6752_2: rn6752@2d {
		status = "okay";
		compatible = "sony,imx415";
		reg = <0x2d>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		//power-domains = <&power RV1126_PD_VI>;
		 pinctrl-names = "default";
		 pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
		port {
			ucam_out1_6752: endpoint {
				remote-endpoint = <&csi_dphy2_input_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	tp2815: tp2815@44 {
		compatible = "techpoint,tp2815";
		reg = <0x44>;
		status = "okay";
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
	//	power-domains = <&power RV1126B_PD_VDO>;
	    pinctrl-names = "rockchip,camera_default";
		// pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
	    reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "YT10092";
		rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
		port {
			ucam_out0_2815: endpoint {
				remote-endpoint = <&mipi_in_ucam0_2815>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

};

&csi2_dphy0 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
		
			csi_dphy0_input_6752: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

// csi2_dphy3 configuration removed to avoid conflicts
// Using csi2_dphy2 for RN6752_2 instead

&mipi0_csi2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in0>;
			};
		};
	};
};

&mipi2_csi2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi2_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy2_out>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi2_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in2>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			cif_mipi_in0: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_output>;
			};
		};
		/* Added placeholder port to keep label referenced by disabled SDITF node */
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			cif_mipi_out0: endpoint@0 {
				reg = <0>;
			};
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "disabled";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds_sditf_in: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_out0>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds_sditf_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_vir0>;
			};
		};
	};
};

&rkcif_mipi_lvds2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			cif_mipi_in2: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_output>;
			};
		};
		/* Placeholder port for cif_mipi_out2 label */
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			cif_mipi_out2: endpoint@0 {
				reg = <0>;
			};
		};
	};
};

&rkcif_mipi_lvds2_sditf {
	status = "disabled";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds2_sditf_in: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_out2>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds2_sditf_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_vir1>;
			};
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "disabled";
};

&rkisp_mmu {
	status = "disabled";
};

&rkisp_vir0 {
	status = "disabled";
	
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		
		isp_vir0: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf_out>;
		};
	};
};

&rkisp_vir0_sditf {
	status = "disabled";
};

&rkisp_vir1 {
	status = "disabled";
	
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		
		isp_vir1: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds2_sditf_out>;
		};
	};
};

&rkisp_vir1_sditf {
	status = "disabled";
};
&rkvpss {
	status = "disabled";
};
&rkvpss_vir0 {
	status = "disabled";
};

&rkvpss_vir1 {
	status = "disabled";
};