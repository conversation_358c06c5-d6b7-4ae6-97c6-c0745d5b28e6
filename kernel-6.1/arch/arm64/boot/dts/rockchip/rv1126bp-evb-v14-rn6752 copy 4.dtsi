// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

/*
 * csi2_dphy0 -> csi0(rx0) clk0 + 4 lane
 * csi2_dphy1 -> csi0(rx0) clk0 + 2 lane 0/1
 * csi2_dphy2 -> csi0(rx0) clk1 + 2 lane 2/3
 * csi2_dphy3 -> csi1(rx1) clk0 + 4 lane
 * csi2_dphy4 -> csi1(rx1) clk0 + 2 lane 0/1
 * csi2_dphy5 -> csi1(rx1) clk1 + 2 lane 2/3
 */

// &pinctrl {
// 	cifm0_dvp_ctl: cifm0-dvp-ctl {
// 		rockchip,pins =
// 			/* cif dvp control */
// 			<6 RK_PC1 1 &pcfg_pull_none>,
// 			<6 RK_PC2 1 &pcfg_pull_none>,
// 			<6 RK_PC3 1 &pcfg_pull_none>,
// 			<6 RK_PC0 1 &pcfg_pull_none>;
// 	};

// 	mipicsi_clk0: mipicsi-clk0 {
// 		rockchip,pins =
// 			/* mipicsi clk0 */
// 			<4 RK_PB1 3 &pcfg_pull_none>;
// 	};

// 	mipicsi_clk1: mipicsi-clk1 {
// 		rockchip,pins =
// 			/* mipicsi clk1 */
// 			<4 RK_PB0 3 &pcfg_pull_none>;
// 	};
// };

&csi2_dphy1 {
	status = "disabled";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_in_ucam0_2815: endpoint@3 {
				reg = <3>;
				remote-endpoint = <&ucam_out0_2815>;
				data-lanes = <1 2 3 4>; // TP2815保持4通道
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy1_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

&csi2_dphy2 {
	status = "disabled";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csi_dphy2_input_6752: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy2_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_input>;
			};
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;
	clock-frequency = <100000>;
	ar0230: ar0230@10 {
		compatible = "aptina,ar0230";
		reg = <0x10>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		/* power-domains = <&power RV1126_PD_VI>; */
		/* pwdn-gpios = <&gpio5 RK_PA2 GPIO_ACTIVE_HIGH>; */
		/*reset-gpios = <&gpio5 RK_PC1 GPIO_ACTIVE_HIGH>;*/
		rockchip,grf = <&grf>;
		// pinctrl-names = "default";
		// pinctrl-0 = <&cifm0_dvp_ctl>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "CMK-OT0836-PT2";
		rockchip,camera-module-lens-name = "YT-2929";
		port {
			cam_para_out1: endpoint {
				/* remote-endpoint = <&cif_para_in>; */
			};
		};
	};
	RN6752_1: RN6752@2c {
		compatible = "sony,imx415";
		reg = <0x2c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		// pinctrl-names = "default";
		// pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "RN6752-4CH";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&csi_dphy0_input_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	RN6752_2: RN6752@2d {
		status = "disabled";
		compatible = "sony,imx415";
		reg = <0x2d>;
		clocks = <&cru CLK_MIPI1_OUT2IO>;
		clock-names = "xvclk";
		// pinctrl-names = "default";
		// pinctrl-0 = <&mipicsi_clk1>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "RN6752-4CH";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out1_6752: endpoint {
				remote-endpoint = <&csi_dphy2_input_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	tp2815: tp2815@44 {
		compatible = "techpoint,tp2815";
		reg = <0x44>;
		status = "disabled";
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		// power-domains = <&power RV1126_PD_VI>;
		// pinctrl-names = "rockchip,camera_default";
		// pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		// power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		// reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "YT10092";
		rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
		port {
			ucam_out0_2815: endpoint {
				remote-endpoint = <&mipi_in_ucam0_2815>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

};

&csi2_dphy0 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
		
			csi_dphy0_input_6752: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

// csi2_dphy3 configuration removed to avoid conflicts
// Using csi2_dphy2 for RN6752_2 instead

&mipi0_csi2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in0>;
			};
		};
	};
};

&mipi2_csi2 {
	status = "disabled";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi2_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy2_out>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi2_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in2>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			cif_mipi_in0: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_output>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			cif_mipi_out0: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_lvds_sditf_in>;
			};
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds_sditf_in: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_out0>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds_sditf_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_vir0>;
			};
		};
	};
};

&rkcif_mipi_lvds2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			cif_mipi_in2: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_output>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			cif_mipi_out2: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_lvds2_sditf_in>;
			};
		};
	};
};

&rkcif_mipi_lvds2_sditf {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds2_sditf_in: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_out2>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_lvds2_sditf_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_vir1>;
			};
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "okay";
};

&rkisp_mmu {
	status = "okay";
};

&rkisp_vir0 {
	status = "okay";
	
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		
		isp_vir0: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf_out>;
		};
	};
};

&rkisp_vir0_sditf {
	status = "okay";
};

&rkisp_vir1 {
	status = "okay";
	
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		
		isp_vir1: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds2_sditf_out>;
		};
	};
};

&rkisp_vir1_sditf {
	status = "okay";
};

&rkvpss_vir0 {
	status = "okay";
};

&rkvpss_vir1 {
	status = "okay";
};