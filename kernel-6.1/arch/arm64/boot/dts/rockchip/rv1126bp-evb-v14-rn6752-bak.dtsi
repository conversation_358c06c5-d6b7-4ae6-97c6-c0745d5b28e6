// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

/*
 * csi2_dphy0 -> csi0(rx0) clk0 + 4 lane
 * csi2_dphy1 -> csi0(rx0) clk0 + 2 lane 0/1
 * csi2_dphy2 -> csi0(rx0) clk1 + 2 lane 2/3
 * csi2_dphy3 -> csi1(rx1) clk0 + 4 lane
 * csi2_dphy4 -> csi1(rx1) clk0 + 2 lane 0/1
 * csi2_dphy5 -> csi1(rx1) clk1 + 2 lane 2/3
 */

&pinctrl {
	cifm0_dvp_ctl: cifm0-dvp-ctl {
		rockchip,pins =
			/* cif dvp control */
			<6 RK_PC1 1 &pcfg_pull_none>,
			<6 RK_PC2 1 &pcfg_pull_none>,
			<6 RK_PC3 1 &pcfg_pull_none>,
			<6 RK_PC0 1 &pcfg_pull_none>;
	};

	mipicsi_clk0: mipicsi-clk0 {
		rockchip,pins =
			/* mipicsi clk0 */
			<4 RK_PB1 3 &pcfg_pull_none>;
	};

	mipicsi_clk1: mipicsi-clk1 {
		rockchip,pins =
			/* mipicsi clk1 */
			<4 RK_PB0 3 &pcfg_pull_none>;
	};
};



&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_in_ucam0: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out0>;
				data-lanes = <1 2>;
			};

			mipi_in_ucam0_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};

			mipi_in_ucam0_2815: endpoint@3 {
				reg = <3>;
				remote-endpoint = <&ucam_out0_2815>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

#if 1
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_csi2_input>;
			};
#else
			csi_dphy1_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_in>;
			};
#endif
		};
	};
};

&csi2_dphy1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			csi_dphy1_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out1>;
				data-lanes = <1 2>;
			};

			csi_dphy1_input_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

#if 1
			csi_dphy1_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_in>;
				data-lanes = <1 2>;
			};
#else
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_csi2_input>;
			};
#endif
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;
	clock-frequency = <100000>;
	ar0230: ar0230@10 {
		compatible = "aptina,ar0230";
		reg = <0x10>;
		clocks = <&cru CLK_CIF_OUT2IO>;
		clock-names = "xvclk";
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		/* power-domains = <&power RV1126_PD_VI>; */
		/* pwdn-gpios = <&gpio5 RK_PA2 GPIO_ACTIVE_HIGH>; */
		/*reset-gpios = <&gpio5 RK_PC1 GPIO_ACTIVE_HIGH>;*/
		rockchip,grf = <&grf>;
		pinctrl-names = "default";
		pinctrl-0 = <&cifm0_dvp_ctl>;
		rockchip,camera-module-index = <2>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "CMK-OT0836-PT2";
		rockchip,camera-module-lens-name = "YT-2929";
		status = "disabled";
		port {
			cam_para_out1: endpoint {
				/* remote-endpoint = <&cif_para_in>; */
			};
		};
	};
	RN6752_1: RN6752@2c {

		compatible = "rn6752,rn6752";
		reg = <0x2c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";

		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		 power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "RN6752";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&mipi_in_ucam0_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	RN6752_2: RN6752@2d {
		compatible = "rn6752,rn6752";
		reg = <0x2d>;
		clocks = <&cru CLK_MIPI2_OUT2IO>;
		clock-names = "xvclk";

		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		 power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "RN6752";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		port {
			ucam_out1_6752: endpoint {
				remote-endpoint = <&csi_dphy1_input_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	tp2815: tp2815@44 {
		compatible = "techpoint,tp2815";
		reg = <0x44>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		/* power-domains = <&power RV1126_PD_VI>; */
		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB3 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA4 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <2>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "YT10092";
		rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
		status = "disabled";
		port {
			ucam_out0_2815: endpoint {
				remote-endpoint = <&mipi_in_ucam0_2815>;
				data-lanes = <1 2>;
			};
		};
	};

	ov4689: ov4689@5f {
		compatible = "ovti,os04a10";
		reg = <0x5f>;
		//clocks = <&cru CLK_MIPICSI_OUT>;
		clock-names = "xvclk";
		// power-domains = <&power RV1126_PD_VI>;
		pinctrl-names = "rockchip,camera_default";
		pinctrl-0 = <&mipicsi_clk0>;

		/*pinctrl-0 = <&mipicsi_clk0>;*/
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "JSD3425-C1";
		rockchip,camera-module-lens-name = "JSD3425-C1";
		port {
			ucam_out1: endpoint {
				remote-endpoint = <&csi_dphy1_input>;
				data-lanes = <1 2>;
			};
		};
	};

	os04a10: os04a10@5c {
		compatible = "ovti,os04a10";
		reg = <0x5c>;
		//clocks = <&cru CLK_MIPICSI_OUT>;
		clock-names = "xvclk";
		// power-domains = <&power RV1126_PD_VI>;
		pinctrl-names = "rockchip,camera_default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		/* pwdn-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>; */
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "CMK-OT1607-FV1";
		rockchip,camera-module-lens-name = "M12-40IRC-4MP-F16";
		/* ir-cut = <&cam_ircut0>; */
		port {
			ucam_out0: endpoint {
				remote-endpoint = <&mipi_in_ucam0>;
				data-lanes = <1 2>;
			};
		};
	};

};

&mipi2_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2>;
			};
		};
	};
};



&rkcif {
	status = "okay";
};

&rkcif_dvp {
	status = "okay";

	port {
		/* Parallel bus endpoint */
		/*
		cif_para_in: endpoint {
			remote-endpoint = <&cam_para_out1>;
			bus-width = <12>;
			hsync-active = <1>;
			vsync-active = <0>;
		};
		*/
	};
};

&rkcif_mipi_lvds {
	status = "okay";
	/* rockchip,cif-monitor = <2 2 5 100 1>; */
	rockchip,cif-monitor = <3 2 10 10 5>;
	port {
		/* MIPI CSI-2 endpoint */
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";

	port {
		/* MIPI CSI-2 endpoint */
		mipi_lvds_sditf: endpoint {
			/* remote-endpoint = <&isp_in>; */
			data-lanes = <1 2>;
		};
	};
};



&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "okay";
};

&rkisp_mmu {
	status = "okay";
};

&rkisp_vir0 {
	status = "okay";

	ports {
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			isp_in: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&csi_dphy1_output>;
				data-lanes = <1 2>;
			};
		};
	};
};