// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2024 Rockchip Electronics Co., Ltd.
 *
 */





&csi2_dphy0_hw {
	status = "okay";
};



&i2c1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1m2_pins>;

	
    ov4689: ov4689@5f {
		compatible = "ovti,os04a10";
		reg = <0x5f>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		
		 pinctrl-names = "rockchip,camera_default";
		 pinctrl-0 = <&mipicsi_clk0>;
		/*pinctrl-0 = <&mipicsi_clk0>;*/
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "JSD3425-C1";
		rockchip,camera-module-lens-name = "JSD3425-C1";
		port {
			ov4689_out: endpoint {
				remote-endpoint = <&mipi1_csi2_input>;
				data-lanes = <1 2>;
			};
		};

	};

	os04a10: os04a10@5c {
		compatible = "ovti,os04a10";
		reg = <0x5c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
	
		pinctrl-names = "rockchip,camera_default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		// pwdn-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "CMK-OT1607-FV1";
		rockchip,camera-module-lens-name = "M12-40IRC-4MP-F16";
		// ir-cut = <&cam_ircut0>;
		port {
			os04a10_out: endpoint {
				remote-endpoint = <&mipi0_csi2_input>;
				data-lanes = <1 2>;
			};
		};
	};
	
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&os04a10_out>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2>;
			};
		};
	};
};

&mipi1_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi1_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ov4689_out>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi1_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in1>;
				data-lanes = <1 2>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mipi_lvds1 {
	status = "okay";

	port {
		cif_mipi_in1: endpoint {
			remote-endpoint = <&mipi1_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};
