// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2024 Rockchip Electronics Co., Ltd.
 *
 */


&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy0_in_tp2815: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&tp2815_out0>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi1_csi2_input>;
			};
		};
	};
};

&csi2_dphy3 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy3_in_tp2815: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&tp2815_out1>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy3_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi3_csi2_input>;
			};
		};
	};
};

&csi2_dphy0_hw {
	status = "okay";
};

&csi2_dphy1_hw {
	status = "okay";
};

&i2c5 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c5m3_xfer>;

	tp2815: tp2815@44 {
		compatible = "techpoint,tp2815";
		status = "okay";
		reg = <0x44>;
		clocks = <&cru CLK_MIPI_CAMERAOUT_M1>;
		clock-names = "xvclk";
		power-domains = <&power RK3576_PD_VI>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk1m0_clk1>;
		avdd-supply = <&vcc_mipicsi0>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";

		port {
			tp2815_out0: endpoint {
				remote-endpoint = <&mipi_dphy0_in_tp2815>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	tp2815b: tp2815b@45 {
		compatible = "techpoint,tp2815";
		status = "okay";
		reg = <0x45>;
		clocks = <&cru CLK_MIPI_CAMERAOUT_M1>;
		clock-names = "xvclk";
		power-domains = <&power RK3576_PD_VI>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk1m0_clk1>;
		avdd-supply = <&vcc_mipicsi0>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";

		port {
			tp2815_out1: endpoint {
				remote-endpoint = <&mipi_dphy3_in_tp2815>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&mipi1_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi1_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi1_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in1>;
			};
		};
	};
};

&mipi3_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi3_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy3_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi3_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in3>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds1 {
	status = "okay";

	port {
		cif_mipi_in1: endpoint {
			remote-endpoint = <&mipi1_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds3 {
	status = "okay";

	port {
		cif_mipi_in3: endpoint {
			remote-endpoint = <&mipi3_csi2_output>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};
