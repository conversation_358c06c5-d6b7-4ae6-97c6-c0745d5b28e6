// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 * Fixed version of rv1126bp-evb-v14-rn6752.dtsi to resolve camera link conflicts
 * 
 * Key fixes:
 * 1. Removed conflicting isp_in endpoint from rkisp_vir0 that was connecting to csi2_dphy1
 * 2. Ensured rkcif_mipi_lvds_sditf does not have conflicting remote-endpoint
 * 3. Maintained proper data flow: Camera -> CSI2 DPHY -> MIPI CSI2 -> RKCIF
 */

/*
 * MIPI CSI2 DPHY mapping (split mode for proper lane assignment):
 * csi2_dphy1 -> mipi_csi2 (lanes 0/1) -> rkcif_mipi_lvds (RN6752_1)
 * csi2_dphy2 -> mipi_csi2 (lanes 2/3) -> rkcif_mipi_lvds (RN6752_2)
 * csi2_dphy0 -> DISABLED (4-lane mode conflicts with 2-lane cameras)
 */

// #include "rv1126bp-evb-v14.dtsi"

/ {
	pinctrl {
		mipicsi {
			/omit-if-no-ref/
			mipicsi_clk0: mipicsi-clk0 {
				rockchip,pins =
					/* mipicsi_clk0 */
					<4 RK_PB1 1 &pcfg_pull_none>;
			};
		};
	};
};

/* Disable csi2_dphy0 to avoid 4-lane mode conflicts */
&csi2_dphy0 {
	status = "disabled";
};

/* Enable csi2_dphy1 for lanes 0/1 (RN6752_1) */
&csi2_dphy1 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
		
			mipi_in_ucam0_6752: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy1_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi_csi2_input>;
				data-lanes = <1 2>;
			};
		};
	};
};

/* Enable csi2_dphy2 for lanes 2/3 (RN6752_2) */
&csi2_dphy2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
		
			mipi_in_ucam1_6752: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			csidphy2_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi1_csi2_input>;
				data-lanes = <1 2>;
			};
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;
	clock-frequency = <100000>;
	
	ar0230: ar0230@10 {
		compatible = "aptina,ar0230";
		reg = <0x10>;
		clocks = <&cru CLK_CIF_OUT2IO>;
		clock-names = "xvclk";
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		rockchip,grf = <&grf>;
		// pinctrl-names = "default";
		// pinctrl-0 = <&cifm0_dvp_ctl>;
		rockchip,camera-module-index = <2>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		status = "disabled";
	};
	
	RN6752_1: RN6752@2C {
		compatible = "richnex,rn6752v1";
		reg = <0x2C>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "RN6752-4CH";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		status = "okay";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&mipi_in_ucam0_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	/* Redirect RN6752_2 to connect to csi2_dphy0 instead of csi2_dphy1 */
	RN6752_2: RN6752@2D {
		compatible = "richnex,rn6752v1";
		reg = <0x2D>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		pinctrl-names = "default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "RN6752-4CH";
		rockchip,camera-module-lens-name = "RN6752-CVBS";
		status = "okay";
		port {
			/* Changed from csi2_dphy1_input_6752 to mipi_in_ucam1_6752 */
			ucam_out1_6752: endpoint {
				remote-endpoint = <&mipi_in_ucam1_6752>;
				data-lanes = <1 2>;
			};
		};
	};
	
	/* tp2815 disabled
		tp2815: tp2815@44 {
			compatible = "techpoint,tp2815";
			reg = <0x44>;
			clocks = <&cru CLK_MIPI0_OUT2IO>;
			clock-names = "xvclk";
			// pinctrl-names = "default";
			// pinctrl-0 = <&mipicsi_clk0>;
			avdd-supply = <&vcc_avdd>;
			dovdd-supply = <&vcc_dovdd>;
			dvdd-supply = <&vcc_dvdd>;
			power-gpios = <&gpio4 RK_PB3 GPIO_ACTIVE_HIGH>;
			reset-gpios = <&gpio6 RK_PA4 GPIO_ACTIVE_LOW>;
			rockchip,camera-module-index = <2>;
			rockchip,camera-module-facing = "front";
			rockchip,camera-module-name = "YT10092";
			rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
			status = "disabled";
			port {
				ucam_out0_2815: endpoint {
					remote-endpoint = <&mipi_in_ucam0_2815>;
					data-lanes = <1 2 3 4>;
				};
			};
		};
		*/
};

&mipi0_csi2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			/* Input from csi2_dphy1 (lanes 0/1) */
			mipi_csi2_input: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&csidphy1_out>;
				data-lanes = <1 2>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2>;
			};
		};
	};
};

/* Enable mipi1_csi2 for csi2_dphy2 (lanes 2/3) */
&mipi1_csi2 {
	status = "okay";
	
	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			/* Input from csi2_dphy2 (lanes 2/3) */
			mipi1_csi2_input: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&csidphy2_out>;
				data-lanes = <1 2>;
			};
		};
		
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			
			mipi1_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in1>;
				data-lanes = <1 2>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";
	// rockchip,cif-monitor = <2 2 5 100 1>;
	rockchip,cif-monitor = <3 2 10 10 5>;
	port {
		/* MIPI CSI-2 endpoint from mipi0_csi2 */
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

/* Enable rkcif_mipi_lvds1 for mipi1_csi2 */
&rkcif_mipi_lvds1 {
	status = "okay";
	rockchip,cif-monitor = <3 2 10 10 5>;
	port {
		/* MIPI CSI-2 endpoint from mipi1_csi2 */
		cif_mipi_in1: endpoint {
			remote-endpoint = <&mipi1_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";
	
	port {
		/* MIPI CSI-2 endpoint - removed conflicting isp_in connection */
		mipi_lvds_sditf: endpoint {
			/* No remote-endpoint to avoid conflicts */
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "okay";
};

&rkisp_mmu {
	status = "okay";
};

/* Remove conflicting rkisp_vir0 port@0 configuration */
&rkisp_vir0 {
	status = "okay";
	/* Use default configuration from rv1126b.dtsi without port@0 */
};