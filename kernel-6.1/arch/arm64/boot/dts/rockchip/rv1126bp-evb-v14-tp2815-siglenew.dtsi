// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2024 Rockchip Electronics Co., Ltd.
 *
 */





&csi2_dphy0_hw {
	status = "okay";
};



&i2c1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1m2_pins>;

	tp2815: tp2815@44 {
		compatible = "techpoint,tp2815";
		reg = <0x44>;
		status = "okay";
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
	//	power-domains = <&power RV1126B_PD_VDO>;
		 pinctrl-names = "rockchip,camera_default";
		 pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		 power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		 reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";

		port {
			tp2815_out0: endpoint {
				remote-endpoint = <&mipi0_csi2_input>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&tp2815_out0>;
				data-lanes = <1 2 3 4>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
			data-lanes = <1 2 3 4>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};
