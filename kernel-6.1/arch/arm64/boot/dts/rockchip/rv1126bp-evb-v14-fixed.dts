// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 * Fixed version of rv1126bp-evb-v14.dts that uses the conflict-free camera configuration
 */

/dts-v1/;
#include "rv1126b.dtsi"
#include "rv1126bp-evb.dtsi"
#include "rv1126bp-evb-v14.dtsi"
#include "rv1126bp-evb-v14-tp2855.dtsi"  // Use TP2855 configuration for detected hardware
//  #include "rv1126bp-evb-cam-vis.dtsi"
/ {
	model = "Rockchip RV1126B-P EVB V14 Board (TP2855 Camera)";
	compatible = "rockchip,rv1126bp-evb-v14", "rockchip,rv1126b";
	chosen {
		bootargs = "earlycon=uart8250,mmio32,0x20810000 console=ttyFIQ0 rw root=PARTUUID=614e0000-0000 rootfstype=ext4 rootwait snd_soc_core.prealloc_buffer_size_kbytes=16 coherent_pool=32K";
	};
};


&fspi0 {
	status = "okay";

	flash@0 {
		compatible = "spi-nand";
		reg = <0>;
		spi-max-frequency = <75000000>;
		spi-rx-bus-width = <4>;
		spi-tx-bus-width = <1>;
	};
};