/* DTS-XLATE-RV1126B */
// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Fuzhou Rockchip Electronics Co., Ltd
 */
&pinctrl {

	/omit-if-no-ref/
	pcfg_pull_up: pcfg-pull-up {
		bias-pull-up;
	};

	/omit-if-no-ref/
	pcfg_pull_down: pcfg-pull-down {
		bias-pull-down;
	};

	/omit-if-no-ref/
	pcfg_pull_none: pcfg-pull-none {
		bias-disable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_0: pcfg-pull-none-drv-level-0 {
		bias-disable;
		drive-strength = <0>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_1: pcfg-pull-none-drv-level-1 {
		bias-disable;
		drive-strength = <1>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_2: pcfg-pull-none-drv-level-2 {
		bias-disable;
		drive-strength = <2>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_3: pcfg-pull-none-drv-level-3 {
		bias-disable;
		drive-strength = <3>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_4: pcfg-pull-none-drv-level-4 {
		bias-disable;
		drive-strength = <4>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_5: pcfg-pull-none-drv-level-5 {
		bias-disable;
		drive-strength = <5>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_6: pcfg-pull-none-drv-level-6 {
		bias-disable;
		drive-strength = <6>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_7: pcfg-pull-none-drv-level-7 {
		bias-disable;
		drive-strength = <7>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_8: pcfg-pull-none-drv-level-8 {
		bias-disable;
		drive-strength = <8>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_9: pcfg-pull-none-drv-level-9 {
		bias-disable;
		drive-strength = <9>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_10: pcfg-pull-none-drv-level-10 {
		bias-disable;
		drive-strength = <10>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_11: pcfg-pull-none-drv-level-11 {
		bias-disable;
		drive-strength = <11>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_12: pcfg-pull-none-drv-level-12 {
		bias-disable;
		drive-strength = <12>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_13: pcfg-pull-none-drv-level-13 {
		bias-disable;
		drive-strength = <13>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_14: pcfg-pull-none-drv-level-14 {
		bias-disable;
		drive-strength = <14>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_15: pcfg-pull-none-drv-level-15 {
		bias-disable;
		drive-strength = <15>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_0: pcfg-pull-up-drv-level-0 {
		bias-pull-up;
		drive-strength = <0>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_1: pcfg-pull-up-drv-level-1 {
		bias-pull-up;
		drive-strength = <1>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_2: pcfg-pull-up-drv-level-2 {
		bias-pull-up;
		drive-strength = <2>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_3: pcfg-pull-up-drv-level-3 {
		bias-pull-up;
		drive-strength = <3>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_4: pcfg-pull-up-drv-level-4 {
		bias-pull-up;
		drive-strength = <4>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_5: pcfg-pull-up-drv-level-5 {
		bias-pull-up;
		drive-strength = <5>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_6: pcfg-pull-up-drv-level-6 {
		bias-pull-up;
		drive-strength = <6>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_7: pcfg-pull-up-drv-level-7 {
		bias-pull-up;
		drive-strength = <7>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_8: pcfg-pull-up-drv-level-8 {
		bias-pull-up;
		drive-strength = <8>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_9: pcfg-pull-up-drv-level-9 {
		bias-pull-up;
		drive-strength = <9>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_10: pcfg-pull-up-drv-level-10 {
		bias-pull-up;
		drive-strength = <10>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_11: pcfg-pull-up-drv-level-11 {
		bias-pull-up;
		drive-strength = <11>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_12: pcfg-pull-up-drv-level-12 {
		bias-pull-up;
		drive-strength = <12>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_13: pcfg-pull-up-drv-level-13 {
		bias-pull-up;
		drive-strength = <13>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_14: pcfg-pull-up-drv-level-14 {
		bias-pull-up;
		drive-strength = <14>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_15: pcfg-pull-up-drv-level-15 {
		bias-pull-up;
		drive-strength = <15>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_0: pcfg-pull-down-drv-level-0 {
		bias-pull-down;
		drive-strength = <0>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_1: pcfg-pull-down-drv-level-1 {
		bias-pull-down;
		drive-strength = <1>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_2: pcfg-pull-down-drv-level-2 {
		bias-pull-down;
		drive-strength = <2>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_3: pcfg-pull-down-drv-level-3 {
		bias-pull-down;
		drive-strength = <3>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_4: pcfg-pull-down-drv-level-4 {
		bias-pull-down;
		drive-strength = <4>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_5: pcfg-pull-down-drv-level-5 {
		bias-pull-down;
		drive-strength = <5>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_6: pcfg-pull-down-drv-level-6 {
		bias-pull-down;
		drive-strength = <6>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_7: pcfg-pull-down-drv-level-7 {
		bias-pull-down;
		drive-strength = <7>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_8: pcfg-pull-down-drv-level-8 {
		bias-pull-down;
		drive-strength = <8>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_9: pcfg-pull-down-drv-level-9 {
		bias-pull-down;
		drive-strength = <9>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_10: pcfg-pull-down-drv-level-10 {
		bias-pull-down;
		drive-strength = <10>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_11: pcfg-pull-down-drv-level-11 {
		bias-pull-down;
		drive-strength = <11>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_12: pcfg-pull-down-drv-level-12 {
		bias-pull-down;
		drive-strength = <12>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_13: pcfg-pull-down-drv-level-13 {
		bias-pull-down;
		drive-strength = <13>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_14: pcfg-pull-down-drv-level-14 {
		bias-pull-down;
		drive-strength = <14>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_15: pcfg-pull-down-drv-level-15 {
		bias-pull-down;
		drive-strength = <15>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_smt: pcfg-pull-up-smt {
		bias-pull-up;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_down_smt: pcfg-pull-down-smt {
		bias-pull-down;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_smt: pcfg-pull-none-smt {
		bias-disable;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_0_smt: pcfg-pull-none-drv-level-0-smt {
		bias-disable;
		drive-strength = <0>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	/omit-if-no-ref/
	pcfg_output_low: pcfg-output-low {
		output-low;
	};
};