// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

#include "dt-bindings/usb/pd.h"
#include "rk3588m.dtsi"
#include "rk3588-vehicle-v20.dtsi"
#include "rk3588-rk806-dual.dtsi"
/ {
	pcie20_avdd0v85: pcie20-avdd0v85 {
		compatible = "regulator-fixed";
		regulator-name = "pcie20_avdd0v85";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <850000>;
		vin-supply = <&vdd_0v85_s0>;
	};

	pcie20_avdd1v8: pcie20-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "pcie20_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	pcie30_avdd0v75: pcie30-avdd0v75 {
		compatible = "regulator-fixed";
		regulator-name = "pcie30_avdd0v75";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <750000>;
		regulator-max-microvolt = <750000>;
		vin-supply = <&avdd_0v75_s0>;
	};

	pcie30_avdd1v8: pcie30-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "pcie30_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;
		/*
		 * On the module itself this is one of these (depending
		 * on the actual card populated):
		 * - SDIO_RESET_L_WL_REG_ON
		 * - PDN (power down when low)
		 */
		post-power-on-delay-ms = <10>;
		reset-gpios = <&gpio1 RK_PB3 GPIO_ACTIVE_LOW>;
		status = "okay";
	};

	fan: pwm-fan {
		compatible = "pwm-fan";
		#cooling-cells = <2>;
		pwms = <&pwm8 0 50000 0>;
		cooling-levels = <0 50 100 150 200 255>;
		rockchip,temp-trips = <
			50000	1
			55000	2
			60000	3
			65000	4
			70000	5
		>;
	};

	vcc5v0_host: vcc5v0-host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio4 RK_PA3 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_host_en>;
	};

	wireless_bluetooth: wireless-bluetooth {
		compatible = "bluetooth-platdata";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		uart_rts_gpios = <&gpio4 RK_PA0 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart9m1_rtsn>, <&bt_reset_gpio>, <&bt_wake_gpio>, <&bt_irq_gpio>;
		pinctrl-1 = <&uart9_gpios>;
		BT,reset_gpio    = <&gpio1 RK_PB5 GPIO_ACTIVE_HIGH>;
		BT,wake_gpio     = <&gpio0 RK_PC7 GPIO_ACTIVE_HIGH>;
		BT,wake_host_irq = <&gpio0 RK_PC6 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless_wlan: wireless-wlan {
		compatible = "wlan-platdata";
		wifi_chip_type = "ap6398s";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_host_wake_irq>;
		WIFI,host_wake_irq = <&gpio0 RK_PB7 GPIO_ACTIVE_HIGH>;
		WIFI,poweren_gpio = <&gpio1 RK_PB3 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	car_rk3308_sound: car-rk3308-sound {
		status = "okay";
		compatible = "simple-audio-card";
		simple-audio-card,name = "rockchip,car-rk3308-sound";
		simple-audio-card,format = "i2s";
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,bitclock-master = <&codec_master>;
		simple-audio-card,frame-master = <&codec_master>;
		simple-audio-card,cpu {
			sound-dai = <&i2s0_8ch>;
			dai-tdm-slot-num = <8>;
			dai-tdm-slot-width = <32>;
		};
		codec_master: simple-audio-card,codec {
			sound-dai = <&spi_codec>;
		};
	};
};

&combphy0_ps {
	status = "okay";
};

&combphy1_ps {
	status = "okay";
};

&combphy2_psu {
	status = "okay";
};

&gmac0 {
	/* Use rgmii-rxid mode to disable rx delay inside Soc */
	phy-mode = "rgmii-rxid";
	clock_in_out = "output";
	snps,reset-gpio = <&gpio2 RK_PC5 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	/* Reset time is 20ms, 100ms for rtl8211f */
	snps,reset-delays-us = <0 20000 100000>;
	pinctrl-names = "default";
	pinctrl-0 = <&gmac0_miim
		     &gmac0_tx_bus2
		     &gmac0_rx_bus2
		     &gmac0_rgmii_clk
		     &gmac0_rgmii_bus
		     &phydisb>;
	tx_delay = <0x43>;
	//rx_delay = <0x3f>;
	phy-handle = <&rgmii_phy>;
	status = "okay";
};

&i2c4 {
	status = "okay";
	pinctrl-0 = <&i2c4m2_xfer>;
	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "hym8563";
		pinctrl-names = "default";
		pinctrl-0 = <&hym8563_int>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PC3 IRQ_TYPE_LEVEL_LOW>;
		wakeup-source;
	};

};

&i2s0_8ch {
	status = "okay";
	rockchip,tdm-fsync-half-frame;
	pinctrl-names = "default";
	pinctrl-0 = <&i2s0_lrck
		     &i2s0_sclk
		     &i2s0_sdi0
		     &i2s0_sdi1
		     &i2s0_sdo0
		     &i2s0_sdo1
		     &i2s0_sdo2
		     &i2s0_sdo3>;
};

&mdio0 {
	rgmii_phy: phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0x1>;
	};
};

&pcie2x1l0 {
	reset-gpios = <&gpio1 RK_PB4 GPIO_ACTIVE_HIGH>;
	rockchip,skip-scan-in-resume;
	pinctrl-names = "default";
	pinctrl-0 = <&wifi_enable_h>;
	status = "disabled";
};

&pcie2x1l2 {
	reset-gpios = <&gpio1 RK_PB4 GPIO_ACTIVE_HIGH>;
	status = "disabled";
};

&pinctrl {
	gmac0 {
		phydisb: phydisb {
			rockchip,pins = <0 RK_PA0 RK_FUNC_GPIO &pcfg_output_high>;
		};
	};

	hym8563 {
		hym8563_int: hym8563-int {
			rockchip,pins = <0 RK_PC3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sdio-pwrseq {
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <1 RK_PB3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb {
		vcc5v0_host_en: vcc5v0-host-en {
			rockchip,pins = <4 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};


	wireless-bluetooth {
		uart9_gpios: uart9-gpios {
			rockchip,pins = <4 RK_PA0 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_reset_gpio: bt-reset-gpio {
			rockchip,pins = <1 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_wake_gpio: bt-wake-gpio {
			rockchip,pins = <0 RK_PC7 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_irq_gpio: bt-irq-gpio {
			rockchip,pins = <0 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-wlan {
		wifi_host_wake_irq: wifi-host-wake-irq {
			rockchip,pins = <0 RK_PB7 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};

	rk3308 {
		rk3308_reset: rk3308-reset {
			rockchip,pins = <4 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm0 {
	pinctrl-0 = <&pwm0m2_pins>;
	status = "okay";
};

&pwm1 {
	pinctrl-0 = <&pwm1m2_pins>;
	status = "okay";
};

&pwm8 {
	pinctrl-0 = <&pwm8m1_pins>;
	status = "okay";
};

&sata0 {
	status = "okay";
};

&sdio {
	max-frequency = <150000000>;
	no-sd;
	no-mmc;
	bus-width = <4>;
	disable-wp;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdiom1_pins>;
	status = "okay";
};

&sdmmc {
	status = "disabled";
};

&spi4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&spi4m0_cs1 &spi4m0_pins>;

	spi_codec: spi-codec@1 {
		compatible ="rockchip,spi-codec";
		reg = <1>;
		spi-lsb-first;
		spi-max-frequency = <5000000>;
		#sound-dai-cells = <0>;
		pinctrl-names = "default";
		pinctrl-0 = <&rk3308_reset>;
		reset-gpios = <&gpio4 RK_PC6 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};
};

&uart9 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart9m1_xfer &uart9m1_ctsn>;
};

&u2phy0 {
	status = "okay";
};

&u2phy0_otg {
	rockchip,sel-pipe-phystatus;
	rockchip,dis-u2-susphy;
	status = "okay";
};

&u2phy1_otg {
	phy-supply = <&vcc5v0_host>;
};

&u2phy2_host {
	phy-supply = <&vcc5v0_host>;
};

&u2phy3_host {
	phy-supply = <&vcc5v0_host>;
};

&usbdp_phy0 {
	rockchip,dp-lane-mux = <2 3>;
	status = "okay";
};

&usbdp_phy0_dp {
	status = "okay";
};

&usbdp_phy0_u3 {
	status = "okay";
};

&usbdp_phy1 {
	maximum-speed = "high-speed";
	rockchip,dp-lane-mux = <3 2 1 0>;
	status = "okay";
};

&usbdp_phy1_dp {
	status = "okay";
};

&usbdp_phy1_u3 {
	status = "okay";
};

&usbdrd_dwc3_0 {
	dr_mode = "peripheral";
	maximum-speed = "high-speed";
	extcon = <&u2phy0>;
	phys = <&u2phy0_otg>;
	phy-names = "usb2-phy";
	snps,dis_u2_susphy_quirk;
	snps,usb2-lpm-disable;
	status = "okay";
};

&usbdrd_dwc3_1 {
	dr_mode = "host";
	maximum-speed = "high-speed";
	snps,dis_u2_susphy_quirk;
	snps,usb2-lpm-disable;
	status = "okay";
};
