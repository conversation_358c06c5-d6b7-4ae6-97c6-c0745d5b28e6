// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 */

/dts-v1/;
#include "rv1126bp.dtsi"
/ {
 ddr_timing: ddr_timing {
  compatible = "rockchip,ddr-timing";
  ddr2_speed_bin = <(0)>;
  ddr3_speed_bin = <(21)>;
  ddr4_speed_bin = <(12)>;
  pd_idle = <13>;
  sr_idle = <93>;
  sr_mc_gate_idle = <0>;
  srpd_lite_idle = <0>;
  standby_idle = <0>;

  auto_pd_dis_freq = <1066>;
  auto_sr_dis_freq = <800>;
  ddr2_dll_dis_freq = <300>;
  ddr3_dll_dis_freq = <300>;
  ddr4_dll_dis_freq = <625>;
  phy_dll_dis_freq = <400>;

  ddr2_odt_dis_freq = <100>;
  phy_ddr2_odt_dis_freq = <100>;
  ddr2_drv = <(0x1 << 1)>;
  ddr2_odt = <(0x1 << 6)>;
  phy_ddr2_ca_drv = <(0x15)>;
  phy_ddr2_ck_drv = <(0x13)>;
  phy_ddr2_dq_drv = <(0x15)>;
  phy_ddr2_odt = <(0x2)>;

  ddr3_odt_dis_freq = <333>;
  phy_ddr3_odt_dis_freq = <333>;
  ddr3_drv = <(0x1 << 1)>;
  ddr3_odt = <(0x1 << 6)>;
  phy_ddr3_ca_drv = <(0x13)>;
  phy_ddr3_ck_drv = <(0x14)>;
  phy_ddr3_dq_drv = <(0x17)>;
  phy_ddr3_odt = <(0x4)>;

  phy_lpddr2_odt_dis_freq = <333>;
  lpddr2_drv = <(0x2)>;
  phy_lpddr2_ca_drv = <(0x16)>;
  phy_lpddr2_ck_drv = <(0x14)>;
  phy_lpddr2_dq_drv = <(0x16)>;
  phy_lpddr2_odt = <(0x0)>;

  lpddr3_odt_dis_freq = <333>;
  phy_lpddr3_odt_dis_freq = <333>;
  lpddr3_drv = <(0x1)>;
  lpddr3_odt = <(0x2)>;
  phy_lpddr3_ca_drv = <(0x15)>;
  phy_lpddr3_ck_drv = <(0x16)>;
  phy_lpddr3_dq_drv = <(0x19)>;
  phy_lpddr3_odt = <(0x4)>;

  lpddr4_odt_dis_freq = <333>;
  phy_lpddr4_odt_dis_freq = <333>;
  lpddr4_drv = <(0x6 << 3)>;
  lpddr4_dq_odt = <(0x1)>;
  lpddr4_ca_odt = <(0x0)>;
  phy_lpddr4_ca_drv = <(0x13)>;
  phy_lpddr4_ck_cs_drv = <(0x15)>;
  phy_lpddr4_dq_drv = <(0x15)>;
  phy_lpddr4_odt = <(0x12)>;

  ddr4_odt_dis_freq = <625>;
  phy_ddr4_odt_dis_freq = <625>;
  ddr4_drv = <(0x0)>;
  ddr4_odt = <(0x2 << 8)>;
  phy_ddr4_ca_drv = <(0x13)>;
  phy_ddr4_ck_drv = <(0x15)>;
  phy_ddr4_dq_drv = <(0x15)>;
  phy_ddr4_odt = <(0x4)>;





  a0_a3_a3_cke1-a_de-skew = <7>;
  a1_ba1_null_cke0-b_de-skew = <7>;
  a2_a9_a9_a4-a_de-skew = <7>;
  a3_a15_null_a5-b_de-skew = <7>;
  a4_a6_a6_ck-a_de-skew = <7>;
  a5_a12_null_odt0-b_de-skew = <7>;
  a6_ba2_null_a0-a_de-skew = <7>;
  a7_a4_a4_odt0-a_de-skew = <7>;
  a8_a1_a1_cke0-a_de-skew = <7>;
  a9_a5_a5_a5-a_de-skew = <7>;
  a10_a8_a8_clkb-a_de-skew = <7>;
  a11_a7_a7_ca2-a_de-skew = <7>;
  a12_rasn_null_ca1-a_de-skew = <7>;
  a13_a13_null_ca3-a_de-skew = <7>;
  a14_a14_null_csb1-b_de-skew = <7>;
  a15_a10_null_ca0-b_de-skew = <7>;
  a16_a11_null_csb0-b_de-skew = <7>;
  a17_null_null_null_de-skew = <7>;
  ba0_csb1_csb1_csb0-a_de-skew = <7>;
  ba1_wen_null_cke1-b_de-skew = <7>;
  bg0_odt1_odt1_csb1-a_de-skew = <7>;
  bg1_a2_a2_odt1-a_de-skew = <7>;
  cke0_casb_null_ca1-b_de-skew = <7>;
  ck_ck_ck_ck-b_de-skew = <7>;
  ckb_ckb_ckb_ckb-b_de-skew = <7>;
  csb0_odt0_odt0_ca2-b_de-skew = <7>;
  odt0_csb0_csb0_ca4-b_de-skew = <7>;
  resetn_resetn_null-resetn_de-skew = <7>;
  actn_cke_cke_ca3-b_de-skew = <7>;
  cke1_null_null_null_de-skew = <7>;
  csb1_ba0_null_null_de-skew = <7>;
  odt1_a0_a0_odt1-b_de-skew = <7>;



  cs0_dm0_rx_de-skew = <7>;
  cs0_dq0_rx_de-skew = <7>;
  cs0_dq1_rx_de-skew = <7>;
  cs0_dq2_rx_de-skew = <7>;
  cs0_dq3_rx_de-skew = <7>;
  cs0_dq4_rx_de-skew = <7>;
  cs0_dq5_rx_de-skew = <7>;
  cs0_dq6_rx_de-skew = <7>;
  cs0_dq7_rx_de-skew = <7>;
  cs0_dqs0p_rx_de-skew = <14>;
  cs0_dqs0n_rx_de-skew = <14>;
  cs0_dm1_rx_de-skew = <7>;
  cs0_dq8_rx_de-skew = <7>;
  cs0_dq9_rx_de-skew = <7>;
  cs0_dq10_rx_de-skew = <7>;
  cs0_dq11_rx_de-skew = <7>;
  cs0_dq12_rx_de-skew = <7>;
  cs0_dq13_rx_de-skew = <7>;
  cs0_dq14_rx_de-skew = <7>;
  cs0_dq15_rx_de-skew = <7>;
  cs0_dqs1p_rx_de-skew = <14>;
  cs0_dqs1n_rx_de-skew = <14>;
  cs0_dm0_tx_de-skew = <7>;
  cs0_dq0_tx_de-skew = <7>;
  cs0_dq1_tx_de-skew = <7>;
  cs0_dq2_tx_de-skew = <7>;
  cs0_dq3_tx_de-skew = <7>;
  cs0_dq4_tx_de-skew = <7>;
  cs0_dq5_tx_de-skew = <7>;
  cs0_dq6_tx_de-skew = <7>;
  cs0_dq7_tx_de-skew = <7>;
  cs0_dqs0p_tx_de-skew = <7>;
  cs0_dqs0n_tx_de-skew = <7>;
  cs0_dm1_tx_de-skew = <7>;
  cs0_dq8_tx_de-skew = <7>;
  cs0_dq9_tx_de-skew = <7>;
  cs0_dq10_tx_de-skew = <7>;
  cs0_dq11_tx_de-skew = <7>;
  cs0_dq12_tx_de-skew = <7>;
  cs0_dq13_tx_de-skew = <7>;
  cs0_dq14_tx_de-skew = <7>;
  cs0_dq15_tx_de-skew = <7>;
  cs0_dqs1p_tx_de-skew = <7>;
  cs0_dqs1n_tx_de-skew = <7>;


  cs0_dm2_rx_de-skew = <7>;
  cs0_dq16_rx_de-skew = <7>;
  cs0_dq17_rx_de-skew = <7>;
  cs0_dq18_rx_de-skew = <7>;
  cs0_dq19_rx_de-skew = <7>;
  cs0_dq20_rx_de-skew = <7>;
  cs0_dq21_rx_de-skew = <7>;
  cs0_dq22_rx_de-skew = <7>;
  cs0_dq23_rx_de-skew = <7>;
  cs0_dqs2p_rx_de-skew = <14>;
  cs0_dqs2n_rx_de-skew = <14>;
  cs0_dm3_rx_de-skew = <7>;
  cs0_dq24_rx_de-skew = <7>;
  cs0_dq25_rx_de-skew = <7>;
  cs0_dq26_rx_de-skew = <7>;
  cs0_dq27_rx_de-skew = <7>;
  cs0_dq28_rx_de-skew = <7>;
  cs0_dq29_rx_de-skew = <7>;
  cs0_dq30_rx_de-skew = <7>;
  cs0_dq31_rx_de-skew = <7>;
  cs0_dqs3p_rx_de-skew = <14>;
  cs0_dqs3n_rx_de-skew = <14>;
  cs0_dm2_tx_de-skew = <7>;
  cs0_dq16_tx_de-skew = <7>;
  cs0_dq17_tx_de-skew = <7>;
  cs0_dq18_tx_de-skew = <7>;
  cs0_dq19_tx_de-skew = <7>;
  cs0_dq20_tx_de-skew = <7>;
  cs0_dq21_tx_de-skew = <7>;
  cs0_dq22_tx_de-skew = <7>;
  cs0_dq23_tx_de-skew = <7>;
  cs0_dqs2p_tx_de-skew = <7>;
  cs0_dqs2n_tx_de-skew = <7>;
  cs0_dm3_tx_de-skew = <7>;
  cs0_dq24_tx_de-skew = <7>;
  cs0_dq25_tx_de-skew = <7>;
  cs0_dq26_tx_de-skew = <7>;
  cs0_dq27_tx_de-skew = <7>;
  cs0_dq28_tx_de-skew = <7>;
  cs0_dq29_tx_de-skew = <7>;
  cs0_dq30_tx_de-skew = <7>;
  cs0_dq31_tx_de-skew = <7>;
  cs0_dqs3p_tx_de-skew = <7>;
  cs0_dqs3n_tx_de-skew = <7>;


  cs1_dm0_rx_de-skew = <7>;
  cs1_dq0_rx_de-skew = <7>;
  cs1_dq1_rx_de-skew = <7>;
  cs1_dq2_rx_de-skew = <7>;
  cs1_dq3_rx_de-skew = <7>;
  cs1_dq4_rx_de-skew = <7>;
  cs1_dq5_rx_de-skew = <7>;
  cs1_dq6_rx_de-skew = <7>;
  cs1_dq7_rx_de-skew = <7>;
  cs1_dqs0p_rx_de-skew = <14>;
  cs1_dqs0n_rx_de-skew = <14>;
  cs1_dm1_rx_de-skew = <7>;
  cs1_dq8_rx_de-skew = <7>;
  cs1_dq9_rx_de-skew = <7>;
  cs1_dq10_rx_de-skew = <7>;
  cs1_dq11_rx_de-skew = <7>;
  cs1_dq12_rx_de-skew = <7>;
  cs1_dq13_rx_de-skew = <7>;
  cs1_dq14_rx_de-skew = <7>;
  cs1_dq15_rx_de-skew = <7>;
  cs1_dqs1p_rx_de-skew = <14>;
  cs1_dqs1n_rx_de-skew = <14>;
  cs1_dm0_tx_de-skew = <7>;
  cs1_dq0_tx_de-skew = <7>;
  cs1_dq1_tx_de-skew = <7>;
  cs1_dq2_tx_de-skew = <7>;
  cs1_dq3_tx_de-skew = <7>;
  cs1_dq4_tx_de-skew = <7>;
  cs1_dq5_tx_de-skew = <7>;
  cs1_dq6_tx_de-skew = <7>;
  cs1_dq7_tx_de-skew = <7>;
  cs1_dqs0p_tx_de-skew = <7>;
  cs1_dqs0n_tx_de-skew = <7>;
  cs1_dm1_tx_de-skew = <7>;
  cs1_dq8_tx_de-skew = <7>;
  cs1_dq9_tx_de-skew = <7>;
  cs1_dq10_tx_de-skew = <7>;
  cs1_dq11_tx_de-skew = <7>;
  cs1_dq12_tx_de-skew = <7>;
  cs1_dq13_tx_de-skew = <7>;
  cs1_dq14_tx_de-skew = <7>;
  cs1_dq15_tx_de-skew = <7>;
  cs1_dqs1p_tx_de-skew = <7>;
  cs1_dqs1n_tx_de-skew = <7>;


  cs1_dm2_rx_de-skew = <7>;
  cs1_dq16_rx_de-skew = <7>;
  cs1_dq17_rx_de-skew = <7>;
  cs1_dq18_rx_de-skew = <7>;
  cs1_dq19_rx_de-skew = <7>;
  cs1_dq20_rx_de-skew = <7>;
  cs1_dq21_rx_de-skew = <7>;
  cs1_dq22_rx_de-skew = <7>;
  cs1_dq23_rx_de-skew = <7>;
  cs1_dqs2p_rx_de-skew = <14>;
  cs1_dqs2n_rx_de-skew = <14>;
  cs1_dm3_rx_de-skew = <7>;
  cs1_dq24_rx_de-skew = <7>;
  cs1_dq25_rx_de-skew = <7>;
  cs1_dq26_rx_de-skew = <7>;
  cs1_dq27_rx_de-skew = <7>;
  cs1_dq28_rx_de-skew = <7>;
  cs1_dq29_rx_de-skew = <7>;
  cs1_dq30_rx_de-skew = <7>;
  cs1_dq31_rx_de-skew = <7>;
  cs1_dqs3p_rx_de-skew = <14>;
  cs1_dqs3n_rx_de-skew = <14>;
  cs1_dm2_tx_de-skew = <7>;
  cs1_dq16_tx_de-skew = <7>;
  cs1_dq17_tx_de-skew = <7>;
  cs1_dq18_tx_de-skew = <7>;
  cs1_dq19_tx_de-skew = <7>;
  cs1_dq20_tx_de-skew = <7>;
  cs1_dq21_tx_de-skew = <7>;
  cs1_dq22_tx_de-skew = <7>;
  cs1_dq23_tx_de-skew = <7>;
  cs1_dqs2p_tx_de-skew = <7>;
  cs1_dqs2n_tx_de-skew = <7>;
  cs1_dm3_tx_de-skew = <7>;
  cs1_dq24_tx_de-skew = <7>;
  cs1_dq25_tx_de-skew = <7>;
  cs1_dq26_tx_de-skew = <7>;
  cs1_dq27_tx_de-skew = <7>;
  cs1_dq28_tx_de-skew = <7>;
  cs1_dq29_tx_de-skew = <7>;
  cs1_dq30_tx_de-skew = <7>;
  cs1_dq31_tx_de-skew = <7>;
  cs1_dqs3p_tx_de-skew = <7>;
  cs1_dqs3n_tx_de-skew = <7>;
 };
};
// # 17 "arch/arm/boot/dts/rv1126.dtsi" 2

/ {
 #address-cells = <1>;
 #size-cells = <1>;

 compatible = "rockchip,rv1126";

 interrupt-parent = <&gic>;

 aliases {
  i2c0 = &i2c0;
  i2c1 = &i2c1;
  i2c2 = &i2c2;
  i2c3 = &i2c3;
  i2c4 = &i2c4;
  i2c5 = &i2c5;
  mmc0 = &emmc;
  mmc1 = &sdio;
  mmc2 = &sdmmc;
  serial0 = &uart0;
  serial1 = &uart1;
  serial2 = &uart2;
  serial3 = &uart3;
  serial4 = &uart4;
  serial5 = &uart5;
  spi0 = &spi0;
  spi1 = &spi1;
  dphy0 = &csi_dphy0;
  dphy1 = &csi_dphy1;
 };

 cpus {
  #address-cells = <1>;
  #size-cells = <0>;

  cpu0: cpu@f00 {
   device_type = "cpu";
   compatible = "arm,cortex-a7";
   reg = <0xf00>;
   enable-method = "psci";
   clocks = <&cru 5>;
   operating-points-v2 = <&cpu0_opp_table>;
   dynamic-power-coefficient = <60>;
   #cooling-cells = <2>;
   cpu-idle-states = <&CPU_SLEEP>;
  };

  cpu1: cpu@f01 {
   device_type = "cpu";
   compatible = "arm,cortex-a7";
   reg = <0xf01>;
   enable-method = "psci";
   clocks = <&cru 5>;
   operating-points-v2 = <&cpu0_opp_table>;
   dynamic-power-coefficient = <60>;
   cpu-idle-states = <&CPU_SLEEP>;
  };

  cpu2: cpu@f02 {
   device_type = "cpu";
   compatible = "arm,cortex-a7";
   reg = <0xf02>;
   enable-method = "psci";
   clocks = <&cru 5>;
   operating-points-v2 = <&cpu0_opp_table>;
   dynamic-power-coefficient = <60>;
   cpu-idle-states = <&CPU_SLEEP>;
  };

  cpu3: cpu@f03 {
   device_type = "cpu";
   compatible = "arm,cortex-a7";
   reg = <0xf03>;
   enable-method = "psci";
   clocks = <&cru 5>;
   operating-points-v2 = <&cpu0_opp_table>;
   dynamic-power-coefficient = <60>;
   cpu-idle-states = <&CPU_SLEEP>;
  };

  idle-states {
   entry-method = "psci";

   CPU_SLEEP: cpu-sleep {
    compatible = "arm,idle-state";
    local-timer-stop;
    arm,psci-suspend-param = <0x0010000>;
    entry-latency-us = <120>;
    exit-latency-us = <250>;
    min-residency-us = <900>;
   };
  };

 };

 cpu0_opp_table: cpu0-opp-table {
  compatible = "operating-points-v2";
  opp-shared;

  nvmem-cells = <&cpu_leakage>, <&cpu_performance>;
  nvmem-cell-names = "leakage", "performance";

  rockchip,reboot-freq = <816000>;

  rockchip,temp-freq-table = <
   100000 1296000
  >;

  clocks = <&cru 1>;
  rockchip,bin-scaling-sel = <
   0 5
   1 18
  >;
  rockchip,bin-voltage-sel = <
   1 0
  >;
  rockchip,pvtm-voltage-sel = <
   0 106000 1
   106001 112000 2
   112001 999999 3
  >;
  rockchip,pvtm-freq = <408000>;
  rockchip,pvtm-volt = <800000>;
  rockchip,pvtm-ch = <0 0>;
  rockchip,pvtm-sample-time = <1000>;
  rockchip,pvtm-number = <10>;
  rockchip,pvtm-error = <1000>;
  rockchip,pvtm-ref-temp = <37>;
  rockchip,pvtm-temp-prop = <(-40) 13>;
  rockchip,pvtm-thermal-zone = "cpu-thermal";

  opp-408000000 {
   opp-hz = /bits/ 64 <408000000>;
   opp-microvolt = <725000 725000 1000000>;
   opp-microvolt-L0 = <725000 725000 1000000>;
   clock-latency-ns = <40000>;
  };
  opp-600000000 {
   opp-hz = /bits/ 64 <600000000>;
   opp-microvolt = <725000 725000 1000000>;
   opp-microvolt-L0 = <725000 725000 1000000>;
   clock-latency-ns = <40000>;
  };
  opp-816000000 {
   opp-hz = /bits/ 64 <816000000>;
   opp-microvolt = <725000 725000 1000000>;
   opp-microvolt-L0 = <750000 750000 1000000>;
   clock-latency-ns = <40000>;
   opp-suspend;
  };
  opp-1008000000 {
   opp-hz = /bits/ 64 <1008000000>;
   opp-microvolt = <775000 775000 1000000>;
   opp-microvolt-L0 = <800000 800000 1000000>;
   opp-microvolt-L1 = <775000 775000 1000000>;
   opp-microvolt-L2 = <775000 775000 1000000>;
   opp-microvolt-L3 = <750000 750000 1000000>;
   clock-latency-ns = <40000>;
  };
  opp-1200000000 {
   opp-hz = /bits/ 64 <1200000000>;
   opp-microvolt = <850000 850000 1000000>;
   opp-microvolt-L0 = <875000 875000 1000000>;
   opp-microvolt-L1 = <850000 850000 1000000>;
   opp-microvolt-L2 = <850000 850000 1000000>;
   opp-microvolt-L3 = <825000 825000 1000000>;
   clock-latency-ns = <40000>;
  };
  opp-1296000000 {
   opp-hz = /bits/ 64 <1296000000>;
   opp-microvolt = <875000 875000 1000000>;
   opp-microvolt-L1 = <875000 875000 1000000>;
   opp-microvolt-L2 = <875000 875000 1000000>;
   opp-microvolt-L3 = <850000 850000 1000000>;
   clock-latency-ns = <40000>;
  };
  opp-1416000000 {
   opp-hz = /bits/ 64 <1416000000>;
   opp-microvolt = <925000 925000 1000000>;
   opp-microvolt-L1 = <925000 925000 1000000>;
   opp-microvolt-L2 = <925000 925000 1000000>;
   opp-microvolt-L3 = <900000 900000 1000000>;
   clock-latency-ns = <40000>;
  };
  opp-1512000000 {
   opp-hz = /bits/ 64 <1512000000>;
   opp-microvolt = <975000 975000 1000000>;
   opp-microvolt-L1 = <975000 975000 1000000>;
   opp-microvolt-L2 = <950000 950000 1000000>;
   opp-microvolt-L3 = <925000 925000 1000000>;
   clock-latency-ns = <40000>;
  };
 };

 cpuinfo {
  compatible = "rockchip,cpuinfo";
  nvmem-cells = <&otp_id>, <&otp_cpu_code>;
  nvmem-cell-names = "id", "cpu-code";
 };

 arm-pmu {
  compatible = "arm,cortex-a7-pmu";
  interrupts = <0 123 4>,
        <0 124 4>,
        <0 125 4>,
        <0 126 4>;
  interrupt-affinity = <&cpu0>, <&cpu1>, <&cpu2>, <&cpu3>;
 };

 bus_soc: bus-soc {
  compatible = "rockchip,rv1126-bus";
  rockchip,busfreq-policy = "smc";
  soc-bus0 {
   bus-id = <0>;
   cfg-val = <0x00300020>;
   enable-msk = <0x7144>;
   status = "okay";
  };
  soc-bus1 {
   bus-id = <1>;
   cfg-val = <0x00300020>;
   enable-msk = <0x70ff>;
   status = "disabled";
  };
  soc-bus2 {
   bus-id = <2>;
   cfg-val = <0x00300020>;
   enable-msk = <0x70ff>;
   status = "disabled";
  };
  soc-bus3 {
   bus-id = <3>;
   cfg-val = <0x00300020>;
   enable-msk = <0x70ff>;
   status = "disabled";
  };
  soc-bus4 {
   bus-id = <4>;
   cfg-val = <0x00300020>;
   enable-msk = <0x7011>;
   status = "disabled";
  };
  soc-bus5 {
   bus-id = <5>;
   cfg-val = <0x00300020>;
   enable-msk = <0x7011>;
   status = "disabled";
  };
  soc-bus6 {
   bus-id = <6>;
   cfg-val = <0x00300020>;
   enable-msk = <0x7011>;
   status = "disabled";
  };
  soc-bus7 {
   bus-id = <7>;
   cfg-val = <0x00300020>;
   enable-msk = <0x0>;
   status = "disabled";
  };
  soc-bus8 {
   bus-id = <8>;
   cfg-val = <0x00300020>;
   enable-msk = <0x0>;
   status = "disabled";
  };
  soc-bus9 {
   bus-id = <9>;
   cfg-val = <0x00300020>;
   enable-msk = <0x0>;
   status = "disabled";
  };
  soc-bus10 {
   bus-id = <10>;
   cfg-val = <0x00300020>;
   enable-msk = <0x0>;
   status = "disabled";
  };
  soc-bus11 {
   bus-id = <11>;
   cfg-val = <0x00300020>;
   enable-msk = <0x7000>;
   status = "disabled";
  };
 };

 display_subsystem: display-subsystem {
  compatible = "rockchip,display-subsystem";
  ports = <&vop_out>;
  status = "disabled";
  logo-memory-region = <&drm_logo>;

  route {
   route_dsi: route-dsi {
    status = "disabled";
    logo,uboot = "logo.bmp";
    logo,kernel = "logo_kernel.bmp";
    logo,mode = "center";
    charge_logo,mode = "center";
    connect = <&vop_out_dsi>;
   };

   route_rgb: route-rgb {
    status = "disabled";
    logo,uboot = "logo.bmp";
    logo,kernel = "logo_kernel.bmp";
    logo,mode = "center";
    charge_logo,mode = "center";
    connect = <&vop_out_rgb>;
   };
  };
 };

 fiq_debugger: fiq-debugger {
  compatible = "rockchip,fiq-debugger";
  rockchip,serial-id = <2>;
  rockchip,wake-irq = <0>;
  rockchip,irq-mode-enable = <0>;
  rockchip,baudrate = <1500000>;
  interrupts = <0 127 4>;
  status = "disabled";
 };

 firmware {
  optee: optee {
   compatible = "linaro,optee-tz";
   method = "smc";
   status = "disabled";
  };
 };

 mpp_srv: mpp-srv {
  compatible = "rockchip,mpp-service";
  rockchip,taskqueue-count = <4>;
  rockchip,resetgroup-count = <4>;
  status = "disabled";
 };

 psci {
  compatible = "arm,psci-1.0";
  method = "smc";
 };

 reserved-memory {
  #address-cells = <1>;
  #size-cells = <1>;
  ranges;

  linux,cma {
   compatible = "shared-dma-pool";
   inactive;
   reusable;
   size = <0x4000000>;
   linux,cma-default;
  };

  drm_logo: drm-logo@00000000 {
   compatible = "rockchip,drm-logo";
   reg = <0x0 0x0>;
  };

  isp_reserved: isp {
   compatible = "shared-dma-pool";
   inactive;
   reusable;
   size = <0x10000000>;
  };

  ramoops: ramoops@8000000 {
   compatible = "ramoops";
   reg = <0x8000000 0x100000>;
   record-size = <0x20000>;
   console-size = <0x40000>;
   ftrace-size = <0x00000>;
   pmsg-size = <0x40000>;
   status = "disabled";
  };
 };

 rkcif_dvp: rkcif_dvp {
  compatible = "rockchip,rkcif-dvp";
  rockchip,hw = <&rkcif>;

  memory-region = <&isp_reserved>;
  status = "disabled";
 };

 rkcif_dvp_sditf: rkcif_dvp_sditf {
  compatible = "rockchip,rkcif-sditf";
  rockchip,cif = <&rkcif_dvp>;
  status = "disabled";
 };

 rkcif_lite_mipi_lvds: rkcif_lite_mipi_lvds {
  compatible = "rockchip,rkcif-mipi-lvds";
  rockchip,hw = <&rkcif_lite>;
  iommus = <&rkcif_lite_mmu>;
  status = "disabled";
 };

 rkcif_lite_sditf: rkcif_lite_sditf {
  compatible = "rockchip,rkcif-sditf";
  rockchip,cif = <&rkcif_lite_mipi_lvds>;
  status = "disabled";
 };

 rkcif_mipi_lvds: rkcif_mipi_lvds {
  compatible = "rockchip,rkcif-mipi-lvds";
  rockchip,hw = <&rkcif>;

  memory-region = <&isp_reserved>;
  status = "disabled";
 };

 rkcif_mipi_lvds_sditf: rkcif_mipi_lvds_sditf {
  compatible = "rockchip,rkcif-sditf";
  rockchip,cif = <&rkcif_mipi_lvds>;
  status = "disabled";
 };

 rockchip_suspend: rockchip-suspend {
  compatible = "rockchip,pm-rv1126";
  status = "disabled";
  rockchip,sleep-debug-en = <0>;
  rockchip,sleep-mode-config = <
   (0
   | (1 << (1))
   | (1 << (9))
   | (1 << (10))
   | (1 << (17))
   )
  >;
  rockchip,wakeup-config = <
   (0
   | (1 << (4))
   )
  >;
 };

 rockchip_system_monitor: rockchip-system-monitor {
  compatible = "rockchip,system-monitor";
 };

 thermal_zones: thermal-zones {
  cpu_thermal: cpu-thermal {
   polling-delay-passive = <20>;
   polling-delay = <1000>;
   sustainable-power = <875>;
   k_pu = <75>;
   k_po = <175>;
   k_i = <0>;

   thermal-sensors = <&cpu_tsadc 0>;

   trips {
    threshold: trip-point-0 {

     temperature = <75000>;

     hysteresis = <2000>;
     type = "passive";
    };
    target: trip-point-1 {

     temperature = <85000>;

     hysteresis = <2000>;
     type = "passive";
    };
    soc_crit: soc-crit {

     temperature = <115000>;

     hysteresis = <2000>;
     type = "critical";
    };
   };

   cooling-maps {
    map0 {
     trip = <&target>;
     cooling-device =
      <&cpu0 (~0) (~0)>;
     contribution = <1024>;
    };
    map1 {
     trip = <&target>;
     cooling-device =
      <&npu (~0) (~0)>;
     contribution = <1024>;
    };
    map2 {
     trip = <&target>;
     cooling-device =
      <&rkvenc (~0) (~0)>;
     contribution = <1060>;
    };
   };
  };

  npu_thermal: npu-thermal {
   polling-delay-passive = <20>;
   polling-delay = <1000>;
   sustainable-power = <977>;

   thermal-sensors = <&npu_tsadc 0>;
  };
 };

 timer {
  compatible = "arm,armv7-timer";
  interrupts = <1 13 ((((1 << (4)) - 1) << 8) | 4)>,
        <1 14 ((((1 << (4)) - 1) << 8) | 4)>,
        <1 11 ((((1 << (4)) - 1) << 8) | 4)>,
        <1 10 ((((1 << (4)) - 1) << 8) | 4)>;
  clock-frequency = <24000000>;
 };

 xin24m: oscillator {
  compatible = "fixed-clock";
  clock-frequency = <24000000>;
  clock-output-names = "xin24m";
  #clock-cells = <0>;
 };

 dummy_cpll: dummy_cpll {
  compatible = "fixed-clock";
  clock-frequency = <0>;
  clock-output-names = "dummy_cpll";
  #clock-cells = <0>;
 };

 gmac_clkin_m0: external-gmac-clockm0 {
  compatible = "fixed-clock";
  clock-frequency = <125000000>;
  clock-output-names = "clk_gmac_rgmii_clkin_m0";
  #clock-cells = <0>;
 };

 gmac_clkini_m1: external-gmac-clockm1 {
  compatible = "fixed-clock";
  clock-frequency = <125000000>;
  clock-output-names = "clk_gmac_rgmii_clkin_m1";
  #clock-cells = <0>;
 };

 grf: syscon@fe000000 {
  compatible = "rockchip,rv1126-grf", "syscon", "simple-mfd";
  reg = <0xfe000000 0x20000>;

  rgb: rgb {
   compatible = "rockchip,rv1126-rgb";
   status = "disabled";

   ports {
    #address-cells = <1>;
    #size-cells = <0>;

    port@0 {
     reg = <0>;
     #address-cells = <1>;
     #size-cells = <0>;

     rgb_in_vop: endpoint@0 {
      reg = <0>;
      remote-endpoint = <&vop_out_rgb>;
     };
    };

   };
  };
 };

 pmugrf: syscon@fe020000 {
  compatible = "rockchip,rv1126-pmugrf", "syscon", "simple-mfd";
  reg = <0xfe020000 0x1000>;

  pmu_io_domains: io-domains {
   compatible = "rockchip,rv1126-pmu-io-voltage-domain";
  };

  reboot-mode {
   compatible = "syscon-reboot-mode";
   offset = <0x200>;
   mode-bootloader = <(0x5242C300 + 1)>;
   mode-charge = <(0x5242C300 + 11)>;
   mode-fastboot = <(0x5242C300 + 9)>;
   mode-loader = <(0x5242C300 + 1)>;
   mode-normal = <(0x5242C300 + 0)>;
   mode-recovery = <(0x5242C300 + 3)>;
   mode-ums = <(0x5242C300 + 12)>;
   mode-panic = <(0x5242C300 + 7)>;
   mode-watchdog = <(0x5242C300 + 8)>;
   mode-dfu = <(0x5242C300 + 13)>;
  };
 };

 qos_usb_host: qos@fe810000 {
  compatible = "syscon";
  reg = <0xfe810000 0x20>;
 };

 qos_usb_otg: qos@fe810080 {
  compatible = "syscon";
  reg = <0xfe810080 0x20>;
 };

 qos_npu: qos@fe850000 {
  compatible = "syscon";
  reg = <0xfe850000 0x20>;
 };

 qos_emmc: qos@fe860000 {
  compatible = "syscon";
  reg = <0xfe860000 0x20>;
 };

 qos_nandc: qos@fe860080 {
  compatible = "syscon";
  reg = <0xfe860080 0x20>;
 };

 qos_sfc: qos@fe860200 {
  compatible = "syscon";
  reg = <0xfe860200 0x20>;
 };

 qos_sdio: qos@fe86c000 {
  compatible = "syscon";
  reg = <0xfe86c000 0x20>;
 };

 qos_vepu_rd0: qos@fe870000 {
  compatible = "syscon";
  reg = <0xfe870000 0x20>;
 };

 qos_vepu_rd1: qos@fe870080 {
  compatible = "syscon";
  reg = <0xfe870080 0x20>;
 };

 qos_vepu_wr: qos@fe870100 {
  compatible = "syscon";
  reg = <0xfe870100 0x20>;
 };

 qos_ispp_m0: qos@fe880000 {
  compatible = "syscon";
  reg = <0xfe880000 0x20>;
 };

 qos_ispp_m1: qos@fe880080 {
  compatible = "syscon";
  reg = <0xfe880080 0x20>;
 };

 qos_isp: qos@fe890000 {
  compatible = "syscon";
  reg = <0xfe890000 0x20>;
 };

 qos_cif_lite: qos@fe890080 {
  compatible = "syscon";
  reg = <0xfe890080 0x20>;
 };

 qos_cif: qos@fe890100 {
  compatible = "syscon";
  reg = <0xfe890100 0x20>;
 };

 qos_iep: qos@fe8a0000 {
  compatible = "syscon";
  reg = <0xfe8a0000 0x20>;
 };

 qos_rga_rd: qos@fe8a0080 {
  compatible = "syscon";
  reg = <0xfe8a0080 0x20>;
 };

 qos_rga_wr: qos@fe8a0100 {
  compatible = "syscon";
  reg = <0xfe8a0100 0x20>;
 };

 qos_vop: qos@fe8a0180 {
  compatible = "syscon";
  reg = <0xfe8a0180 0x20>;
 };

 qos_vdpu: qos@fe8b0000 {
  compatible = "syscon";
  reg = <0xfe8b0000 0x20>;
 };

 qos_jpeg: qos@fe8c0000 {
  compatible = "syscon";
  reg = <0xfe8c0000 0x20>;
 };

 qos_crypto: qos@fe8d0000 {
  compatible = "syscon";
  reg = <0xfe8d0000 0x20>;
 };

 gic: interrupt-controller@feff0000 {
  compatible = "arm,gic-400";
  interrupt-controller;
  #interrupt-cells = <3>;
  #address-cells = <0>;

  reg = <0xfeff1000 0x1000>,
        <0xfeff2000 0x2000>,
        <0xfeff4000 0x2000>,
        <0xfeff6000 0x2000>;
  interrupts = <1 9 ((((1 << (4)) - 1) << 8) | 4)>;
 };

 arm-debug@ff010000 {
  compatible = "rockchip,debug";
  reg = <0xff010000 0x1000>,
        <0xff012000 0x1000>,
        <0xff014000 0x1000>,
        <0xff016000 0x1000>;
 };

 pvtm@ff040000 {
  compatible = "rockchip,rv1126-cpu-pvtm";
  reg = <0xff040000 0x100>;
  #address-cells = <1>;
  #size-cells = <0>;

  pvtm@0 {
   reg = <0>;
   clocks = <&cru 8>, <&cru 245>;
   clock-names = "clk", "pclk";
   resets = <&cru 236>, <&cru 235>;
   reset-names = "rst", "rst-p";
  };
 };

 pmu: power-management@ff3e0000 {
  compatible = "rockchip,rv1126-pmu", "syscon", "simple-mfd";
  reg = <0xff3e0000 0x1000>;

  power: power-controller {
   compatible = "rockchip,rv1126-power-controller";
   #power-domain-cells = <1>;
   #address-cells = <1>;
   #size-cells = <0>;
   status = "okay";


   pd_npu@7 {
    reg = <7>;
    clocks = <&cru 195>,
      <&cru 240>,
      <&cru 281>,
      <&cru 144>;
    pm_qos = <&qos_npu>;
   };

   pd_vepu@8 {
    reg = <8>;
    clocks = <&cru 168>,
      <&cru 211>,
      <&cru 86>;
    pm_qos = <&qos_vepu_rd0>,
      <&qos_vepu_rd1>,
      <&qos_vepu_wr>;
   };

   pd_crypto@13 {
    reg = <13>;
    clocks = <&cru 166>,
      <&cru 203>,
      <&cru 56>,
      <&cru 57>;
    pm_qos = <&qos_crypto>;
   };
   pd_vi@9 {
    reg = <9>;
    clocks = <&cru 180>,
      <&cru 221>,
      <&cru 95>,
      <&cru 181>,
      <&cru 222>,
      <&cru 155>,
      <&cru 99>,
      <&cru 103>,
      <&cru 276>,
      <&cru 182>,
      <&cru 223>,
      <&cru 156>;
    pm_qos = <&qos_isp>,
      <&qos_cif_lite>,
      <&qos_cif>;
   };
   pd_vo@10 {
    reg = <10>;
    clocks = <&cru 174>,
      <&cru 217>,
      <&cru 90>,
      <&cru 175>,
      <&cru 218>,
      <&cru 154>,
      <&cru 274>,
      <&cru 176>,
      <&cru 219>,
      <&cru 91>;
    pm_qos = <&qos_rga_rd>, <&qos_rga_wr>,
      <&qos_vop>, <&qos_iep>;
   };
   pd_ispp@11 {
    reg = <11>;
    clocks = <&cru 186>,
      <&cru 225>,
      <&cru 107>;
    pm_qos = <&qos_ispp_m0>,
      <&qos_ispp_m1>;
   };
   pd_vdpu@12 {
    reg = <12>;
    clocks = <&cru 171>,
      <&cru 214>,
      <&cru 87>,
      <&cru 88>,
      <&cru 89>,
      <&cru 172>,
      <&cru 215>;
    pm_qos = <&qos_vdpu>,
      <&qos_jpeg>;
   };
   pd_nvm@15 {
    reg = <15>;
    clocks = <&cru 232>,
      <&cru 114>,
      <&cru 233>,
      <&cru 117>,
      <&cru 234>,
      <&cru 235>,
      <&cru 118>;
    pm_qos = <&qos_emmc>,
      <&qos_nandc>,
      <&qos_sfc>;
   };
   pd_sdio@16 {
    reg = <16>;
    clocks = <&cru 230>,
      <&cru 111>;
    pm_qos = <&qos_sdio>;
   };
   pd_usb@17 {
    reg = <17>;
    clocks = <&cru 237>,
      <&cru 238>,
      <&cru 119>,
      <&cru 189>,
      <&cru 120>;
    pm_qos = <&qos_usb_host>,
      <&qos_usb_otg>;
   };
  };
 };

 i2c0: i2c@ff3f0000 {
  compatible = "rockchip,rv1126-i2c", "rockchip,rk3399-i2c";
  reg = <0xff3f0000 0x1000>;
  interrupts = <0 4 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  clocks = <&pmucru 12>, <&pmucru 33>;
  clock-names = "i2c", "pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&i2c0_xfer>;
  status = "disabled";
 };

 i2c2: i2c@ff400000 {
  compatible = "rockchip,rv1126-i2c", "rockchip,rk3399-i2c";
  reg = <0xff400000 0x1000>;
  interrupts = <0 6 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  rockchip,grf = <&pmugrf>;
  clocks = <&pmucru 13>, <&pmucru 34>;
  clock-names = "i2c", "pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&i2c2_xfer>;
  status = "disabled";
 };

 amba {
  compatible = "simple-bus";
  #address-cells = <1>;
  #size-cells = <1>;
  ranges;

  dmac: dma-controller@ff4e0000 {
   compatible = "arm,pl330", "arm,primecell";
   reg = <0xff4e0000 0x4000>;
   interrupts = <0 1 4>,
         <0 2 4>;
   #dma-cells = <1>;
   clocks = <&cru 161>;
   clock-names = "apb_pclk";
   arm,pl330-periph-burst;
  };
 };

 uart1: serial@ff410000 {
  compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
  reg = <0xff410000 0x100>;
  interrupts = <0 13 4>;
  reg-shift = <2>;
  reg-io-width = <4>;
  dmas = <&dmac 7>, <&dmac 6>;
  clock-frequency = <24000000>;
  clocks = <&pmucru 11>, <&pmucru 32>;
  clock-names = "baudclk", "apb_pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&uart1m0_xfer &uart1m0_ctsn &uart1m0_rtsn>;
  status = "disabled";
 };

 pwm0: pwm@ff430000 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff430000 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm0m0_pins>;
  clocks = <&pmucru 15>, <&pmucru 35>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm1: pwm@ff430010 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff430010 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm1m0_pins>;
  clocks = <&pmucru 15>, <&pmucru 35>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm2: pwm@ff430020 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff430020 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm2m0_pins>;
  clocks = <&pmucru 15>, <&pmucru 35>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm3: pwm@ff430030 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff430030 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm3m0_pins>;
  clocks = <&pmucru 15>, <&pmucru 35>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm4: pwm@ff440000 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff440000 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm4m0_pins>;
  clocks = <&pmucru 17>, <&pmucru 36>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm5: pwm@ff440010 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff440010 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm5m0_pins>;
  clocks = <&pmucru 17>, <&pmucru 36>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm6: pwm@ff440020 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff440020 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm6m0_pins>;
  clocks = <&pmucru 17>, <&pmucru 36>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm7: pwm@ff440030 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff440030 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm7m0_pins>;
  clocks = <&pmucru 17>, <&pmucru 36>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 spi0: spi@ff450000 {
  compatible = "rockchip,rv1126-spi", "rockchip,rk3066-spi";
  reg = <0xff450000 0x1000>;
  interrupts = <0 10 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  clocks = <&pmucru 18>, <&pmucru 37>;
  clock-names = "spiclk", "apb_pclk";
  dmas = <&dmac 1>, <&dmac 0>;
  dma-names = "tx", "rx";
  pinctrl-names = "default", "high_speed";
  pinctrl-0 = <&spi0m0_cs0 &spi0m0_cs1 &spi0m0_pins>;
  pinctrl-1 = <&spi0m0_cs0 &spi0m0_cs1 &spi0m0_pins_hs>;
  status = "disabled";
 };

 pvtm@ff470000 {
  compatible = "rockchip,rv1126-pmu-pvtm";
  reg = <0xff470000 0x100>;
  #address-cells = <1>;
  #size-cells = <0>;

  pvtm@2 {
   reg = <2>;
   clocks = <&pmucru 20>, <&pmucru 44>;
   clock-names = "clk", "pclk";
   resets = <&pmucru 24>,
     <&pmucru 25>;
   reset-names = "rst", "rst-p";
  };
 };

 pmucru: clock-controller@ff480000 {
  compatible = "rockchip,rv1126-pmucru";
  reg = <0xff480000 0x1000>;
  rockchip,pmugrf = <&pmugrf>;
  #clock-cells = <1>;
  #reset-cells = <1>;
 };

 cru: clock-controller@ff490000 {
  compatible = "rockchip,rv1126-cru";
  reg = <0xff490000 0x1000>;
  rockchip,grf = <&grf>;
  #clock-cells = <1>;
  #reset-cells = <1>;

  assigned-clocks =
   <&pmucru 3>, <&pmucru 1>,
   <&pmucru 30>, <&cru 3>,
   <&cru 4>, <&cru 5>,
   <&cru 160>, <&cru 236>,
   <&cru 246>, <&cru 187>,
   <&cru 226>, <&cru 204>,
   <&cru 200>;
  assigned-clock-rates =
   <32768>, <1200000000>,
   <100000000>, <500000000>,
   <1400000000>, <600000000>,
   <500000000>, <200000000>,
   <100000000>, <300000000>,
   <200000000>, <150000000>,
   <200000000>;
  assigned-clock-parents =
   <&pmucru 2>;
 };

 csi_dphy0: csi-dphy@ff4b0000 {
  compatible = "rockchip,rv1126-csi-dphy";
  reg = <0xff4b0000 0x8000>;
  clocks = <&cru 290>;
  clock-names = "pclk";
  rockchip,grf = <&grf>;
  status = "disabled";
 };

 csi_dphy1: csi-dphy@ff4b8000 {
  compatible = "rockchip,rv1126-csi-dphy";
  reg = <0xff4b8000 0x8000>;
  clocks = <&cru 291>;
  clock-names = "pclk";
  rockchip,grf = <&grf>;
  status = "disabled";
 };

 u2phy0: usb2-phy@ff4c0000 {
  compatible = "rockchip,rv1126-usb2phy";
  reg = <0xff4c0000 0x8000>;
  rockchip,grf = <&grf>;
  clocks = <&pmucru 23>, <&cru 293>;
  clock-names = "phyclk", "pclk";
  resets = <&cru 184>, <&cru 182>;
  reset-names = "u2phy", "u2phy-apb";
  #clock-cells = <0>;
  status = "disabled";

  u2phy_otg: otg-port {
   #phy-cells = <0>;
   interrupts = <0 115 4>,
         <0 116 4>,
         <0 117 4>,
         <0 120 4>;
   interrupt-names = "otg-bvalid", "otg-id",
       "linestate", "disconnect";
   status = "disabled";
  };
 };

 u2phy1: usb2-phy@ff4c8000 {
  compatible = "rockchip,rv1126-usb2phy";
  reg = <0xff4c8000 0x8000>;
  rockchip,grf = <&grf>;
  clocks = <&pmucru 24>, <&cru 292>;
  clock-names = "phyclk", "pclk";
  assigned-clocks = <&cru 6>;
  assigned-clock-parents = <&u2phy1>;
  resets = <&cru 185>, <&cru 183>;
  reset-names = "u2phy", "u2phy-apb";
  #clock-cells = <0>;
  clock-output-names = "usb480m_phy";
  status = "disabled";

  u2phy_host: host-port {
   #phy-cells = <0>;
   interrupts = <0 118 4>,
         <0 119 4>;
   interrupt-names = "linestate", "disconnect";
   status = "disabled";
  };
 };

 mipi_dphy: mipi-dphy@ff4d0000 {
  compatible = "rockchip,rv1126-mipi-dphy", "rockchip,rk1808-mipi-dphy";
  reg = <0xff4d0000 0x500>;
  assigned-clocks = <&pmucru 26>;
  assigned-clock-rates = <24000000>;
  clocks = <&pmucru 26>, <&cru 289>;
  clock-names = "ref", "pclk";
  clock-output-names = "mipi_dphy_pll";
  #clock-cells = <0>;
  resets = <&cru 230>;
  reset-names = "apb";
  #phy-cells = <0>;
  rockchip,grf = <&grf>;
  status = "disabled";
 };

 rng: rng@ff500400 {
  compatible = "rockchip,cryptov2-rng";
  reg = <0xff500400 0x80>;
  clocks = <&cru 203>;
  clock-names = "hclk_crypto";
  power-domains = <&power 13>;
  resets = <&cru 92>;
  reset-names = "reset";
  status = "disabled";
 };

 crypto: crypto@ff500000 {
  compatible = "rockchip,rv1126-crypto";
  reg = <0xff500000 0x400>, <0xff500480 0x3B80>;
  interrupts = <0 3 4>;
  clocks = <&cru 56>, <&cru 57>,
   <&cru 166>, <&cru 203>;
  clock-names = "aclk", "hclk", "sclk", "apb_pclk";
  power-domains = <&power 13>;
  resets = <&cru 92>;
  reset-names = "crypto-rst";
  status = "disabled";
 };

 i2c1: i2c@ff510000 {
  compatible = "rockchip,rv1126-i2c", "rockchip,rk3399-i2c";
  reg = <0xff510000 0x1000>;
  interrupts = <0 5 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  clocks = <&cru 33>, <&cru 255>;
  clock-names = "i2c", "pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&i2c1_xfer>;
  status = "disabled";
 };

 i2c3: i2c@ff520000 {
  compatible = "rockchip,rv1126-i2c", "rockchip,rk3399-i2c";
  reg = <0xff520000 0x1000>;
  interrupts = <0 7 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  clocks = <&cru 34>, <&cru 256>;
  clock-names = "i2c", "pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&i2c3m0_xfer>;
  status = "disabled";
 };

 i2c4: i2c@ff530000 {
  compatible = "rockchip,rv1126-i2c", "rockchip,rk3399-i2c";
  reg = <0xff530000 0x1000>;
  interrupts = <0 8 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  clocks = <&cru 35>, <&cru 257>;
  clock-names = "i2c", "pclk";
  pinctrl-names = "default";
   clock-frequency = <400000>;
  pinctrl-0 = <&i2c4m0_xfer>;
  status = "okay";

  lsm6ds3@6b {
   compatible = "st,lsm6ds3";
   reg = <0x6b>;
  };

  lsm6dsl@6b {
   compatible = "st,lsm6dsl";
   reg = <0x6b>;
  };
 };

 i2c5: i2c@ff540000 {
  compatible = "rockchip,rv1126-i2c", "rockchip,rk3399-i2c";
  reg = <0xff540000 0x1000>;
  interrupts = <0 9 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  clocks = <&cru 36>, <&cru 258>;
  clock-names = "i2c", "pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&i2c5m0_xfer>;
  status = "disabled";
 };

 pwm8: pwm@ff550000 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff550000 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm8m0_pins>;
  clocks = <&cru 39>, <&cru 261>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm9: pwm@ff550010 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff550010 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm9m0_pins>;
  clocks = <&cru 39>, <&cru 261>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm10: pwm@ff550020 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff550020 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm10m0_pins>;
  clocks = <&cru 39>, <&cru 261>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 pwm11: pwm@ff550030 {
  compatible = "rockchip,rv1126-pwm", "rockchip,rk3328-pwm";
  reg = <0xff550030 0x10>;
  #pwm-cells = <3>;
  pinctrl-names = "active";
  pinctrl-0 = <&pwm11m0_pins>;
  clocks = <&cru 39>, <&cru 261>;
  clock-names = "pwm", "pclk";
  status = "disabled";
 };

 uart0: serial@ff560000 {
  compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
  reg = <0xff560000 0x100>;
  interrupts = <0 12 4>;
  reg-shift = <2>;
  reg-io-width = <4>;
  dmas = <&dmac 5>, <&dmac 4>;
  clock-frequency = <24000000>;
  clocks = <&cru 16>, <&cru 250>;
  clock-names = "baudclk", "apb_pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&uart0_xfer &uart0_ctsn &uart0_rtsn>;
  status = "disabled";
 };

 uart2: serial@ff570000 {
  compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
  reg = <0xff570000 0x100>;
  interrupts = <0 14 4>;
  reg-shift = <2>;
  reg-io-width = <4>;
  dmas = <&dmac 9>, <&dmac 8>;
  clock-frequency = <24000000>;
  clocks = <&cru 20>, <&cru 251>;
  clock-names = "baudclk", "apb_pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&uart2m1_xfer>;
  status = "disabled";
 };

 uart3: serial@ff580000 {
  compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
  reg = <0xff580000 0x100>;
  interrupts = <0 15 4>;
  reg-shift = <2>;
  reg-io-width = <4>;
  dmas = <&dmac 11>, <&dmac 10>;
  clock-frequency = <24000000>;
  clocks = <&cru 24>, <&cru 252>;
  clock-names = "baudclk", "apb_pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&uart3m0_xfer &uart3m0_ctsn &uart3m0_rtsn>;
  status = "disabled";
 };

 uart4: serial@ff590000 {
  compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
  reg = <0xff590000 0x100>;
  interrupts = <0 16 4>;
  reg-shift = <2>;
  reg-io-width = <4>;
  dmas = <&dmac 13>, <&dmac 12>;
  clock-frequency = <24000000>;
  clocks = <&cru 28>, <&cru 253>;
  clock-names = "baudclk", "apb_pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&uart4m0_xfer &uart4m0_ctsn &uart4m0_rtsn>;
  status = "okay";
 };

 uart5: serial@ff5a0000 {
  compatible = "rockchip,rv1126-uart", "snps,dw-apb-uart";
  reg = <0xff5a0000 0x100>;
  interrupts = <0 17 4>;
  reg-shift = <2>;
  reg-io-width = <4>;
  dmas = <&dmac 15>, <&dmac 14>;
  clock-frequency = <24000000>;
  clocks = <&cru 32>, <&cru 254>;
  clock-names = "baudclk", "apb_pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&uart5m0_xfer &uart5m0_ctsn &uart5m0_rtsn>;
  status = "disabled";
 };

 spi1: spi@ff5b0000 {
  compatible = "rockchip,rv1126-spi", "rockchip,rk3066-spi";
  reg = <0xff5b0000 0x1000>;
  interrupts = <0 11 4>;
  #address-cells = <1>;
  #size-cells = <0>;
  clocks = <&cru 37>, <&cru 259>;
  clock-names = "spiclk", "apb_pclk";
  dmas = <&dmac 3>, <&dmac 2>;
  dma-names = "tx", "rx";
  pinctrl-names = "default", "high_speed";
  pinctrl-0 = <&spi1m0_cs0 &spi1m0_cs1 &spi1m0_pins>;
  pinctrl-1 = <&spi1m0_cs0 &spi1m0_cs1 &spi1m0_pins_hs>;
  status = "disabled";
 };

 otp: otp@ff5c0000 {
  compatible = "rockchip,rv1126-otp";
  reg = <0xff5c0000 0x1000>;
  #address-cells = <1>;
  #size-cells = <1>;
  clocks = <&cru 148>, <&cru 294>;
  clock-names = "otp", "apb_pclk";
  status = "disabled";


  otp_cpu_code: cpu-code@2 {
   reg = <0x02 0x2>;
  };
  otp_id: id@7 {
   reg = <0x07 0x10>;
  };
  cpu_leakage: cpu-leakage@17 {
   reg = <0x17 0x1>;
  };
  logic_leakage: logic-leakage@18 {
   reg = <0x18 0x1>;
  };
  npu_leakage: npu-leakage@19 {
   reg = <0x19 0x1>;
  };
  venc_leakage: venc-leakage@1a {
   reg = <0x1a 0x1>;
  };
  cpu_performance: cpu-performance@1e {
   reg = <0x1e 0x1>;
   bits = <4 3>;
  };
  npu_performance: npu-performance@1f {
   reg = <0x1f 0x1>;
   bits = <0 2>;
  };
  venc_performance: venc-performance@1f {
   reg = <0x1f 0x1>;
   bits = <2 2>;
  };
  cpu_tsadc_trim_l: cpu-tsadc-trim-l@23 {
   reg = <0x23 0x1>;
  };
  cpu_tsadc_trim_h: cpu-tsadc-trim-h@24 {
   reg = <0x24 0x1>;
   bits = <0 4>;
  };
  npu_tsadc_trim_l: npu-tsadc-trim-l@25 {
   reg = <0x25 0x1>;
  };
  npu_tsadc_trim_h: npu-tsadc-trim-h@26 {
   reg = <0x26 0x1>;
   bits = <0 4>;
  };
  tsadc_trim_base: tsadc-trim-base@27 {
   reg = <0x27 0x1>;
  };
 };

 saradc: saradc@ff5e0000 {
  compatible = "rockchip,rk3399-saradc";
  reg = <0xff5e0000 0x100>;
  interrupts = <0 40 4>;
  #io-channel-cells = <1>;
  clocks = <&cru 44>, <&cru 266>;
  clock-names = "saradc", "apb_pclk";
  resets = <&cru 59>;
  reset-names = "saradc-apb";
  status = "disabled";
 };

 cpu_tsadc: tsadc@ff5f0000 {
  compatible = "rockchip,rv1126-tsadc";
  reg = <0xff5f0000 0x100>;
  rockchip,grf = <&grf>;
  interrupts = <0 39 4>;
  assigned-clocks = <&cru 54>;
  assigned-clock-rates = <4000000>;
  clocks = <&cru 54>, <&cru 271>,
    <&cru 55>;
  clock-names = "tsadc", "apb_pclk", "phy_clk";
  resets = <&cru 232>, <&cru 233>,
    <&cru 234>;
  reset-names = "tsadc-apb", "tsadc", "tsadc-phy";
  rockchip,hw-tshut-temp = <120000>;
  #thermal-sensor-cells = <1>;
  nvmem-cells = <&cpu_tsadc_trim_l>, <&cpu_tsadc_trim_h>, <&tsadc_trim_base>;
  nvmem-cell-names = "trim_l", "trim_h", "trim_base";
  rockchip,hw-tshut-mode = <0>;
  rockchip,hw-tshut-polarity = <0>;
  pinctrl-names = "gpio", "otpout";
  pinctrl-0 = <&tsadcm0_shut>;
  pinctrl-1 = <&tsadc_shutorg>;
  status = "disabled";
 };

 npu_tsadc: tsadc@ff5f8000 {
  compatible = "rockchip,rv1126-tsadc";
  reg = <0xff5f8000 0x100>;
  rockchip,grf = <&grf>;
  interrupts = <0 113 4>;
  assigned-clocks = <&cru 52>;
  assigned-clock-rates = <4000000>;
  clocks = <&cru 52>, <&cru 270>,
    <&cru 53>;
  clock-names = "tsadc", "apb_pclk", "phy_clk";
  resets = <&cru 216>, <&cru 217>,
    <&cru 218>;
  reset-names = "tsadc-apb", "tsadc", "tsadc-phy";
  rockchip,hw-tshut-temp = <120000>;
  #thermal-sensor-cells = <1>;
  nvmem-cells = <&npu_tsadc_trim_l>, <&npu_tsadc_trim_h>, <&tsadc_trim_base>;
  nvmem-cell-names = "trim_l", "trim_h", "trim_base";
  rockchip,hw-tshut-mode = <0>;
  rockchip,hw-tshut-polarity = <0>;
  pinctrl-names = "gpio", "otpout";
  pinctrl-0 = <&tsadcm0_shut>;
  pinctrl-1 = <&tsadc_shutorg>;
  status = "disabled";
 };

 dcf: dcf@ff600000 {
  compatible = "syscon";
  reg = <0xff600000 0x1000>;
  status = "disabled";
 };

 can: can@ff610000 {
  compatible = "rockchip,can-1.0";
  reg = <0xff610000 0x100>;
  pinctrl-names = "default";
  pinctrl-0 = <&canm0_pins>;
  interrupts = <0 100 4>;
  assigned-clocks = <&cru 51>;
  assigned-clock-rates = <200000000>;
  clocks = <&cru 51>, <&cru 269>;
  clock-names = "baudclk", "apb_pclk";
  resets = <&cru 81>, <&cru 80>;
  reset-names = "can", "can-apb";

  status = "disabled";
 };

 rktimer: rktimer@ff660000 {
  compatible = "rockchip,rk3288-timer";
  reg = <0xff660000 0x20>;
  interrupts = <0 24 4>;
  clocks = <&cru 267>, <&cru 45>;
  clock-names = "pclk", "timer";
 };

 wdt: watchdog@ff680000 {
  compatible = "rockchip,rv1126-wdt", "snps,dw-wdt";
  reg = <0xff680000 0x100>;
  clocks = <&cru 248>;
  interrupts = <0 32 4>;
  status = "disabled";
 };

 mailbox: mailbox@ff6a0000 {
  compatible = "rockchip,rv1126-mailbox",
        "rockchip,rk3368-mailbox";
  reg = <0xff6a0000 0x1000>;
  interrupts = <0 111 4>;
  clocks = <&cru 249>;
  clock-names = "pclk_mailbox";
  #mbox-cells = <1>;
  status = "disabled";
 };

 hw_decompress: decompress@ff6c0000 {
  compatible = "rockchip,hw-decompress";
  reg = <0xff6c0000 0x1000>;
  interrupts = <0 81 4>;
  clocks = <&cru 164>, <&cru 150>, <&cru 268>;
  clock-names = "aclk", "dclk", "pclk";
  resets = <&cru 87>;
  reset-names = "dresetn";
  status = "disabled";
 };

 i2s0_8ch: i2s@ff800000 {
  compatible = "rockchip,rv1126-i2s-tdm";
  reg = <0xff800000 0x1000>;
  interrupts = <0 46 4>;
  clocks = <&cru 61>, <&cru 65>, <&cru 205>;
  clock-names = "mclk_tx", "mclk_rx", "hclk";
  dmas = <&dmac 20>, <&dmac 19>;
  dma-names = "tx", "rx";
  resets = <&cru 99>, <&cru 100>;
  reset-names = "tx-m", "rx-m";
  rockchip,cru = <&cru>;
  rockchip,grf = <&grf>;
  pinctrl-names = "default";
  pinctrl-0 = <&i2s0m0_sclk_tx
        &i2s0m0_sclk_rx
        &i2s0m0_lrck_tx
        &i2s0m0_lrck_rx
        &i2s0m0_sdi0
        &i2s0m0_sdo0
        &i2s0m0_sdo1_sdi3
        &i2s0m0_sdo2_sdi2
        &i2s0m0_sdo3_sdi1>;
  status = "disabled";
 };

 i2s1_2ch: i2s@ff810000 {
  compatible = "rockchip,rv1126-i2s", "rockchip,rk3066-i2s";
  reg = <0xff810000 0x1000>;
  interrupts = <0 47 4>;
  clocks = <&cru 71>, <&cru 206>;
  clock-names = "i2s_clk", "i2s_hclk";
  dmas = <&dmac 22>, <&dmac 21>;
  dma-names = "tx", "rx";
  pinctrl-names = "default";
  pinctrl-0 = <&i2s1m0_sclk
        &i2s1m0_lrck
        &i2s1m0_sdi
        &i2s1m0_sdo>;
  status = "disabled";
 };

 i2s2_2ch: i2s@ff820000 {
  compatible = "rockchip,rv1126-i2s", "rockchip,rk3066-i2s";
  reg = <0xff820000 0x1000>;
  interrupts = <0 48 4>;
  clocks = <&cru 76>, <&cru 207>;
  clock-names = "i2s_clk", "i2s_hclk";
  dmas = <&dmac 24>, <&dmac 23>;
  dma-names = "tx", "rx";
  pinctrl-names = "default";
  pinctrl-0 = <&i2s2m0_sclk
        &i2s2m0_lrck
        &i2s2m0_sdi
        &i2s2m0_sdo>;
  status = "disabled";
 };

 pdm: pdm@ff830000 {
  compatible = "rockchip,rv1126-pdm", "rockchip,pdm";
  reg = <0xff830000 0x1000>;
  clocks = <&cru 78>, <&cru 208>;
  clock-names = "pdm_clk", "pdm_hclk";
  dmas = <&dmac 25>;
  dma-names = "rx";
  pinctrl-names = "default";
  pinctrl-0 = <&pdmm0_clk
        &pdmm0_clk1
        &pdmm0_sdi0
        &pdmm0_sdi1
        &pdmm0_sdi2
        &pdmm0_sdi3>;
  status = "disabled";
 };

 audpwm: audpwm@ff840000 {
  compatible = "rockchip,rv1126-audio-pwm", "rockchip,audio-pwm-v1";
  reg = <0xff840000 0x1000>;
  clocks = <&cru 82>, <&cru 209>;
  clock-names = "clk", "hclk";
  dmas = <&dmac 26>;
  dma-names = "tx";
  pinctrl-names = "default";
  pinctrl-0 = <&audpwmm0_pins>;
  rockchip,sample-width-bits = <11>;
  rockchip,interpolat-points = <1>;
  status = "disabled";
 };

 rkacdc_dig: codec-digital@ff850000 {
  compatible = "rockchip,rv1126-codec-digital", "rockchip,codec-digital-v1";
  reg = <0xff850000 0x1000>;
  clocks = <&cru 83>, <&cru 84>, <&cru 272>;
  clock-names = "adc", "dac", "pclk";
  pinctrl-names = "default";
  pinctrl-0 = <&acodec_pins>;
  resets = <&cru 110>;
  reset-names = "reset" ;
  rockchip,grf = <&grf>;
  status = "disabled";
 };

 dfi: dfi@ff9c0000 {
  reg = <0xff9c0000 0x400>;
  compatible = "rockchip,rv1126-dfi";
  rockchip,pmugrf = <&pmugrf>;
  status = "disabled";
 };

 dmc: dmc {
  compatible = "rockchip,rv1126-dmc";
  dcf = <&dcf>;
  interrupts = <0 0 4>;
  interrupt-names = "complete";
  devfreq-events = <&dfi>;
  clocks = <&cru 147>;
  clock-names = "dmc_clk";
  operating-points-v2 = <&dmc_opp_table>;
  ddr_timing = <&ddr_timing>;
  upthreshold = <40>;
  downdifferential = <20>;
  system-status-freq = <

   (1 << 0) 924000
   (1 << 3) 328000
   (1 << 1) 328000
   (1 << 5) 924000
   (1 << 12) 924000
   (1 << 14) 924000
   (1 << 13) 924000
  >;
  auto-min-freq = <328000>;
  auto-freq-en = <1>;
  #cooling-cells = <2>;
  status = "disabled";
 };

 dmc_opp_table: dmc-opp-table {
  compatible = "operating-points-v2";

  opp-328000000 {
   opp-hz = /bits/ 64 <328000000>;
   opp-microvolt = <800000>;
  };
  opp-528000000 {
   opp-hz = /bits/ 64 <528000000>;
   opp-microvolt = <800000>;
  };
  opp-784000000 {
   opp-hz = /bits/ 64 <784000000>;
   opp-microvolt = <800000>;
  };
  opp-924000000 {
   opp-hz = /bits/ 64 <924000000>;
   opp-microvolt = <800000>;
  };
  opp-1056000000 {
   opp-hz = /bits/ 64 <1056000000>;
   opp-microvolt = <800000>;
   status = "disabled";
  };
 };

 dmcdbg: dmcdbg {
  compatible = "rockchip,rv1126-dmcdbg";
  status = "disabled";
 };

 rkcif: rkcif@ffae0000 {
  compatible = "rockchip,rv1126-cif";
  reg = <0xffae0000 0x8000>;
  reg-names = "cif_regs";
  interrupts = <0 58 4>;
  interrupt-names = "cif-intr";
  clocks = <&cru 181>,<&cru 222>,
    <&cru 155>;
  clock-names = "aclk_cif","hclk_cif",
         "dclk_cif";
  resets = <&cru 148>, <&cru 149>,
    <&cru 150>, <&cru 151>,
    <&cru 152>, <&cru 153>;
  reset-names = "rst_cif_a", "rst_cif_h",
         "rst_cif_d", "rst_cif_p",
         "rst_cif_i", "rst_cif_rx_p";
  assigned-clocks = <&cru 155>;
  assigned-clock-rates = <300000000>;
  power-domains = <&power 9>;
  rockchip,grf = <&grf>;

  memory-region = <&isp_reserved>;
  status = "disabled";
 };

 rkcif_mmu: iommu@ffae0800 {
  compatible = "rockchip,iommu";
  reg = <0xffae0800 0x100>;
  interrupts = <0 58 4>;
  interrupt-names = "cif_mmu";
  clocks = <&cru 181>, <&cru 222>;
  clock-names = "aclk", "iface";
  power-domains = <&power 9>;
  #iommu-cells = <0>;
  status = "disabled";
 };

 rkcif_lite: rkcif_lite@ffae8000 {
  compatible = "rockchip,rv1126-cif-lite";
  reg = <0xffae8000 0x8000>;
  reg-names = "cif_regs";
  interrupts = <0 53 4>;
  interrupt-names = "cif-lite-intr";
  clocks = <&cru 182>,<&cru 223>,
    <&cru 156>;
  clock-names = "aclk_cif_lite","hclk_cif_lite",
         "dclk_cif_lite";
  resets = <&cru 220>, <&cru 221>,
    <&cru 222>, <&cru 223>;
  reset-names = "rst_cif_lite_a", "rst_cif_lite_h",
         "rst_cif_lite_d", "rst_cif_lite_rx_p";
  assigned-clocks = <&cru 156>;
  assigned-clock-rates = <300000000>;
  power-domains = <&power 9>;
  iommus = <&rkcif_lite_mmu>;
  status = "disabled";
 };

 rkcif_lite_mmu: iommu@ffae8800 {
  compatible = "rockchip,iommu";
  reg = <0xffae8800 0x100>;
  interrupts = <0 53 4>;
  interrupt-names = "cif_lite_mmu";
  clocks = <&cru 182>, <&cru 223>;
  clock-names = "aclk", "iface";
  power-domains = <&power 9>;
  #iommu-cells = <0>;
  status = "disabled";
 };

 rk_rga: rk_rga@ffaf0000 {
  compatible = "rockchip,rga2";
  reg = <0xffaf0000 0x1000>;
  interrupts = <0 62 4>;
  clocks = <&cru 174>, <&cru 217>, <&cru 90>;
  clock-names = "aclk_rga", "hclk_rga", "clk_rga";
  power-domains = <&power 10>;
  status = "disabled";
 };

 vop: vop@ffb00000 {
  compatible = "rockchip,rv1126-vop";
  reg = <0xffb00000 0x200>, <0xffb00a00 0x400>;
  reg-names = "regs", "gamma_lut";
  rockchip,grf = <&grf>;
  interrupts = <0 59 4>;
  clocks = <&cru 175>, <&cru 154>, <&cru 218>;
  clock-names = "aclk_vop", "dclk_vop", "hclk_vop";
  iommus = <&vop_mmu>;
  power-domains = <&power 10>;
  status = "disabled";

  vop_out: port {
   #address-cells = <1>;
   #size-cells = <0>;

   vop_out_rgb: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&rgb_in_vop>;
   };

   vop_out_dsi: endpoint@1 {
    reg = <1>;
    remote-endpoint = <&dsi_in_vop>;
   };
  };
 };

 vop_mmu: iommu@ffb00f00 {
  compatible = "rockchip,iommu";
  reg = <0xffb00f00 0x100>;
  interrupts = <0 59 4>;
  interrupt-names = "vop_mmu";
  clocks = <&cru 175>, <&cru 218>;
  clock-names = "aclk", "iface";
  #iommu-cells = <0>;
  rockchip,disable-device-link-resume;
  power-domains = <&power 10>;
  status = "disabled";
 };

 mipi_csi2: mipi-csi2@ffb10000 {
  compatible = "rockchip,rv1126-mipi-csi2";
  reg = <0xffb10000 0x10000>;
  reg-names = "csihost_regs";
  interrupts = <0 56 4>,
        <0 57 4>;
  interrupt-names = "csi-intr1", "csi-intr2";
  clocks = <&cru 276>;
  clock-names = "pclk_csi2host";
  resets = <&cru 159>;
  reset-names = "srst_csihost_p";
  power-domains = <&power 9>;
  status = "disabled";
 };

 iep: iep@ffb20000 {
  compatible = "rockchip,rv1126-iep", "rockchip,iep-v2";
  reg = <0xffb20000 0x500>;
  interrupts = <0 114 4>;
  clocks = <&cru 176>, <&cru 219>, <&cru 91>;
  clock-names = "aclk", "hclk", "sclk";
  resets = <&cru 139>, <&cru 140>,
   <&cru 141>;
  reset-names = "rst_a", "rst_h", "rst_s";
  power-domains = <&power 10>;
  rockchip,srv = <&mpp_srv>;
  rockchip,taskqueue-node = <3>;
  rockchip,resetgroup-node = <3>;
  iommus = <&iep_mmu>;
  status = "disabled";
 };

 iep_mmu: iommu@ffb20800 {
  compatible = "rockchip,iommu";
  reg = <0xffb20800 0x100>;
  interrupts = <0 114 4>;
  interrupt-names = "iep_mmu";
  clocks = <&cru 176>, <&cru 219>;
  clock-names = "aclk", "iface";
  #iommu-cells = <0>;
  power-domains = <&power 10>;

  status = "disabled";
 };

 dsi: dsi@ffb30000 {
  compatible = "rockchip,rv1126-mipi-dsi";
  reg = <0xffb30000 0x500>;
  interrupts = <0 61 4>;
  clocks = <&cru 274>, <&mipi_dphy>;
  clock-names = "pclk", "hs_clk";
  resets = <&cru 138>;
  reset-names = "apb";
  phys = <&mipi_dphy>;
  phy-names = "mipi_dphy";
  rockchip,grf = <&grf>;
  #address-cells = <1>;
  #size-cells = <0>;
  power-domains = <&power 10>;
  status = "disabled";

  ports {
   port {
    dsi_in_vop: endpoint {
     remote-endpoint = <&vop_out_dsi>;
    };
   };
  };
 };

 rkisp: rkisp@ffb50000 {
  compatible = "rockchip,rv1126-rkisp";
  reg = <0xffb50000 0x10000>;
  interrupts = <0 52 4>,
        <0 54 4>,
        <0 55 4>;
  interrupt-names = "isp_irq", "mi_irq", "mipi_irq";
  clocks = <&cru 180>, <&cru 221>,
    <&cru 95>;
  clock-names = "aclk_isp", "hclk_isp", "clk_isp";
  assigned-clocks = <&cru 180>, <&cru 221>;
  assigned-clock-rates = <500000000>, <250000000>;
  resets = <&cru 147>, <&cru 142>;
  reset-names = "isp", "isp-rx-p";
  power-domains = <&power 9>;
  iommus = <&rkisp_mmu>;
  memory-region = <&isp_reserved>;
  status = "disabled";
 };

 rkisp_mmu: iommu@ffb51a00 {
  compatible = "rockchip,iommu";
  reg = <0xffb51a00 0x100>;
  interrupts = <0 51 4>;
  interrupt-names = "isp_mmu";
  clocks = <&cru 180>, <&cru 221>;
  clock-names = "aclk", "iface";
  power-domains = <&power 9>;
  #iommu-cells = <0>;
  rockchip,disable-mmu-reset;
  status = "disabled";
 };

 rkisp_vir0: rkisp-vir0 {
  compatible = "rockchip,rv1126-rkisp-vir";
  rockchip,hw = <&rkisp>;
  status = "disabled";

  ports {
   #address-cells = <1>;
   #size-cells = <0>;

   port@1 {
    reg = <1>;
    #address-cells = <1>;
    #size-cells = <0>;

    isp0_out: endpoint@1 {
     reg = <1>;
     remote-endpoint = <&ispp0_in>;
    };
   };
  };
 };

 rkisp_vir1: rkisp-vir1 {
  compatible = "rockchip,rv1126-rkisp-vir";
  rockchip,hw = <&rkisp>;
  status = "disabled";

  ports {
   #address-cells = <1>;
   #size-cells = <0>;

   port@1 {
    reg = <1>;
    #address-cells = <1>;
    #size-cells = <0>;

    isp1_out: endpoint@1 {
     reg = <1>;
     remote-endpoint = <&ispp1_in>;
    };
   };
  };
 };

 rkisp_vir2: rkisp-vir2 {
  compatible = "rockchip,rv1126-rkisp-vir";
  rockchip,hw = <&rkisp>;
  status = "disabled";

  ports {
   #address-cells = <1>;
   #size-cells = <0>;

   port@1 {
    reg = <1>;
    #address-cells = <1>;
    #size-cells = <0>;

    isp2_out: endpoint@1 {
     reg = <1>;
     remote-endpoint = <&ispp2_in>;
    };
   };
  };
 };

 rkispp: rkispp@ffb60000 {
  compatible = "rockchip,rv1126-rkispp";
  reg = <0xffb60000 0x20000>;
  interrupts = <0 63 4>,
        <0 64 4>;
  interrupt-names = "ispp_irq", "fec_irq";
  clocks = <&cru 186>, <&cru 225>,
    <&cru 107>;
  clock-names = "aclk_ispp", "hclk_ispp", "clk_ispp";
  assigned-clocks = <&cru 186>, <&cru 225>,
      <&cru 107>;
  assigned-clock-rates = <500000000>, <250000000>,
           <400000000>;
  power-domains = <&power 11>;
  iommus = <&rkispp_mmu>;
  rockchip,restart-monitor-en;
  status = "disabled";
 };

 rkispp_mmu: iommu@ffb60e00 {
  compatible = "rockchip,iommu";
  reg = <0xffb60e00 0x40>, <0xffb60e40 0x40>, <0xffb60f00 0x40>;
  interrupts = <0 65 4>,
        <0 66 4>,
        <0 67 4>;
  interrupt-names = "ispp_mmu0_r", "ispp_mmu0_w", "ispp_mmu1";
  clocks = <&cru 186>, <&cru 225>;
  clock-names = "aclk", "iface";
  power-domains = <&power 11>;
  #iommu-cells = <0>;
  rockchip,disable-mmu-reset;
  status = "disabled";
 };

 rkispp_vir0: rkispp-vir0 {
  compatible = "rockchip,rv1126-rkispp-vir";
  rockchip,hw = <&rkispp>;
  status = "disabled";

  port {
   #address-cells = <1>;
   #size-cells = <0>;

   ispp0_in: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&isp0_out>;
   };
  };
 };

 rkispp_vir1: rkispp-vir1 {
  compatible = "rockchip,rv1126-rkispp-vir";
  rockchip,hw = <&rkispp>;
  status = "disabled";

  port {
   #address-cells = <1>;
   #size-cells = <0>;

   ispp1_in: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&isp1_out>;
   };
  };
 };

 rkispp_vir2: rkispp-vir2 {
  compatible = "rockchip,rv1126-rkispp-vir";
  rockchip,hw = <&rkispp>;
  status = "disabled";

  port {
   #address-cells = <1>;
   #size-cells = <0>;

   ispp2_in: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&isp2_out>;
   };
  };
 };

 rkvdec: rkvdec@ffb80000 {
  compatible = "rockchip,rkv-decoder-rv1126", "rockchip,rkv-decoder-v1";
  reg = <0xffb80000 0x400>;
  interrupts = <0 71 4>;
  interrupt-names = "irq_dec";
  clocks = <&cru 171>, <&cru 214>,
    <&cru 88>, <&cru 87>,
    <&cru 89>;
  clock-names = "aclk_vcodec", "hclk_vcodec","clk_cabac",
         "clk_core", "clk_hevc_cabac";
  resets = <&cru 119>, <&cru 120>,
    <&cru 122>, <&cru 121>,
    <&cru 123>;
  reset-names = "video_a", "video_h", "video_cabac",
         "video_core", "video_hevc_cabac";
  power-domains = <&power 12>;
  iommus = <&rkvdec_mmu>;
  rockchip,srv = <&mpp_srv>;
  rockchip,taskqueue-node = <0>;
  rockchip,resetgroup-node = <0>;
  status = "disabled";
 };

 rkvdec_mmu: iommu@ffb80480 {
  compatible = "rockchip,iommu";
  reg = <0xffb80480 0x40>, <0xffb804c0 0x40>;
  interrupts = <0 72 4>;
  interrupt-names = "rkvdec_mmu";
  clocks = <&cru 171>, <&cru 214>;
  clock-names = "aclk", "iface";
  power-domains = <&power 12>;
  #iommu-cells = <0>;
  status = "disabled";
 };

 vepu: vepu@ffb90000 {
  compatible = "rockchip,vpu-encoder-v2";
  reg = <0xffb90000 0x400>;
  interrupts = <0 74 4>;
  clocks = <&cru 172>, <&cru 215>;
  clock-names = "aclk_vcodec", "hclk_vcodec";
  rockchip,normal-rates = <400000000>, <0>;
  rockchip,advanced-rates = <500000000>, <0>;
  rockchip,default-max-load = <2088960>;
  resets = <&cru 126>, <&cru 127>;
  reset-names = "shared_video_a", "shared_video_h";
  iommus = <&vpu_mmu>;
  rockchip,srv = <&mpp_srv>;
  rockchip,taskqueue-node = <1>;
  rockchip,resetgroup-node = <1>;
  power-domains = <&power 12>;
  status = "disabled";
 };

 vdpu: vdpu@ffb90400 {
  compatible = "rockchip,vpu-decoder-v2";
  reg = <0xffb90400 0x400>;
  interrupts = <0 73 4>;
  interrupt-names = "irq_dec";
  clocks = <&cru 172>, <&cru 215>;
  clock-names = "aclk_vcodec", "hclk_vcodec";
  resets = <&cru 126>, <&cru 127>;
  reset-names = "shared_video_a", "shared_video_h";
  iommus = <&vpu_mmu>;
  power-domains = <&power 12>;
  rockchip,srv = <&mpp_srv>;
  rockchip,taskqueue-node = <1>;
  rockchip,resetgroup-node = <1>;
  status = "disabled";
 };

 vpu_mmu: iommu@ffb90800 {
  compatible = "rockchip,iommu";
  reg = <0xffb90800 0x40>;
  interrupts = <0 75 4>;
  interrupt-names = "vpu_mmu";
  clock-names = "aclk", "iface";
  clocks = <&cru 172>, <&cru 215>;
  power-domains = <&power 12>;
  #iommu-cells = <0>;
  status = "disabled";
 };

 rkvenc: rkvenc@ffbb0000 {
  compatible = "rockchip,rkv-encoder-rv1126", "rockchip,rkv-encoder-v1";
  reg = <0xffbb0000 0x400>;
  interrupts = <0 68 4>;
  interrupt-names = "irq_enc";
  clocks = <&cru 168>, <&cru 211>,
   <&cru 86>;
  clock-names = "aclk_vcodec", "hclk_vcodec", "clk_core";
  rockchip,normal-rates = <297000000>, <0>, <396000000>;
  rockchip,advanced-rates = <297000000>, <0>, <594000000>;
  rockchip,default-max-load = <2088960>;
  resets = <&cru 114>, <&cru 115>,
   <&cru 116>;
  reset-names = "video_a", "video_h", "video_core";
  assigned-clocks = <&cru 168>, <&cru 86>;
  assigned-clock-rates = <297000000>, <396000000>;
  operating-points-v2 = <&rkvenc_opp_table>;
  dynamic-power-coefficient = <1418>;
  #cooling-cells = <2>;
  iommus = <&rkvenc_mmu>;
  node-name = "rkvenc";
  rockchip,srv = <&mpp_srv>;
  rockchip,taskqueue-node = <2>;
  rockchip,resetgroup-node = <2>;
  power-domains = <&power 8>;
  status = "disabled";
 };

 rkvenc_opp_table: rkvenc-opp-table {
  compatible = "operating-points-v2";

  nvmem-cells = <&venc_leakage>, <&venc_performance>;
  nvmem-cell-names = "leakage", "performance";

  rockchip,temp-freq-table = <
   80000 500000
   100000 396000
  >;

  clocks = <&pmucru 1>;
  rockchip,bin-scaling-sel = <
   0 37
   1 43
  >;
  rockchip,bin-voltage-sel = <
   1 0
  >;

  rockchip,evb-irdrop = <25000>;


  opp-297000000 {
   opp-hz = /bits/ 64 <297000000>;
   opp-microvolt = <725000 725000 1000000>;
   opp-microvolt-L0 = <750000 750000 1000000>;
  };
  opp-396000000 {
   opp-hz = /bits/ 64 <396000000>;
   opp-microvolt = <725000 725000 1000000>;
   opp-microvolt-L0 = <775000 775000 1000000>;
  };
  opp-500000000 {
   opp-hz = /bits/ 64 <500000000>;
   opp-microvolt = <750000 750000 1000000>;
  };
  opp-594000000 {
   opp-hz = /bits/ 64 <594000000>;
   opp-microvolt = <825000 825000 1000000>;
  };
 };

 rkvenc_mmu: iommu@ffbb0f00 {
  compatible = "rockchip,iommu";
  reg = <0xffbb0f00 0x40>, <0xffbb0f40 0x40>;
  interrupts = <0 69 4>,
   <0 70 4>;
  interrupt-names = "rkvenc_mmu0", "rkvenc_mmu1";
  clocks = <&cru 168>, <&cru 211>;
  clock-names = "aclk", "iface";
  rockchip,disable-mmu-reset;
  rockchip,enable-cmd-retry;
  #iommu-cells = <0>;
  power-domains = <&power 8>;
  status = "disabled";
 };

 pvtm@ffc00000 {
  compatible = "rockchip,rv1126-npu-pvtm";
  reg = <0xffc00000 0x100>;
  #address-cells = <1>;
  #size-cells = <0>;

  pvtm@1 {
   reg = <1>;
   clocks = <&cru 146>, <&cru 282>;
   clock-names = "clk", "pclk";
   resets = <&cru 215>, <&cru 214>;
   reset-names = "rts", "rst-p";
  };
 };

 gmac: ethernet@ffc40000 {
  compatible = "rockchip,rv1126-gmac", "snps,dwmac-4.20a";
  reg = <0xffc40000 0x0ffff>;
  interrupts = <0 95 4>,
        <0 96 4>;
  interrupt-names = "macirq", "eth_wake_irq";
  rockchip,grf = <&grf>;
  clocks = <&cru 126>, <&cru 136>,
    <&cru 136>, <&cru 127>,
    <&cru 191>, <&cru 278>,
    <&cru 136>, <&cru 137>;
  clock-names = "stmmaceth", "mac_clk_rx",
         "mac_clk_tx", "clk_mac_ref",
         "aclk_mac", "pclk_mac",
         "clk_mac_speed", "ptp_ref";
  resets = <&cru 190>;
  reset-names = "stmmaceth";

  snps,mixed-burst;
  snps,tso;

  snps,axi-config = <&stmmac_axi_setup>;
  snps,mtl-rx-config = <&mtl_rx_setup>;
  snps,mtl-tx-config = <&mtl_tx_setup>;
  status = "disabled";

  mdio: mdio {
   compatible = "snps,dwmac-mdio";
   #address-cells = <0x1>;
   #size-cells = <0x0>;
  };

  stmmac_axi_setup: stmmac-axi-config {
   snps,wr_osr_lmt = <4>;
   snps,rd_osr_lmt = <8>;
   snps,blen = <0 0 0 0 16 8 4>;
  };

  mtl_rx_setup: rx-queues-config {
   snps,rx-queues-to-use = <1>;
   queue0 {};
  };

  mtl_tx_setup: tx-queues-config {
   snps,tx-queues-to-use = <1>;
   queue0 {};
  };
 };

 emmc: dwmmc@ffc50000 {
  compatible = "rockchip,rv1126-dw-mshc", "rockchip,rk3288-dw-mshc";
  reg = <0xffc50000 0x4000>;
  interrupts = <0 78 4>;
  clocks = <&cru 232>, <&cru 114>,
    <&cru 115>, <&cru 116>;
  clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
  fifo-depth = <0x100>;
  max-frequency = <200000000>;
  pinctrl-names = "default";
  pinctrl-0 = <&emmc_clk &emmc_cmd &emmc_bus8>;
  power-domains = <&power 15>;
  rockchip,use-v2-tuning;
  status = "disabled";
 };

 sdmmc: dwmmc@ffc60000 {
  compatible = "rockchip,rv1126-dw-mshc", "rockchip,rk3288-dw-mshc";
  reg = <0xffc60000 0x4000>;
  interrupts = <0 76 4>;
  clocks = <&cru 228>, <&cru 108>,
    <&cru 109>, <&cru 110>;
  clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
  fifo-depth = <0x100>;
  max-frequency = <200000000>;
  pinctrl-names = "default";
  pinctrl-0 = <&sdmmc0_clk &sdmmc0_cmd &sdmmc0_det &sdmmc0_bus4>;
  status = "disabled";
 };

 sdio: dwmmc@ffc70000 {
  compatible = "rockchip,rv1126-dw-mshc", "rockchip,rk3288-dw-mshc";
  reg = <0xffc70000 0x4000>;
  interrupts = <0 77 4>;
  clocks = <&cru 230>, <&cru 111>,
    <&cru 112>, <&cru 113>;
  clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
  fifo-depth = <0x100>;
  max-frequency = <200000000>;
  pinctrl-names = "default";
  pinctrl-0 = <&sdio_clk &sdio_cmd &sdio_bus4>;
  power-domains = <&power 16>;
  status = "disabled";
 };

 nandc: nandc@ffc80000 {
  compatible = "rockchip,rk-nandc";
  reg = <0xffc80000 0x4000>;
  interrupts = <0 79 4>;
  nandc_id = <0>;
  clocks = <&cru 117>, <&cru 233>;
  clock-names = "clk_nandc", "hclk_nandc";
  pinctrl-names = "default";
  pinctrl-0 = <&flash_pins>;
  power-domains = <&power 15>;
  status = "disabled";
 };

 sfc: sfc@ffc90000 {
  compatible = "rockchip,sfc";
  reg = <0xffc90000 0x4000>;
  interrupts = <0 80 4>;
  clocks = <&cru 118>, <&cru 234>;
  clock-names = "clk_sfc", "hclk_sfc";
  pinctrl-names = "default";
  pinctrl-0 = <&fspi_pins>;
  assigned-clocks = <&cru 118>;
  assigned-clock-rates = <80000000>;
  power-domains = <&power 15>;
  status = "disabled";
 };

 npu: npu@ffbc0000 {
  compatible = "rockchip,npu";
  reg = <0xffbc0000 0x4000>;
  clocks = <&cru 195>, <&cru 240>, <&cru 281>, <&cru 144>;
  clock-names = "aclk_npu", "hclk_npu", "pclk_pdnpu", "sclk_npu";
  assigned-clocks = <&cru 144>, <&cru 195>;
  assigned-clock-rates = <396000000>, <600000000>;
  operating-points-v2 = <&npu_opp_table>;
  dynamic-power-coefficient = <1343>;
  #cooling-cells = <2>;
  interrupts = <0 107 4>;
  power-domains = <&power 7>;
  status = "disabled";
 };

 npu_opp_table: npu-opp-table {
  compatible = "operating-points-v2";

  nvmem-cells = <&npu_leakage>, <&npu_performance>;
  nvmem-cell-names = "leakage", "performance";

  rockchip,temp-freq-table = <
   80000 600000
   90000 396000
   100000 300000
  >;

  clocks = <&pmucru 1>;
  rockchip,bin-scaling-sel = <
   0 23
   1 37
   2 37
  >;
  rockchip,bin-voltage-sel = <
   2 0
  >;
  rockchip,pvtm-voltage-sel = <
   0 112500 1
   112501 117500 2
   117501 999999 3
  >;
  rockchip,pvtm-freq = <396000>;
  rockchip,pvtm-volt = <800000>;
  rockchip,pvtm-ch = <1 0>;
  rockchip,pvtm-sample-time = <1000>;
  rockchip,pvtm-number = <10>;
  rockchip,pvtm-error = <1000>;
  rockchip,pvtm-ref-temp = <37>;
  rockchip,pvtm-temp-prop = <(-29) 0>;
  rockchip,pvtm-thermal-zone = "npu-thermal";

  opp-200000000 {
   opp-hz = /bits/ 64 <200000000>;
   opp-microvolt = <750000 750000 1000000>;
   opp-microvolt-L0 = <800000 800000 1000000>;
  };
  opp-300000000 {
   opp-hz = /bits/ 64 <300000000>;
   opp-microvolt = <750000 750000 1000000>;
   opp-microvolt-L0 = <800000 800000 1000000>;
  };
  opp-396000000 {
   opp-hz = /bits/ 64 <396000000>;
   opp-microvolt = <750000 750000 1000000>;
   opp-microvolt-L0 = <800000 800000 1000000>;
  };
  opp-500000000 {
   opp-hz = /bits/ 64 <500000000>;
   opp-microvolt = <750000 750000 1000000>;
   opp-microvolt-L0 = <800000 800000 1000000>;
  };
  opp-600000000 {
   opp-hz = /bits/ 64 <600000000>;
   opp-microvolt = <750000 750000 1000000>;
   opp-microvolt-L0 = <800000 800000 1000000>;
  };
  opp-700000000 {
   opp-hz = /bits/ 64 <700000000>;
   opp-microvolt = <800000 800000 1000000>;
   opp-microvolt-L1 = <800000 800000 1000000>;
   opp-microvolt-L2 = <775000 775000 1000000>;
   opp-microvolt-L3 = <750000 750000 1000000>;
  };
  opp-800000000 {
   opp-hz = /bits/ 64 <800000000>;
   opp-microvolt = <850000 850000 1000000>;
   opp-microvolt-L1 = <850000 850000 1000000>;
   opp-microvolt-L2 = <825000 825000 1000000>;
   opp-microvolt-L3 = <800000 800000 1000000>;
  };
  opp-934000000 {
   opp-hz = /bits/ 64 <934000000>;
   opp-microvolt = <950000 950000 1000000>;
   opp-microvolt-L1 = <950000 950000 1000000>;
   opp-microvolt-L2 = <925000 925000 1000000>;
   opp-microvolt-L3 = <900000 900000 1000000>;
  };
 };

 usbdrd: usb0 {
  compatible = "rockchip,rv1126-dwc3", "rockchip,rk3399-dwc3";
  #address-cells = <1>;
  #size-cells = <1>;
  ranges;
  clocks = <&cru 120>, <&cru 189>,
    <&cru 201>;
  clock-names = "ref_clk", "bus_clk", "hclk";
  status = "disabled";

  usbdrd_dwc3: dwc3@ffd00000 {
   compatible = "snps,dwc3";
   reg = <0xffd00000 0x100000>;
   interrupts = <0 85 4>;
   dr_mode = "otg";
   maximum-speed = "high-speed";
   phys = <&u2phy_otg>;
   phy-names = "usb2-phy";
   phy_type = "utmi_wide";
   power-domains = <&power 17>;
   resets = <&cru 181>;
   reset-names = "usb3-otg";
   snps,dis_enblslpm_quirk;
   snps,dis-u2-freeclk-exists-quirk;
   snps,dis_u2_susphy_quirk;
   snps,dis-del-phy-power-chg-quirk;
   snps,tx-ipgap-linecheck-dis-quirk;
   snps,tx-fifo-resize;
   snps,xhci-trb-ent-quirk;
   status = "disabled";
  };
 };

 usb_host0_ehci: usb@ffe00000 {
  compatible = "generic-ehci";
  reg = <0xffe00000 0x10000>;
  interrupts = <0 82 4>;
  clocks = <&cru 237>, <&cru 238>,
    <&u2phy1>;
  clock-names = "usbhost", "arbiter", "utmi";
  phys = <&u2phy_host>;
  phy-names = "usb";
  power-domains = <&power 17>;
  status = "disabled";
 };

 usb_host0_ohci: usb@ffe10000 {
  compatible = "generic-ohci";
  reg = <0xffe10000 0x10000>;
  interrupts = <0 83 4>;
  clocks = <&cru 237>, <&cru 238>,
    <&u2phy1>;
  clock-names = "usbhost", "arbiter", "utmi";
  phys = <&u2phy_host>;
  phy-names = "usb";
  power-domains = <&power 17>;
  status = "disabled";
 };

 pinctrl: pinctrl {
  compatible = "rockchip,rv1126-pinctrl";
  rockchip,grf = <&grf>;
  rockchip,pmu = <&pmugrf>;
  #address-cells = <1>;
  #size-cells = <1>;
  ranges;

  gpio0: gpio0@ff460000 {
   compatible = "rockchip,gpio-bank";
   reg = <0xff460000 0x100>;
   interrupts = <0 34 4>;
   clocks = <&pmucru 38>, <&pmucru 19>;

   gpio-controller;
   #gpio-cells = <2>;

   interrupt-controller;
   #interrupt-cells = <2>;
  };

  gpio1: gpio1@ff620000 {
   compatible = "rockchip,gpio-bank";
   reg = <0xff620000 0x100>;
   interrupts = <0 35 4>;
   clocks = <&cru 262>, <&cru 40>;

   gpio-controller;
   #gpio-cells = <2>;

   interrupt-controller;
   #interrupt-cells = <2>;
  };

  gpio2: gpio2@ff630000 {
   compatible = "rockchip,gpio-bank";
   reg = <0xff630000 0x100>;
   interrupts = <0 36 4>;
   clocks = <&cru 263>, <&cru 41>;

   gpio-controller;
   #gpio-cells = <2>;

   interrupt-controller;
   #interrupt-cells = <2>;
  };

  gpio3: gpio3@ff640000 {
   compatible = "rockchip,gpio-bank";
   reg = <0xff640000 0x100>;
   interrupts = <0 37 4>;
   clocks = <&cru 264>, <&cru 42>;

   gpio-controller;
   #gpio-cells = <2>;

   interrupt-controller;
   #interrupt-cells = <2>;
  };

  gpio4: gpio4@ff650000 {
   compatible = "rockchip,gpio-bank";
   reg = <0xff650000 0x100>;
   interrupts = <0 38 4>;
   clocks = <&cru 265>, <&cru 43>;

   gpio-controller;
   #gpio-cells = <2>;

   interrupt-controller;
   #interrupt-cells = <2>;
  };
 };
};

# 1 "arch/arm/boot/dts/rv1126-pinctrl.dtsi" 1






# 1 "arch/arm/boot/dts/rockchip-pinconf.dtsi" 1




&pinctrl {

 /omit-if-no-ref/
 pcfg_pull_up: pcfg-pull-up {
  bias-pull-up;
 };

 /omit-if-no-ref/
 pcfg_pull_down: pcfg-pull-down {
  bias-pull-down;
 };

 /omit-if-no-ref/
 pcfg_pull_none: pcfg-pull-none {
  bias-disable;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_0: pcfg-pull-none-drv-level-0 {
  bias-disable;
  drive-strength = <0>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_1: pcfg-pull-none-drv-level-1 {
  bias-disable;
  drive-strength = <1>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_2: pcfg-pull-none-drv-level-2 {
  bias-disable;
  drive-strength = <2>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_3: pcfg-pull-none-drv-level-3 {
  bias-disable;
  drive-strength = <3>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_4: pcfg-pull-none-drv-level-4 {
  bias-disable;
  drive-strength = <4>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_5: pcfg-pull-none-drv-level-5 {
  bias-disable;
  drive-strength = <5>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_6: pcfg-pull-none-drv-level-6 {
  bias-disable;
  drive-strength = <6>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_7: pcfg-pull-none-drv-level-7 {
  bias-disable;
  drive-strength = <7>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_8: pcfg-pull-none-drv-level-8 {
  bias-disable;
  drive-strength = <8>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_9: pcfg-pull-none-drv-level-9 {
  bias-disable;
  drive-strength = <9>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_10: pcfg-pull-none-drv-level-10 {
  bias-disable;
  drive-strength = <10>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_11: pcfg-pull-none-drv-level-11 {
  bias-disable;
  drive-strength = <11>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_12: pcfg-pull-none-drv-level-12 {
  bias-disable;
  drive-strength = <12>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_13: pcfg-pull-none-drv-level-13 {
  bias-disable;
  drive-strength = <13>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_14: pcfg-pull-none-drv-level-14 {
  bias-disable;
  drive-strength = <14>;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_15: pcfg-pull-none-drv-level-15 {
  bias-disable;
  drive-strength = <15>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_0: pcfg-pull-up-drv-level-0 {
  bias-pull-up;
  drive-strength = <0>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_1: pcfg-pull-up-drv-level-1 {
  bias-pull-up;
  drive-strength = <1>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_2: pcfg-pull-up-drv-level-2 {
  bias-pull-up;
  drive-strength = <2>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_3: pcfg-pull-up-drv-level-3 {
  bias-pull-up;
  drive-strength = <3>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_4: pcfg-pull-up-drv-level-4 {
  bias-pull-up;
  drive-strength = <4>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_5: pcfg-pull-up-drv-level-5 {
  bias-pull-up;
  drive-strength = <5>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_6: pcfg-pull-up-drv-level-6 {
  bias-pull-up;
  drive-strength = <6>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_7: pcfg-pull-up-drv-level-7 {
  bias-pull-up;
  drive-strength = <7>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_8: pcfg-pull-up-drv-level-8 {
  bias-pull-up;
  drive-strength = <8>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_9: pcfg-pull-up-drv-level-9 {
  bias-pull-up;
  drive-strength = <9>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_10: pcfg-pull-up-drv-level-10 {
  bias-pull-up;
  drive-strength = <10>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_11: pcfg-pull-up-drv-level-11 {
  bias-pull-up;
  drive-strength = <11>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_12: pcfg-pull-up-drv-level-12 {
  bias-pull-up;
  drive-strength = <12>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_13: pcfg-pull-up-drv-level-13 {
  bias-pull-up;
  drive-strength = <13>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_14: pcfg-pull-up-drv-level-14 {
  bias-pull-up;
  drive-strength = <14>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_drv_level_15: pcfg-pull-up-drv-level-15 {
  bias-pull-up;
  drive-strength = <15>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_0: pcfg-pull-down-drv-level-0 {
  bias-pull-down;
  drive-strength = <0>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_1: pcfg-pull-down-drv-level-1 {
  bias-pull-down;
  drive-strength = <1>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_2: pcfg-pull-down-drv-level-2 {
  bias-pull-down;
  drive-strength = <2>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_3: pcfg-pull-down-drv-level-3 {
  bias-pull-down;
  drive-strength = <3>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_4: pcfg-pull-down-drv-level-4 {
  bias-pull-down;
  drive-strength = <4>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_5: pcfg-pull-down-drv-level-5 {
  bias-pull-down;
  drive-strength = <5>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_6: pcfg-pull-down-drv-level-6 {
  bias-pull-down;
  drive-strength = <6>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_7: pcfg-pull-down-drv-level-7 {
  bias-pull-down;
  drive-strength = <7>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_8: pcfg-pull-down-drv-level-8 {
  bias-pull-down;
  drive-strength = <8>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_9: pcfg-pull-down-drv-level-9 {
  bias-pull-down;
  drive-strength = <9>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_10: pcfg-pull-down-drv-level-10 {
  bias-pull-down;
  drive-strength = <10>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_11: pcfg-pull-down-drv-level-11 {
  bias-pull-down;
  drive-strength = <11>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_12: pcfg-pull-down-drv-level-12 {
  bias-pull-down;
  drive-strength = <12>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_13: pcfg-pull-down-drv-level-13 {
  bias-pull-down;
  drive-strength = <13>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_14: pcfg-pull-down-drv-level-14 {
  bias-pull-down;
  drive-strength = <14>;
 };

 /omit-if-no-ref/
 pcfg_pull_down_drv_level_15: pcfg-pull-down-drv-level-15 {
  bias-pull-down;
  drive-strength = <15>;
 };

 /omit-if-no-ref/
 pcfg_pull_up_smt: pcfg-pull-up-smt {
  bias-pull-up;
  input-schmitt-enable;
 };

 /omit-if-no-ref/
 pcfg_pull_down_smt: pcfg-pull-down-smt {
  bias-pull-down;
  input-schmitt-enable;
 };

 /omit-if-no-ref/
 pcfg_pull_none_smt: pcfg-pull-none-smt {
  bias-disable;
  input-schmitt-enable;
 };

 /omit-if-no-ref/
 pcfg_pull_none_drv_level_0_smt: pcfg-pull-none-drv-level-0-smt {
  bias-disable;
  drive-strength = <0>;
  input-schmitt-enable;
 };

 /omit-if-no-ref/
 pcfg_output_high: pcfg-output-high {
  output-high;
 };

 /omit-if-no-ref/
 pcfg_output_low: pcfg-output-low {
  output-low;
 };
};
# 8 "arch/arm/boot/dts/rv1126-pinctrl.dtsi" 2





&pinctrl {
 acodec {
  /omit-if-no-ref/
  acodec_pins: acodec-pins {
   rockchip,pins =

    <3 25 3 &pcfg_pull_none>,

    <3 31 3 &pcfg_pull_none>,

    <3 28 3 &pcfg_pull_none>,

    <3 24 3 &pcfg_pull_none>,

    <3 30 3 &pcfg_pull_none>,

    <3 29 3 &pcfg_pull_none>,

    <3 27 3 &pcfg_pull_none>;
  };
 };
 auddsm {
  /omit-if-no-ref/
  auddsm_pins: auddsm-pins {
   rockchip,pins =

    <3 27 5 &pcfg_pull_none>,

    <3 29 5 &pcfg_pull_none>,

    <4 0 5 &pcfg_pull_none>,

    <4 1 5 &pcfg_pull_none>;
  };
 };
 audpwm {
  /omit-if-no-ref/
  audpwmm0_pins: audpwmm0-pins {
   rockchip,pins =

    <4 0 3 &pcfg_pull_none>,

    <4 1 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  audpwmm1_pins: audpwmm1-pins {
   rockchip,pins =

    <3 27 4 &pcfg_pull_none>,

    <3 29 4 &pcfg_pull_none>;
  };
 };
 can {
  /omit-if-no-ref/
  canm0_pins: canm0-pins {
   rockchip,pins =

    <3 0 3 &pcfg_pull_none>,

    <3 1 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  canm1_pins: canm1-pins {
   rockchip,pins =

    <3 6 5 &pcfg_pull_none>,

    <3 7 5 &pcfg_pull_none>;
  };
 };
 cif {
  /omit-if-no-ref/
  cifm0_dvp_ctl: cifm0-dvp-ctl {
   rockchip,pins =

    <3 21 1 &pcfg_pull_none>,

    <3 22 1 &pcfg_pull_none>,

    <3 4 1 &pcfg_pull_none>,

    <3 14 1 &pcfg_pull_none>,

    <3 15 1 &pcfg_pull_none>,

    <3 16 1 &pcfg_pull_none>,

    <3 17 1 &pcfg_pull_none>,

    <3 18 1 &pcfg_pull_none>,

    <3 19 1 &pcfg_pull_none>,

    <3 5 1 &pcfg_pull_none>,

    <3 6 1 &pcfg_pull_none>,

    <3 7 1 &pcfg_pull_none>,

    <3 8 1 &pcfg_pull_none>,

    <3 9 1 &pcfg_pull_none>,

    <3 10 1 &pcfg_pull_none>,

    <3 11 1 &pcfg_pull_none>,

    <3 12 1 &pcfg_pull_none>,

    <3 13 1 &pcfg_pull_none>,

    <3 23 1 &pcfg_pull_none>,

    <3 20 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  cifm1_dvp_ctl: cifm1-dvp-ctl {
   rockchip,pins =

    <2 26 3 &pcfg_pull_none>,

    <2 25 3 &pcfg_pull_none>,

    <2 4 3 &pcfg_pull_none>,

    <2 18 3 &pcfg_pull_none>,

    <2 19 3 &pcfg_pull_none>,

    <2 20 3 &pcfg_pull_none>,

    <2 21 3 &pcfg_pull_none>,

    <2 22 3 &pcfg_pull_none>,

    <2 23 3 &pcfg_pull_none>,

    <2 5 3 &pcfg_pull_none>,

    <2 6 3 &pcfg_pull_none>,

    <2 11 3 &pcfg_pull_none>,

    <2 12 3 &pcfg_pull_none>,

    <2 13 3 &pcfg_pull_none>,

    <2 14 3 &pcfg_pull_none>,

    <2 15 3 &pcfg_pull_none>,

    <2 16 3 &pcfg_pull_none>,

    <2 17 3 &pcfg_pull_none>,

    <2 27 3 &pcfg_pull_none>,

    <2 24 3 &pcfg_pull_none>;
  };
 };
 clk {
  /omit-if-no-ref/
  clkm0_out_ethernet: clkm0-out-ethernet {
   rockchip,pins =

    <3 21 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  clkm1_out_ethernet: clkm1-out-ethernet {
   rockchip,pins =

    <2 21 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  clk_32k: clk-32k {
   rockchip,pins =

    <0 2 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  clk_ref: clk-ref {
   rockchip,pins =

    <0 0 1 &pcfg_pull_none>;
  };
 };
 emmc {
  /omit-if-no-ref/
  emmc_rstnout: emmc-rstnout {
   rockchip,pins =

    <1 3 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  emmc_bus8: emmc-bus8 {
   rockchip,pins =

    <0 20 2 &pcfg_pull_up_drv_level_2>,

    <0 21 2 &pcfg_pull_up_drv_level_2>,

    <0 22 2 &pcfg_pull_up_drv_level_2>,

    <0 23 2 &pcfg_pull_up_drv_level_2>,

    <0 24 2 &pcfg_pull_up_drv_level_2>,

    <0 25 2 &pcfg_pull_up_drv_level_2>,

    <0 26 2 &pcfg_pull_up_drv_level_2>,

    <0 27 2 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  emmc_clk: emmc-clk {
   rockchip,pins =

    <0 31 2 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  emmc_cmd: emmc-cmd {
   rockchip,pins =

    <0 29 2 &pcfg_pull_up_drv_level_2>;
  };
 };
 flash {
  /omit-if-no-ref/
  flash_pins: flash-pins {
   rockchip,pins =

    <1 0 1 &pcfg_pull_none>,

    <0 31 1 &pcfg_pull_none>,

    <0 28 1 &pcfg_pull_none>,

    <0 20 1 &pcfg_pull_none>,

    <0 21 1 &pcfg_pull_none>,

    <0 22 1 &pcfg_pull_none>,

    <0 23 1 &pcfg_pull_none>,

    <0 24 1 &pcfg_pull_none>,

    <0 25 1 &pcfg_pull_none>,

    <0 26 1 &pcfg_pull_none>,

    <0 27 1 &pcfg_pull_none>,

    <1 2 1 &pcfg_pull_none>,

    <1 1 1 &pcfg_pull_none>,

    <1 21 4 &pcfg_pull_none>,

    <1 20 4 &pcfg_pull_none>,

    <0 11 1 &pcfg_pull_none>,

    <1 3 1 &pcfg_pull_none>,

    <0 29 1 &pcfg_pull_none>;
  };
 };
 fspi {
  /omit-if-no-ref/
  fspi_cs1: fspi-cs1 {
   rockchip,pins =

    <0 25 3 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  fspi_pins: fspi-pins {
   rockchip,pins =

    <1 3 3 &pcfg_pull_down>,

    <0 28 3 &pcfg_pull_up>,

    <1 0 3 &pcfg_pull_up>,

    <1 1 3 &pcfg_pull_up>,

    <0 30 3 &pcfg_pull_up>,

    <1 2 3 &pcfg_pull_up>;
  };
 };
 i2c0 {
  /omit-if-no-ref/
  i2c0_xfer: i2c0-xfer {
   rockchip,pins =

    <0 12 1 &pcfg_pull_none_drv_level_0_smt>,

    <0 13 1 &pcfg_pull_none_drv_level_0_smt>;
  };
 };
 i2c1 {
  /omit-if-no-ref/
  i2c1_xfer: i2c1-xfer {
   rockchip,pins =

    <1 27 1 &pcfg_pull_none_drv_level_0_smt>,

    <1 26 1 &pcfg_pull_none_drv_level_0_smt>;
  };
 };
 i2c2 {
  /omit-if-no-ref/
  i2c2_xfer: i2c2-xfer {
   rockchip,pins =

    <0 18 1 &pcfg_pull_none_drv_level_0_smt>,

    <0 19 1 &pcfg_pull_none_drv_level_0_smt>;
  };
 };
 i2c3 {
  /omit-if-no-ref/
  i2c3m0_xfer: i2c3m0-xfer {
   rockchip,pins =

    <3 4 5 &pcfg_pull_none_drv_level_0_smt>,

    <3 5 5 &pcfg_pull_none_drv_level_0_smt>;
  };
  /omit-if-no-ref/
  i2c3m1_xfer: i2c3m1-xfer {
   rockchip,pins =

    <2 28 7 &pcfg_pull_none_drv_level_0_smt>,

    <2 29 7 &pcfg_pull_none_drv_level_0_smt>;
  };
  /omit-if-no-ref/
  i2c3m2_xfer: i2c3m2-xfer {
   rockchip,pins =

    <1 30 3 &pcfg_pull_none_drv_level_0_smt>,

    <1 31 3 &pcfg_pull_none_drv_level_0_smt>;
  };
 };
 i2c4 {
  /omit-if-no-ref/
  i2c4m0_xfer: i2c4m0-xfer {
   rockchip,pins =

    <3 0 7 &pcfg_pull_none_drv_level_0_smt>,

    <3 1 7 &pcfg_pull_none_drv_level_0_smt>;
  };
  /omit-if-no-ref/
  i2c4m1_xfer: i2c4m1-xfer {
   rockchip,pins =

    <4 0 4 &pcfg_pull_none_drv_level_0_smt>,

    <4 1 4 &pcfg_pull_none_drv_level_0_smt>;
  };
 };
 i2c5 {
  /omit-if-no-ref/
  i2c5m0_xfer: i2c5m0-xfer {
   rockchip,pins =

    <2 5 7 &pcfg_pull_none_drv_level_0_smt>,

    <2 11 7 &pcfg_pull_none_drv_level_0_smt>;
  };
  /omit-if-no-ref/
  i2c5m1_xfer: i2c5m1-xfer {
   rockchip,pins =

    <3 8 5 &pcfg_pull_none_drv_level_0_smt>,

    <3 9 5 &pcfg_pull_none_drv_level_0_smt>;
  };
  /omit-if-no-ref/
  i2c5m2_xfer: i2c5m2-xfer {
   rockchip,pins =

    <1 24 4 &pcfg_pull_none_drv_level_0_smt>,

    <1 25 4 &pcfg_pull_none_drv_level_0_smt>;
  };
 };
 i2s0 {
  /omit-if-no-ref/
  i2s0m0_lrck_rx: i2s0m0-lrck-rx {
   rockchip,pins =

    <3 28 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_lrck_tx: i2s0m0-lrck-tx {
   rockchip,pins =

    <3 27 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_mclk: i2s0m0-mclk {
   rockchip,pins =

    <3 26 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_sclk_rx: i2s0m0-sclk-rx {
   rockchip,pins =

    <3 25 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_sclk_tx: i2s0m0-sclk-tx {
   rockchip,pins =

    <3 24 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_sdi0: i2s0m0-sdi0 {
   rockchip,pins =

    <3 30 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_sdo0: i2s0m0-sdo0 {
   rockchip,pins =

    <3 29 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_sdo1_sdi3: i2s0m0-sdo1-sdi3 {
   rockchip,pins =

    <3 31 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_sdo2_sdi2: i2s0m0-sdo2-sdi2 {
   rockchip,pins =

    <4 0 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m0_sdo3_sdi1: i2s0m0-sdo3-sdi1 {
   rockchip,pins =

    <4 1 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_lrck_rx: i2s0m1-lrck-rx {
   rockchip,pins =

    <3 10 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_lrck_tx: i2s0m1-lrck-tx {
   rockchip,pins =

    <3 5 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_mclk: i2s0m1-mclk {
   rockchip,pins =

    <3 8 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_sclk_rx: i2s0m1-sclk-rx {
   rockchip,pins =

    <3 9 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_sclk_tx: i2s0m1-sclk-tx {
   rockchip,pins =

    <3 4 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_sdi0: i2s0m1-sdi0 {
   rockchip,pins =

    <3 7 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_sdo0: i2s0m1-sdo0 {
   rockchip,pins =

    <3 6 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_sdo1_sdi3: i2s0m1-sdo1-sdi3 {
   rockchip,pins =

    <3 11 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_sdo2_sdi2: i2s0m1-sdo2-sdi2 {
   rockchip,pins =

    <3 12 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s0m1_sdo3_sdi1: i2s0m1-sdo3-sdi1 {
   rockchip,pins =

    <3 13 3 &pcfg_pull_none>;
  };
 };
 i2s1 {
  /omit-if-no-ref/
  i2s1m0_lrck: i2s1m0-lrck {
   rockchip,pins =

    <1 0 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m0_mclk: i2s1m0-mclk {
   rockchip,pins =

    <0 28 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m0_sclk: i2s1m0-sclk {
   rockchip,pins =

    <1 1 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m0_sdi: i2s1m0-sdi {
   rockchip,pins =

    <1 2 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m0_sdo: i2s1m0-sdo {
   rockchip,pins =

    <0 30 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m1_lrck: i2s1m1-lrck {
   rockchip,pins =

    <1 31 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m1_mclk: i2s1m1-mclk {
   rockchip,pins =

    <1 29 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m1_sclk: i2s1m1-sclk {
   rockchip,pins =

    <1 30 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m1_sdi: i2s1m1-sdi {
   rockchip,pins =

    <2 0 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m1_sdo: i2s1m1-sdo {
   rockchip,pins =

    <2 1 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m2_lrck: i2s1m2-lrck {
   rockchip,pins =

    <2 26 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m2_mclk: i2s1m2-mclk {
   rockchip,pins =

    <2 23 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m2_sclk: i2s1m2-sclk {
   rockchip,pins =

    <2 25 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m2_sdi: i2s1m2-sdi {
   rockchip,pins =

    <2 27 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s1m2_sdo: i2s1m2-sdo {
   rockchip,pins =

    <2 24 6 &pcfg_pull_none>;
  };
 };
 i2s2 {
  /omit-if-no-ref/
  i2s2m0_lrck: i2s2m0-lrck {
   rockchip,pins =

    <1 23 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m0_mclk: i2s2m0-mclk {
   rockchip,pins =

    <1 24 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m0_sclk: i2s2m0-sclk {
   rockchip,pins =

    <1 22 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m0_sdi: i2s2m0-sdi {
   rockchip,pins =

    <1 21 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m0_sdo: i2s2m0-sdo {
   rockchip,pins =

    <1 20 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m1_lrck: i2s2m1-lrck {
   rockchip,pins =

    <2 10 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m1_mclk: i2s2m1-mclk {
   rockchip,pins =

    <2 11 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m1_sclk: i2s2m1-sclk {
   rockchip,pins =

    <2 9 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m1_sdi: i2s2m1-sdi {
   rockchip,pins =

    <2 8 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  i2s2m1_sdo: i2s2m1-sdo {
   rockchip,pins =

    <2 7 2 &pcfg_pull_none>;
  };
 };
 lcdc {
  /omit-if-no-ref/
  lcdc_ctl: lcdc-ctl {
   rockchip,pins =

    <2 31 1 &pcfg_pull_none_drv_level_8>,

    <2 4 1 &pcfg_pull_none_drv_level_2>,

    <2 5 1 &pcfg_pull_none_drv_level_2>,

    <2 6 1 &pcfg_pull_none_drv_level_2>,

    <2 7 1 &pcfg_pull_none_drv_level_2>,

    <2 8 1 &pcfg_pull_none_drv_level_2>,

    <2 9 1 &pcfg_pull_none_drv_level_2>,

    <2 10 1 &pcfg_pull_none_drv_level_2>,

    <2 11 1 &pcfg_pull_none_drv_level_2>,

    <2 12 1 &pcfg_pull_none_drv_level_2>,

    <2 13 1 &pcfg_pull_none_drv_level_2>,

    <2 14 1 &pcfg_pull_none_drv_level_2>,

    <2 15 1 &pcfg_pull_none_drv_level_2>,

    <2 16 1 &pcfg_pull_none_drv_level_2>,

    <2 17 1 &pcfg_pull_none_drv_level_2>,

    <2 18 1 &pcfg_pull_none_drv_level_2>,

    <2 19 1 &pcfg_pull_none_drv_level_2>,

    <2 20 1 &pcfg_pull_none_drv_level_2>,

    <2 21 1 &pcfg_pull_none_drv_level_2>,

    <2 22 1 &pcfg_pull_none_drv_level_2>,

    <2 23 1 &pcfg_pull_none_drv_level_2>,

    <2 24 1 &pcfg_pull_none_drv_level_2>,

    <2 25 1 &pcfg_pull_none_drv_level_2>,

    <2 26 1 &pcfg_pull_none_drv_level_2>,

    <2 27 1 &pcfg_pull_none_drv_level_2>,

    <2 28 1 &pcfg_pull_none_drv_level_2>,

    <2 29 1 &pcfg_pull_none_drv_level_2>,

    <2 30 1 &pcfg_pull_none_drv_level_2>;
  };
 };
 mcu {
  /omit-if-no-ref/
  mcu_pins: mcu-pins {
   rockchip,pins =

    <1 6 4 &pcfg_pull_none>,

    <1 9 4 &pcfg_pull_none>,

    <1 8 4 &pcfg_pull_none>,

    <1 7 4 &pcfg_pull_none>,

    <1 5 4 &pcfg_pull_none>;
  };
 };
 mipicsi {
  /omit-if-no-ref/
  mipicsi_clk0: mipicsi-clk0 {
   rockchip,pins =

    <2 3 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  mipicsi_clk1: mipicsi-clk1 {
   rockchip,pins =

    <2 2 1 &pcfg_pull_none>;
  };
 };
 pdm {
  /omit-if-no-ref/
  pdmm0_clk: pdmm0-clk {
   rockchip,pins =

    <3 28 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm0_clk1: pdmm0-clk1 {
   rockchip,pins =

    <3 25 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm0_sdi0: pdmm0-sdi0 {
   rockchip,pins =

    <3 30 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm0_sdi1: pdmm0-sdi1 {
   rockchip,pins =

    <4 1 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm0_sdi2: pdmm0-sdi2 {
   rockchip,pins =

    <4 0 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm0_sdi3: pdmm0-sdi3 {
   rockchip,pins =

    <3 31 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm1_clk: pdmm1-clk {
   rockchip,pins =

    <3 16 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm1_clk1: pdmm1-clk1 {
   rockchip,pins =

    <3 19 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm1_sdi0: pdmm1-sdi0 {
   rockchip,pins =

    <3 17 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm1_sdi1: pdmm1-sdi1 {
   rockchip,pins =

    <3 18 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm1_sdi2: pdmm1-sdi2 {
   rockchip,pins =

    <3 14 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pdmm1_sdi3: pdmm1-sdi3 {
   rockchip,pins =

    <3 15 3 &pcfg_pull_none>;
  };
 };
 pmic {
  /omit-if-no-ref/
  pmic_pins: pmic-pins {
   rockchip,pins =

    <0 9 1 &pcfg_pull_none>,

    <0 10 1 &pcfg_pull_none>;
  };
 };
 pmu {
  /omit-if-no-ref/
  pmu_pins: pmu-pins {
   rockchip,pins =

    <0 17 1 &pcfg_pull_none>;
  };
 };
 prelight {
  /omit-if-no-ref/
  prelight_pins: prelight-pins {
   rockchip,pins =

    <1 22 4 &pcfg_pull_none>;
  };
 };
 pwm0 {
  /omit-if-no-ref/
  pwm0m0_pins: pwm0m0-pins {
   rockchip,pins =

    <0 14 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm0m1_pins: pwm0m1-pins {
   rockchip,pins =

    <2 11 5 &pcfg_pull_none>;
  };
 };
 pwm1 {
  /omit-if-no-ref/
  pwm1m0_pins: pwm1m0-pins {
   rockchip,pins =

    <0 15 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm1m1_pins: pwm1m1-pins {
   rockchip,pins =

    <2 10 5 &pcfg_pull_none>;
  };
 };
 pwm2 {
  /omit-if-no-ref/
  pwm2m0_pins: pwm2m0-pins {
   rockchip,pins =

    <0 16 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm2m1_pins: pwm2m1-pins {
   rockchip,pins =

    <2 9 5 &pcfg_pull_none>;
  };
 };
 pwm3 {
  /omit-if-no-ref/
  pwm3m0_pins: pwm3m0-pins {
   rockchip,pins =

    <0 17 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm3m1_pins: pwm3m1-pins {
   rockchip,pins =

    <2 8 5 &pcfg_pull_none>;
  };
 };
 pwm4 {
  /omit-if-no-ref/
  pwm4m0_pins: pwm4m0-pins {
   rockchip,pins =

    <0 18 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm4m1_pins: pwm4m1-pins {
   rockchip,pins =

    <2 7 5 &pcfg_pull_none>;
  };
 };
 pwm5 {
  /omit-if-no-ref/
  pwm5m0_pins: pwm5m0-pins {
   rockchip,pins =

    <0 19 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm5m1_pins: pwm5m1-pins {
   rockchip,pins =

    <2 6 5 &pcfg_pull_none>;
  };
 };
 pwm6 {
  /omit-if-no-ref/
  pwm6m0_pins: pwm6m0-pins {
   rockchip,pins =

    <0 10 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm6m1_pins: pwm6m1-pins {
   rockchip,pins =

    <2 28 5 &pcfg_pull_none>;
  };
 };
 pwm7 {
  /omit-if-no-ref/
  pwm7m0_pins: pwm7m0-pins {
   rockchip,pins =

    <0 9 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm7m1_pins: pwm7m1-pins {
   rockchip,pins =

    <3 0 5 &pcfg_pull_none>;
  };
 };
 pwm8 {
  /omit-if-no-ref/
  pwm8m0_pins: pwm8m0-pins {
   rockchip,pins =

    <3 4 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm8m1_pins: pwm8m1-pins {
   rockchip,pins =

    <2 31 5 &pcfg_pull_none>;
  };
 };
 pwm9 {
  /omit-if-no-ref/
  pwm9m0_pins: pwm9m0-pins {
   rockchip,pins =

    <3 5 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm9m1_pins: pwm9m1-pins {
   rockchip,pins =

    <2 30 5 &pcfg_pull_none>;
  };
 };
 pwm10 {
  /omit-if-no-ref/
  pwm10m0_pins: pwm10m0-pins {
   rockchip,pins =

    <3 6 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm10m1_pins: pwm10m1-pins {
   rockchip,pins =

    <2 29 5 &pcfg_pull_none>;
  };
 };
 pwm11 {
  /omit-if-no-ref/
  pwm11m0_pins: pwm11m0-pins {
   rockchip,pins =

    <3 7 6 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  pwm11m1_pins: pwm11m1-pins {
   rockchip,pins =

    <3 1 5 &pcfg_pull_none>;
  };
 };
 rgmii {
  /omit-if-no-ref/
  rgmiim0_miim: rgmiim0-miim {
   rockchip,pins =

    <3 20 2 &pcfg_pull_none>,

    <3 19 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  rgmiim0_rxer: rgmiim0-rxer {
   rockchip,pins =

    <3 18 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  rgmiim0_bus2: rgmiim0-bus2 {
   rockchip,pins =

    <3 14 2 &pcfg_pull_none>,

    <3 15 2 &pcfg_pull_none>,

    <3 17 2 &pcfg_pull_none>,

    <3 11 2 &pcfg_pull_none_drv_level_3>,

    <3 12 2 &pcfg_pull_none_drv_level_3>,

    <3 13 2 &pcfg_pull_none_drv_level_3>;
  };
  /omit-if-no-ref/
  rgmiim0_bus4: rgmiim0-bus4 {
   rockchip,pins =

    <3 23 2 &pcfg_pull_none>,

    <3 7 2 &pcfg_pull_none>,

    <3 8 2 &pcfg_pull_none>,

    <3 22 2 &pcfg_pull_none_drv_level_3>,

    <3 9 2 &pcfg_pull_none_drv_level_3>,

    <3 10 2 &pcfg_pull_none_drv_level_3>;
  };
  /omit-if-no-ref/
  rgmiim0_mclkinout: rgmiim0-mclkinout {
   rockchip,pins =

    <3 16 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  rgmiim1_miim: rgmiim1-miim {
   rockchip,pins =

    <2 18 2 &pcfg_pull_none>,

    <2 17 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  rgmiim1_rxer: rgmiim1-rxer {
   rockchip,pins =

    <2 16 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  rgmiim1_bus2: rgmiim1-bus2 {
   rockchip,pins =

    <2 13 2 &pcfg_pull_none>,

    <2 14 2 &pcfg_pull_none>,

    <2 12 2 &pcfg_pull_none>,

    <2 19 2 &pcfg_pull_none_drv_level_3>,

    <2 20 2 &pcfg_pull_none_drv_level_3>,

    <2 22 2 &pcfg_pull_none_drv_level_3>;
  };
  /omit-if-no-ref/
  rgmiim1_bus4: rgmiim1-bus4 {
   rockchip,pins =

    <2 27 2 &pcfg_pull_none>,

    <2 23 2 &pcfg_pull_none>,

    <2 24 2 &pcfg_pull_none>,

    <2 26 2 &pcfg_pull_none_drv_level_3>,

    <2 25 2 &pcfg_pull_none_drv_level_3>,

    <2 4 2 &pcfg_pull_none_drv_level_3>;
  };
  /omit-if-no-ref/
  rgmiim1_mclkinout: rgmiim1-mclkinout {
   rockchip,pins =

    <2 15 2 &pcfg_pull_none>;
  };
 };
 sdio {
  /omit-if-no-ref/
  sdio_bus4: sdio-bus4 {
   rockchip,pins =

    <1 12 1 &pcfg_pull_up_drv_level_2>,

    <1 13 1 &pcfg_pull_up_drv_level_2>,

    <1 14 1 &pcfg_pull_up_drv_level_2>,

    <1 15 1 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  sdio_clk: sdio-clk {
   rockchip,pins =

    <1 10 1 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  sdio_cmd: sdio-cmd {
   rockchip,pins =

    <1 11 1 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  sdio_det: sdio-det {
   rockchip,pins =

    <1 24 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  sdio_pwr: sdio-pwr {
   rockchip,pins =

    <1 25 2 &pcfg_pull_none>;
  };
 };
 sdmmc0 {
  /omit-if-no-ref/
  sdmmc0_bus4: sdmmc0-bus4 {
   rockchip,pins =

    <1 4 1 &pcfg_pull_up_drv_level_2>,

    <1 5 1 &pcfg_pull_up_drv_level_2>,

    <1 6 1 &pcfg_pull_up_drv_level_2>,

    <1 7 1 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  sdmmc0_clk: sdmmc0-clk {
   rockchip,pins =

    <1 8 1 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  sdmmc0_cmd: sdmmc0-cmd {
   rockchip,pins =

    <1 9 1 &pcfg_pull_up_drv_level_2>;
  };
  /omit-if-no-ref/
  sdmmc0_det: sdmmc0-det {
   rockchip,pins =

    <0 3 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  sdmmc0_pwr: sdmmc0-pwr {
   rockchip,pins =

    <0 16 1 &pcfg_pull_none>;
  };
 };
 spi0 {
  /omit-if-no-ref/
  spi0m0_pins: spi0m0-pins {
   rockchip,pins =

    <0 8 1 &pcfg_pull_up_drv_level_0>,

    <0 7 1 &pcfg_pull_up_drv_level_0>,

    <0 6 1 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m0_cs0: spi0m0-cs0 {
   rockchip,pins =

    <0 5 1 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m0_cs1: spi0m0-cs1 {
   rockchip,pins =

    <0 4 1 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m1_pins: spi0m1-pins {
   rockchip,pins =

    <2 1 1 &pcfg_pull_up_drv_level_0>,

    <1 31 1 &pcfg_pull_up_drv_level_0>,

    <1 30 1 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m1_cs0: spi0m1-cs0 {
   rockchip,pins =

    <2 0 1 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m1_cs1: spi0m1-cs1 {
   rockchip,pins =

    <1 29 1 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m2_pins: spi0m2-pins {
   rockchip,pins =

    <2 10 6 &pcfg_pull_up_drv_level_0>,

    <2 9 6 &pcfg_pull_up_drv_level_0>,

    <2 8 6 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m2_cs0: spi0m2-cs0 {
   rockchip,pins =

    <2 7 6 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi0m2_cs1: spi0m2-cs1 {
   rockchip,pins =

    <2 11 6 &pcfg_pull_up_drv_level_0>;
  };
 };
 spi1 {
  /omit-if-no-ref/
  spi1m0_pins: spi1m0-pins {
   rockchip,pins =

    <3 16 5 &pcfg_pull_up_drv_level_0>,

    <3 15 5 &pcfg_pull_up_drv_level_0>,

    <3 14 5 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m0_cs0: spi1m0-cs0 {
   rockchip,pins =

    <3 13 5 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m0_cs1: spi1m0-cs1 {
   rockchip,pins =

    <3 12 5 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m1_pins: spi1m1-pins {
   rockchip,pins =

    <1 22 3 &pcfg_pull_up_drv_level_0>,

    <1 21 3 &pcfg_pull_up_drv_level_0>,

    <1 20 3 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m1_cs0: spi1m1-cs0 {
   rockchip,pins =

    <1 23 3 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m1_cs1: spi1m1-cs1 {
   rockchip,pins =

    <1 24 3 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m2_pins: spi1m2-pins {
   rockchip,pins =

    <2 29 6 &pcfg_pull_up_drv_level_0>,

    <2 31 6 &pcfg_pull_up_drv_level_0>,

    <2 30 6 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m2_cs0: spi1m2-cs0 {
   rockchip,pins =

    <2 28 6 &pcfg_pull_up_drv_level_0>;
  };
  /omit-if-no-ref/
  spi1m2_cs1: spi1m2-cs1 {
   rockchip,pins =

    <3 0 6 &pcfg_pull_up_drv_level_0>;
  };
 };
 tsadc {
  /omit-if-no-ref/
  tsadcm0_shut: tsadcm0-shut {
   rockchip,pins =

    <0 1 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  tsadcm1_shut: tsadcm1-shut {
   rockchip,pins =

    <0 10 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  tsadc_shutorg: tsadc-shutorg {
   rockchip,pins =

    <0 1 2 &pcfg_pull_none>;
  };
 };
 uart0 {
  /omit-if-no-ref/
  uart0_xfer: uart0-xfer {
   rockchip,pins =

    <1 18 1 &pcfg_pull_up>,

    <1 19 1 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart0_ctsn: uart0-ctsn {
   rockchip,pins =

    <1 17 1 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart0_rtsn: uart0-rtsn {
   rockchip,pins =

    <1 16 1 &pcfg_pull_none>;
  };
 };
 uart1 {
  /omit-if-no-ref/
  uart1m0_xfer: uart1m0-xfer {
   rockchip,pins =

    <0 15 2 &pcfg_pull_up>,

    <0 14 2 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart1m0_ctsn: uart1m0-ctsn {
   rockchip,pins =

    <0 17 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart1m0_rtsn: uart1m0-rtsn {
   rockchip,pins =

    <0 16 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart1m1_xfer: uart1m1-xfer {
   rockchip,pins =

    <1 25 5 &pcfg_pull_up>,

    <1 24 5 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart1m1_ctsn: uart1m1-ctsn {
   rockchip,pins =

    <1 23 5 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart1m1_rtsn: uart1m1-rtsn {
   rockchip,pins =

    <1 22 5 &pcfg_pull_none>;
  };
 };
 uart2 {
  /omit-if-no-ref/
  uart2m0_xfer: uart2m0-xfer {
   rockchip,pins =

    <1 4 3 &pcfg_pull_up>,

    <1 5 3 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart2m1_xfer: uart2m1-xfer {
   rockchip,pins =

    <3 3 1 &pcfg_pull_up>,

    <3 2 1 &pcfg_pull_up>;
  };
 };
 uart3 {
  /omit-if-no-ref/
  uart3m0_xfer: uart3m0-xfer {
   rockchip,pins =

    <3 23 4 &pcfg_pull_up>,

    <3 22 4 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart3m0_ctsn: uart3m0-ctsn {
   rockchip,pins =

    <3 21 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart3m0_rtsn: uart3m0-rtsn {
   rockchip,pins =

    <3 20 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart3m1_xfer: uart3m1-xfer {
   rockchip,pins =

    <1 6 2 &pcfg_pull_up>,

    <1 7 2 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart3m1_ctsn: uart3m1-ctsn {
   rockchip,pins =

    <1 9 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart3m1_rtsn: uart3m1-rtsn {
   rockchip,pins =

    <1 8 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart3m2_xfer: uart3m2-xfer {
   rockchip,pins =

    <3 1 4 &pcfg_pull_up>,

    <3 0 4 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart3m2_ctsn: uart3m2-ctsn {
   rockchip,pins =

    <2 31 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart3m2_rtsn: uart3m2-rtsn {
   rockchip,pins =

    <2 30 4 &pcfg_pull_none>;
  };
 };
 uart4 {
  /omit-if-no-ref/
  uart4m0_xfer: uart4m0-xfer {
   rockchip,pins =

    <3 5 4 &pcfg_pull_up>,

    <3 4 4 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart4m0_ctsn: uart4m0-ctsn {
   rockchip,pins =

    <3 11 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart4m0_rtsn: uart4m0-rtsn {
   rockchip,pins =

    <3 10 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart4m1_xfer: uart4m1-xfer {
   rockchip,pins =

    <2 7 4 &pcfg_pull_up>,

    <2 6 4 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart4m1_ctsn: uart4m1-ctsn {
   rockchip,pins =

    <2 5 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart4m1_rtsn: uart4m1-rtsn {
   rockchip,pins =

    <2 4 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart4m2_xfer: uart4m2-xfer {
   rockchip,pins =

    <1 28 3 &pcfg_pull_up>,

    <1 29 3 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart4m2_ctsn: uart4m2-ctsn {
   rockchip,pins =

    <1 27 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart4m2_rtsn: uart4m2-rtsn {
   rockchip,pins =

    <1 26 3 &pcfg_pull_none>;
  };
 };
 uart5 {
  /omit-if-no-ref/
  uart5m0_xfer: uart5m0-xfer {
   rockchip,pins =

    <3 7 4 &pcfg_pull_up>,

    <3 6 4 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart5m0_ctsn: uart5m0-ctsn {
   rockchip,pins =

    <3 9 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart5m0_rtsn: uart5m0-rtsn {
   rockchip,pins =

    <3 8 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart5m1_xfer: uart5m1-xfer {
   rockchip,pins =

    <2 9 4 &pcfg_pull_up>,

    <2 8 4 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart5m1_ctsn: uart5m1-ctsn {
   rockchip,pins =

    <2 11 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart5m1_rtsn: uart5m1-rtsn {
   rockchip,pins =

    <2 10 4 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart5m2_xfer: uart5m2-xfer {
   rockchip,pins =

    <2 1 3 &pcfg_pull_up>,

    <2 0 3 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  uart5m2_ctsn: uart5m2-ctsn {
   rockchip,pins =

    <2 3 3 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  uart5m2_rtsn: uart5m2-rtsn {
   rockchip,pins =

    <2 2 3 &pcfg_pull_none>;
  };
 };
};
&pinctrl {
 gpio {
  /omit-if-no-ref/
  uart0_rtsn_gpio: uart0-rts-pin {
   rockchip,pins =
    <1 16 0 &pcfg_pull_none>;
  };
 };
 pwm-pull-down {
  /omit-if-no-ref/
  pwm0m0_pins_pull_down: pwm0m0-pins {
   rockchip,pins =

    <0 14 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm0m1_pins_pull_down: pwm0m1-pins {
   rockchip,pins =

    <2 11 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm1m0_pins_pull_down: pwm1m0-pins {
   rockchip,pins =

    <0 15 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm1m1_pins_pull_down: pwm1m1-pins {
   rockchip,pins =

    <2 10 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm2m0_pins_pull_down: pwm2m0-pins {
   rockchip,pins =

    <0 16 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm2m1_pins_pull_down: pwm2m1-pins {
   rockchip,pins =

    <2 9 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm3m0_pins_pull_down: pwm3m0-pins {
   rockchip,pins =

    <0 17 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm3m1_pins_pull_down: pwm3m1-pins {
   rockchip,pins =

    <2 8 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm4m0_pins_pull_down: pwm4m0-pins {
   rockchip,pins =

    <0 18 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm4m1_pins_pull_down: pwm4m1-pins {
   rockchip,pins =

    <2 7 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm5m0_pins_pull_down: pwm5m0-pins {
   rockchip,pins =

    <0 19 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm5m1_pins_pull_down: pwm5m1-pins {
   rockchip,pins =

    <2 6 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm6m0_pins_pull_down: pwm6m0-pins {
   rockchip,pins =

    <0 10 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm6m1_pins_pull_up: pwm6m1-pins-pull-up {
   rockchip,pins =

    <2 28 5 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  pwm7m0_pins_pull_down: pwm7m0-pins {
   rockchip,pins =

    <0 9 3 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm7m1_pins_pull_up: pwm7m1-pins-pull-up {
   rockchip,pins =

    <3 0 5 &pcfg_pull_up>;
  };
  /omit-if-no-ref/
  pwm8m0_pins_pull_down: pwm8m0-pins {
   rockchip,pins =

    <3 4 6 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm8m1_pins_pull_down: pwm8m1-pins {
   rockchip,pins =

    <2 31 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm9m0_pins_pull_down: pwm9m0-pins {
   rockchip,pins =

    <3 5 6 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm9m1_pins_pull_down: pwm9m1-pins {
   rockchip,pins =

    <2 30 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm10m0_pins_pull_down: pwm10m0-pins {
   rockchip,pins =

    <3 6 6 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm10m1_pins_pull_down: pwm10m1-pins {
   rockchip,pins =

    <2 29 5 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm11m0_pins_pull_down: pwm11m0-pins {
   rockchip,pins =

    <3 7 6 &pcfg_pull_down>;
  };
  /omit-if-no-ref/
  pwm11m1_pins_pull_down: pwm11m1-pins {
   rockchip,pins =

    <3 1 5 &pcfg_pull_down>;
  };
 };
 spi0-hs {
  /omit-if-no-ref/
  spi0m0_pins_hs: spi0m0-pins {
   rockchip,pins =

    <0 8 1 &pcfg_pull_up_drv_level_1>,

    <0 7 1 &pcfg_pull_up_drv_level_1>,

    <0 6 1 &pcfg_pull_up_drv_level_1>;
  };
  /omit-if-no-ref/
  spi0m1_pins_hs: spi0m1-pins {
   rockchip,pins =

    <2 1 1 &pcfg_pull_up_drv_level_1>,

    <1 31 1 &pcfg_pull_up_drv_level_1>,

    <1 30 1 &pcfg_pull_up_drv_level_1>;
  };
  /omit-if-no-ref/
  spi0m2_pins_hs: spi0m2-pins {
   rockchip,pins =

    <2 10 6 &pcfg_pull_up_drv_level_1>,

    <2 9 6 &pcfg_pull_up_drv_level_1>,

    <2 8 6 &pcfg_pull_up_drv_level_1>;
  };
 };
 spi1-hs {
  /omit-if-no-ref/
  spi1m0_pins_hs: spi1m0-pins {
   rockchip,pins =

    <3 16 5 &pcfg_pull_up_drv_level_1>,

    <3 15 5 &pcfg_pull_up_drv_level_1>,

    <3 14 5 &pcfg_pull_up_drv_level_1>;
  };
  /omit-if-no-ref/
  spi1m1_pins_hs: spi1m1-pins {
   rockchip,pins =

    <1 22 3 &pcfg_pull_up_drv_level_1>,

    <1 21 3 &pcfg_pull_up_drv_level_1>,

    <1 20 3 &pcfg_pull_up_drv_level_1>;
  };
  /omit-if-no-ref/
  spi1m2_pins_hs: spi1m2-pins {
   rockchip,pins =

    <2 29 6 &pcfg_pull_up_drv_level_1>,

    <2 31 6 &pcfg_pull_up_drv_level_1>,

    <2 30 6 &pcfg_pull_up_drv_level_1>;
  };
 };
 gmac_clk {
  /omit-if-no-ref/
  rgmiim0_mclkinout_level0: rgmiim0_mclkinout-level0 {
   rockchip,pins =

    <3 16 2 &pcfg_pull_none_drv_level_0>;
  };
  /omit-if-no-ref/
  rgmiim0_mclkinout_level3: rgmiim0_mclkinout-level3 {
   rockchip,pins =

    <3 16 2 &pcfg_pull_none_drv_level_3>;
  };
  /omit-if-no-ref/
  rgmiim1_mclkinout_level0: rgmiim1_mclkinout-level0 {
   rockchip,pins =

    <2 15 2 &pcfg_pull_none_drv_level_0>;
  };
  /omit-if-no-ref/
  rgmiim1_mclkinout_level3: rgmiim1_mclkinout-level3 {
   rockchip,pins =

    <2 15 2 &pcfg_pull_none_drv_level_3>;
  };
 };
 rmii {
  /omit-if-no-ref/
  rmiim0_miim: rmiim0-miim {
   rockchip,pins =

    <3 20 2 &pcfg_pull_none_drv_level_0>,

    <3 19 2 &pcfg_pull_none>;
  };

  /omit-if-no-ref/
  rmiim0_bus2: rmiim0-bus2 {
   rockchip,pins =

    <3 14 2 &pcfg_pull_none>,

    <3 15 2 &pcfg_pull_none>,

    <3 17 2 &pcfg_pull_none>,

    <3 11 2 &pcfg_pull_none_drv_level_0>,

    <3 12 2 &pcfg_pull_none_drv_level_0>,

    <3 13 2 &pcfg_pull_none_drv_level_0>;
  };
  /omit-if-no-ref/
  rmiim1_miim: rmiim1-miim {
   rockchip,pins =

    <2 18 2 &pcfg_pull_none_drv_level_0>,

    <2 17 2 &pcfg_pull_none>;
  };
  /omit-if-no-ref/
  rmiim1_bus2: rmiim1-bus2 {
   rockchip,pins =

    <2 13 2 &pcfg_pull_none>,

    <2 14 2 &pcfg_pull_none>,

    <2 12 2 &pcfg_pull_none>,

    <2 19 2 &pcfg_pull_none_drv_level_0>,

    <2 20 2 &pcfg_pull_none_drv_level_0>,

    <2 22 2 &pcfg_pull_none_drv_level_0>;
  };
 };
};
// # 2660 "arch/arm/boot/dts/rv1126.dtsi" 2
// # 7 "arch/arm/boot/dts/rv1126-evb-ddr3-v13.dts" 2
// # 1 "arch/arm/boot/dts/rv1126-evb-v13.dtsi" 1





// # 1 "arch/arm/boot/dts/rv1126-evb-v12.dtsi" 1





// # 1 "arch/arm64/boot/dts/rockchip/rv1126bp-evb-v10.dtsi" 1





// # 1 "./scripts/dtc/include-prefixes/dt-bindings/display/drm_mipi_dsi.h" 1
// // # 7 "arch/arm64/boot/dts/rockchip/rv1126bp-evb-v10.dtsi" 2
// # 1 "./scripts/dtc/include-prefixes/dt-bindings/input/input.h" 1
// # 13 "./scripts/dtc/include-prefixes/dt-bindings/input/input.h"
// # 1 "./scripts/dtc/include-prefixes/dt-bindings/input/linux-event-codes.h" 1
// # 14 "./scripts/dtc/include-prefixes/dt-bindings/input/input.h" 2
// // # 8 "arch/arm64/boot/dts/rockchip/rv1126bp-evb-v10.dtsi" 2

/ {
 leds {
  compatible = "gpio-leds";

  system_led {
   gpios = <&gpio3 18 0>;
   default-state = "on";
  };
 };

    camera_power{
        compatible = "vispect,mrv220_camera_power";
        status = "okay";
        power-gpios = <&gpio3 8 0>;
 };

 adc-keys {
  compatible = "adc-keys";
  io-channels = <&saradc 0>;
  io-channel-names = "buttons";
  poll-interval = <100>;
  keyup-threshold-microvolt = <1800000>;

  esc-key {
   label = "esc";
   linux,code = <1>;
   press-threshold-microvolt = <0>;
  };

  right-key {
   label = "right";
   linux,code = <106>;
   press-threshold-microvolt = <400781>;
  };

  left-key {
   label = "left";
   linux,code = <105>;
   press-threshold-microvolt = <801562>;
  };

  menu-key {
   label = "menu";
   linux,code = <139>;
   press-threshold-microvolt = <1198828>;
  };
 };

 backlight: backlight {
  compatible = "pwm-backlight";
  pwms = <&pwm3 0 25000 0>;
  brightness-levels = <
     0 1 2 3 4 5 6 7
     8 9 10 11 12 13 14 15
    16 17 18 19 20 21 22 23
    24 25 26 27 28 29 30 31
    32 33 34 35 36 37 38 39
    40 41 42 43 44 45 46 47
    48 49 50 51 52 53 54 55
    56 57 58 59 60 61 62 63
    64 65 66 67 68 69 70 71
    72 73 74 75 76 77 78 79
    80 81 82 83 84 85 86 87
    88 89 90 91 92 93 94 95
    96 97 98 99 100 101 102 103
   104 105 106 107 108 109 110 111
   112 113 114 115 116 117 118 119
   120 121 122 123 124 125 126 127
   128 129 130 131 132 133 134 135
   136 137 138 139 140 141 142 143
   144 145 146 147 148 149 150 151
   152 153 154 155 156 157 158 159
   160 161 162 163 164 165 166 167
   168 169 170 171 172 173 174 175
   176 177 178 179 180 181 182 183
   184 185 186 187 188 189 190 191
   192 193 194 195 196 197 198 199
   200 201 202 203 204 205 206 207
   208 209 210 211 212 213 214 215
   216 217 218 219 220 221 222 223
   224 225 226 227 228 229 230 231
   232 233 234 235 236 237 238 239
   240 241 242 243 244 245 246 247
   248 249 250 251 252 253 254 255>;
  default-brightness-level = <200>;
 };

 cam_ircut0: cam_ircut {
  status = "okay";
  compatible = "rockchip,ircut";
  ircut-open-gpios = <&gpio2 7 0>;
  ircut-close-gpios = <&gpio2 6 0>;
  rockchip,camera-module-index = <1>;
  rockchip,camera-module-facing = "front";
 };

 dummy_codec: dummy-codec {
  compatible = "rockchip,dummy-codec";
  #sound-dai-cells = <0>;
 };

 pdm_mic_array: pdm-mic_array {
  status = "disabled";
  compatible = "simple-audio-card";
  simple-audio-card,name = "rockchip,pdm-mic-array";
  simple-audio-card,cpu {
   sound-dai = <&pdm>;
  };
  simple-audio-card,codec {
   sound-dai = <&dummy_codec>;
  };
 };

 rk809_sound: rk809-sound {
  compatible = "simple-audio-card";
  simple-audio-card,format = "i2s";
  simple-audio-card,name = "rockchip,rk809-codec";
  simple-audio-card,mclk-fs = <256>;
  simple-audio-card,widgets =
   "Microphone", "Mic Jack",
   "Headphone", "Headphone Jack";
  simple-audio-card,routing =
   "Mic Jack", "MICBIAS1",
   "IN1P", "Mic Jack",
   "Headphone Jack", "HPOL",
   "Headphone Jack", "HPOR";
  simple-audio-card,cpu {
   sound-dai = <&i2s0_8ch>;
  };
  simple-audio-card,codec {
   sound-dai = <&rk809_codec>;
  };
 };

 sdio_pwrseq: sdio-pwrseq {
  compatible = "mmc-pwrseq-simple";
  pinctrl-names = "default";
  pinctrl-0 = <&wifi_enable_h>;







  reset-gpios = <&gpio0 6 1>;
 };

 vcc18_lcd_n: vcc18-lcd-n {
  compatible = "regulator-fixed";
  regulator-name = "vcc18_lcd_n";
  gpio = <&gpio3 29 0>;
  enable-active-high;
  regulator-boot-on;
 };

 vcc5v0_sys: vccsys {
  compatible = "regulator-fixed";
  regulator-name = "vcc5v0_sys";
  regulator-always-on;
  regulator-boot-on;
  regulator-min-microvolt = <5000000>;
  regulator-max-microvolt = <5000000>;
 };

 vdd_npu: vdd-npu {
  compatible = "pwm-regulator";
  pwms = <&pwm0 0 5000 1>;
  regulator-name = "vdd_npu";
  regulator-min-microvolt = <650000>;
  regulator-max-microvolt = <950000>;
  regulator-init-microvolt = <800000>;
  regulator-always-on;
  regulator-boot-on;
  regulator-settling-time-up-us = <250>;
  pwm-supply = <&vcc5v0_sys>;
  status = "okay";
 };

 vdd_vepu: vdd-vepu {
  compatible = "pwm-regulator";
  pwms = <&pwm1 0 5000 1>;
  regulator-name = "vdd_vepu";
  regulator-min-microvolt = <650000>;
  regulator-max-microvolt = <950000>;
  regulator-init-microvolt = <800000>;
  regulator-always-on;
  regulator-boot-on;
  regulator-settling-time-up-us = <250>;
  pwm-supply = <&vcc5v0_sys>;
  status = "okay";
 };

 wireless-bluetooth {
  compatible = "bluetooth-platdata";
  uart_rts_gpios = <&gpio1 16 1>;
  pinctrl-names = "default", "rts_gpio";
  pinctrl-0 = <&uart0_rtsn>;
  pinctrl-1 = <&uart0_rtsn_gpio>;
  BT,power_gpio = <&gpio0 7 0>;
  BT,wake_host_irq = <&gpio0 5 0>;
  status = "disabled";
 };

 wireless_wlan: wireless-wlan {
  compatible = "wlan-platdata";
  rockchip,grf = <&grf>;
  clocks = <&rk809 1>;
  clock-names = "clk_wifi";
  pinctrl-names = "default";
  pinctrl-0 = <&wifi_wake_host>;
  wifi_chip_type = "ap6255";

  WIFI,host_wake_irq = <&gpio0 8 0>;
  status = "disabled";
 };
};

&cpu0 {
 cpu-supply = <&vdd_arm>;
};

&cpu_tsadc {
 status = "okay";
};

&display_subsystem {
 status = "okay";
};

&dsi {
 status = "okay";

 rockchip,lane-rate = <480>;
 panel@0 {
  compatible = "ilitek,ili9881d", "simple-panel-dsi";
  reg = <0>;
  backlight = <&backlight>;
  power-supply = <&vcc18_lcd_n>;
  prepare-delay-ms = <5>;
  reset-delay-ms = <1>;
  init-delay-ms = <80>;
  disable-delay-ms = <10>;
  unprepare-delay-ms = <5>;

  width-mm = <68>;
  height-mm = <121>;

  dsi,flags = <((1 << 0) | (1 << 1) |
         (1 << 11) | (1 << 9))>;
  dsi,format = <0>;
  dsi,lanes = <4>;

  panel-init-sequence = [
   39 00 04 ff 98 81 03
   15 00 02 01 00
   15 00 02 02 00
   15 00 02 03 53
   15 00 02 04 53
   15 00 02 05 13
   15 00 02 06 04
   15 00 02 07 02
   15 00 02 08 02
   15 00 02 09 00
   15 00 02 0a 00
   15 00 02 0b 00
   15 00 02 0c 00
   15 00 02 0d 00
   15 00 02 0e 00
   15 00 02 0f 00
   15 00 02 10 00
   15 00 02 11 00
   15 00 02 12 00
   15 00 02 13 00
   15 00 02 14 00
   15 00 02 15 08
   15 00 02 16 10
   15 00 02 17 00
   15 00 02 18 08
   15 00 02 19 00
   15 00 02 1a 00
   15 00 02 1b 00
   15 00 02 1c 00
   15 00 02 1d 00
   15 00 02 1e c0
   15 00 02 1f 80
   15 00 02 20 02
   15 00 02 21 09
   15 00 02 22 00
   15 00 02 23 00
   15 00 02 24 00
   15 00 02 25 00
   15 00 02 26 00
   15 00 02 27 00
   15 00 02 28 55
   15 00 02 29 03
   15 00 02 2a 00
   15 00 02 2b 00
   15 00 02 2c 00
   15 00 02 2d 00
   15 00 02 2e 00
   15 00 02 2f 00
   15 00 02 30 00
   15 00 02 31 00
   15 00 02 32 00
   15 00 02 33 00
   15 00 02 34 04
   15 00 02 35 05
   15 00 02 36 05
   15 00 02 37 00
   15 00 02 38 3c
   15 00 02 39 35
   15 00 02 3a 00
   15 00 02 3b 40
   15 00 02 3c 00
   15 00 02 3d 00
   15 00 02 3e 00
   15 00 02 3f 00
   15 00 02 40 00
   15 00 02 41 88
   15 00 02 42 00
   15 00 02 43 00
   15 00 02 44 1f
   15 00 02 50 01
   15 00 02 51 23
   15 00 02 52 45
   15 00 02 53 67
   15 00 02 54 89
   15 00 02 55 ab
   15 00 02 56 01
   15 00 02 57 23
   15 00 02 58 45
   15 00 02 59 67
   15 00 02 5a 89
   15 00 02 5b ab
   15 00 02 5c cd
   15 00 02 5d ef
   15 00 02 5e 03
   15 00 02 5f 14
   15 00 02 60 15
   15 00 02 61 0c
   15 00 02 62 0d
   15 00 02 63 0e
   15 00 02 64 0f
   15 00 02 65 10
   15 00 02 66 11
   15 00 02 67 08
   15 00 02 68 02
   15 00 02 69 0a
   15 00 02 6a 02
   15 00 02 6b 02
   15 00 02 6c 02
   15 00 02 6d 02
   15 00 02 6e 02
   15 00 02 6f 02
   15 00 02 70 02
   15 00 02 71 02
   15 00 02 72 06
   15 00 02 73 02
   15 00 02 74 02
   15 00 02 75 14
   15 00 02 76 15
   15 00 02 77 0f
   15 00 02 78 0e
   15 00 02 79 0d
   15 00 02 7a 0c
   15 00 02 7b 11
   15 00 02 7c 10
   15 00 02 7d 06
   15 00 02 7e 02
   15 00 02 7f 0a
   15 00 02 80 02
   15 00 02 81 02
   15 00 02 82 02
   15 00 02 83 02
   15 00 02 84 02
   15 00 02 85 02
   15 00 02 86 02
   15 00 02 87 02
   15 00 02 88 08
   15 00 02 89 02
   15 00 02 8a 02
   39 00 04 ff 98 81 04
   15 00 02 00 80
   15 00 02 70 00
   15 00 02 71 00
   15 00 02 66 fe
   15 00 02 82 15
   15 00 02 84 15
   15 00 02 85 15
   15 00 02 3a 24
   15 00 02 32 ac
   15 00 02 8c 80
   15 00 02 3c f5
   15 00 02 88 33
   39 00 04 ff 98 81 01
   15 00 02 22 0a
   15 00 02 31 00
   15 00 02 53 78
   15 00 02 55 7b
   15 00 02 60 20
   15 00 02 61 00
   15 00 02 62 0d
   15 00 02 63 00
   15 00 02 a0 00
   15 00 02 a1 10
   15 00 02 a2 1c
   15 00 02 a3 13
   15 00 02 a4 15
   15 00 02 a5 26
   15 00 02 a6 1a
   15 00 02 a7 1d
   15 00 02 a8 67
   15 00 02 a9 1c
   15 00 02 aa 29
   15 00 02 ab 5b
   15 00 02 ac 26
   15 00 02 ad 28
   15 00 02 ae 5c
   15 00 02 af 30
   15 00 02 b0 31
   15 00 02 b1 32
   15 00 02 b2 00
   15 00 02 b1 2e
   15 00 02 b2 32
   15 00 02 b3 00
   15 00 02 c0 00
   15 00 02 c1 10
   15 00 02 c2 1c
   15 00 02 c3 13
   15 00 02 c4 15
   15 00 02 c5 26
   15 00 02 c6 1a
   15 00 02 c7 1d
   15 00 02 c8 67
   15 00 02 c9 1c
   15 00 02 ca 29
   15 00 02 cb 5b
   15 00 02 cc 26
   15 00 02 cd 28
   15 00 02 ce 5c
   15 00 02 cf 30
   15 00 02 d0 31
   15 00 02 d1 2e
   15 00 02 d2 32
   15 00 02 d3 00
   39 00 04 ff 98 81 00
   05 00 01 11
   05 01 01 29
  ];

  display-timings {
   native-mode = <&timing0>;

   timing0: timing0 {
    clock-frequency = <65000000>;
    hactive = <720>;
    vactive = <1280>;
    hfront-porch = <48>;
    hsync-len = <8>;
    hback-porch = <52>;
    vfront-porch = <16>;
    vsync-len = <6>;
    vback-porch = <15>;
    hsync-active = <0>;
    vsync-active = <0>;
    de-active = <0>;
    pixelclk-active = <0>;
   };
  };

  ports {
   #address-cells = <1>;
   #size-cells = <0>;

   port@0 {
    reg = <0>;
    panel_in_dsi: endpoint {
     remote-endpoint = <&dsi_out_panel>;
    };
   };
  };
 };

 ports {
  #address-cells = <1>;
  #size-cells = <0>;

  port@1 {
   reg = <1>;
   dsi_out_panel: endpoint {
    remote-endpoint = <&panel_in_dsi>;
   };
  };
 };
};

&csi_dphy0 {
 status = "okay";

 ports {
  #address-cells = <1>;
  #size-cells = <0>;
  port@0 {
   reg = <0>;
   #address-cells = <1>;
   #size-cells = <0>;

   mipi_in_ucam0: endpoint@1 {
    reg = <1>;
    remote-endpoint = <&ucam_out0>;
    data-lanes = <1 2>;
   };

   mipi_in_ucam0_6752: endpoint@2 {
    reg = <2>;
    remote-endpoint = <&ucam_out0_6752>;
    data-lanes = <1 2>;
   };

   mipi_in_ucam0_2815: endpoint@3 {
    reg = <3>;
    remote-endpoint = <&ucam_out0_2815>;
    data-lanes = <1 2 3 4>;
   };
  };
  port@1 {
   reg = <1>;
   #address-cells = <1>;
   #size-cells = <0>;







   csi_dphy1_output: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&isp_in>;
   };

  };
 };
};

&csi_dphy1 {
 status = "okay";

 ports {
  #address-cells = <1>;
  #size-cells = <0>;

  port@0 {
   reg = <0>;
   #address-cells = <1>;
   #size-cells = <0>;

   csi_dphy1_input: endpoint@1 {
    reg = <1>;
    remote-endpoint = <&ucam_out1>;
    data-lanes = <1 2>;
   };


   csi_dphy1_input_6752: endpoint@2 {
    reg = <2>;
    remote-endpoint = <&ucam_out1_6752>;
    data-lanes = <1 2>;
   };
  };

  port@1 {
   reg = <1>;
   #address-cells = <1>;
   #size-cells = <0>;
// # 593 "arch/arm64/boot/dts/rockchip/rv1126bp-evb-v10.dtsi"
   csidphy0_out: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&mipi_csi2_input>;
   };

  };
 };
};

&emmc {
 bus-width = <8>;
 cap-mmc-highspeed;
 non-removable;
 mmc-hs200-1_8v;
 rockchip,default-sample-phase = <90>;
 supports-emmc;
 /delete-property/ pinctrl-names;
 /delete-property/ pinctrl-0;
 status = "okay";
};

&fiq_debugger {
 status = "okay";
};

&gmac {
 phy-mode = "rmii";
 clock_in_out = "output";

 snps,reset-gpio = <&gpio2 4 1>;
 snps,reset-active-low;

 snps,reset-delays-us = <100000 200000 100000>;

 assigned-clocks = <&cru 125>, <&cru 126>, <&cru 136>;
 assigned-clock-rates = <0>, <50000000>;
 assigned-clock-parents = <&cru 124>, <&cru 125>, <&cru 135>;

 pinctrl-names = "default";
 pinctrl-0 = <&rmiim1_miim &rgmiim1_rxer &rmiim1_bus2 &rgmiim1_mclkinout>;


 phy-handle = <&phy>;
 status = "okay";
};

&i2c0 {
 status = "okay";
 clock-frequency = <400000>;

 rk809: pmic@20 {
  compatible = "rockchip,rk809";
  reg = <0x20>;
  interrupt-parent = <&gpio0>;
  interrupts = <9 8>;
  pinctrl-names = "default", "pmic-sleep",
   "pmic-power-off", "pmic-reset";
  pinctrl-0 = <&pmic_int>;
  pinctrl-1 = <&soc_slppin_gpio>, <&rk817_slppin_slp>;
  pinctrl-2 = <&soc_slppin_gpio>, <&rk817_slppin_pwrdn>;
  pinctrl-3 = <&soc_slppin_slp>, <&rk817_slppin_rst>;
  rockchip,system-power-controller;
  wakeup-source;
  #clock-cells = <1>;
  clock-output-names = "rk808-clkout1", "rk808-clkout2";

  pmic-reset-func = <0>;

  vcc1-supply = <&vcc5v0_sys>;
  vcc2-supply = <&vcc5v0_sys>;
  vcc3-supply = <&vcc5v0_sys>;
  vcc4-supply = <&vcc5v0_sys>;
  vcc5-supply = <&vcc_buck5>;
  vcc6-supply = <&vcc_buck5>;
  vcc7-supply = <&vcc5v0_sys>;
  vcc8-supply = <&vcc3v3_sys>;
  vcc9-supply = <&vcc5v0_sys>;

  pwrkey {
   status = "okay";
  };

  pinctrl_rk8xx: pinctrl_rk8xx {
   gpio-controller;
   #gpio-cells = <2>;

   /omit-if-no-ref/
   rk817_slppin_null: rk817_slppin_null {
    pins = "gpio_slp";
    function = "pin_fun0";
   };

   /omit-if-no-ref/
   rk817_slppin_slp: rk817_slppin_slp {
    pins = "gpio_slp";
    function = "pin_fun1";
   };

   /omit-if-no-ref/
   rk817_slppin_pwrdn: rk817_slppin_pwrdn {
    pins = "gpio_slp";
    function = "pin_fun2";
   };

   /omit-if-no-ref/
   rk817_slppin_rst: rk817_slppin_rst {
    pins = "gpio_slp";
    function = "pin_fun3";
   };
  };

  regulators {
   vdd_logic: DCDC_REG1 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <725000>;
    regulator-max-microvolt = <1350000>;
    regulator-ramp-delay = <6001>;
    regulator-initial-mode = <0x2>;
    regulator-name = "vdd_logic";
    regulator-state-mem {
     regulator-on-in-suspend;
     regulator-suspend-microvolt = <800000>;
    };
   };

   vdd_arm: DCDC_REG2 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <725000>;
    regulator-max-microvolt = <1350000>;
    regulator-ramp-delay = <6001>;
    regulator-initial-mode = <0x2>;
    regulator-name = "vdd_arm";
    regulator-state-mem {
     regulator-off-in-suspend;
    };
   };

   vcc_ddr: DCDC_REG3 {
    regulator-always-on;
    regulator-boot-on;
    regulator-initial-mode = <0x2>;
    regulator-name = "vcc_ddr";
    regulator-state-mem {
     regulator-on-in-suspend;
    };
   };

   vcc3v3_sys: DCDC_REG4 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <3300000>;
    regulator-max-microvolt = <3300000>;
    regulator-initial-mode = <0x2>;
    regulator-name = "vcc3v3_sys";
    regulator-state-mem {
     regulator-on-in-suspend;
     regulator-suspend-microvolt = <3300000>;
    };
   };

   vcc_buck5: DCDC_REG5 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <2200000>;
    regulator-max-microvolt = <2200000>;
    regulator-name = "vcc_buck5";
    regulator-state-mem {
     regulator-on-in-suspend;
     regulator-suspend-microvolt = <2200000>;
    };
   };

   vcc_0v8: LDO_REG1 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <800000>;
    regulator-max-microvolt = <800000>;
    regulator-name = "vcc_0v8";
    regulator-state-mem {
     regulator-off-in-suspend;
    };
   };

   vcc1v8_pmu: LDO_REG2 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <1800000>;
    regulator-max-microvolt = <1800000>;
    regulator-name = "vcc1v8_pmu";
    regulator-state-mem {
     regulator-on-in-suspend;
     regulator-suspend-microvolt = <1800000>;
    };
   };

   vdd0v8_pmu: LDO_REG3 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <800000>;
    regulator-max-microvolt = <800000>;
    regulator-name = "vcc0v8_pmu";
    regulator-state-mem {
     regulator-on-in-suspend;
     regulator-suspend-microvolt = <800000>;
    };
};

&pmu {
	pmu_io_domains: io-domains {
		compatible = "rockchip,pmu-io-domains";

		pmuio0-supply {
			regulator-min-microvolt = <3300000>;
			regulator-max-microvolt = <3300000>;
		};

		pmuio1-supply {
			regulator-min-microvolt = <3300000>;
			regulator-max-microvolt = <3300000>;
		};

		vccio2-supply {
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <3300000>;
		};

		vccio3-supply {
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <1800000>;
		};

		vccio4-supply {
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <1800000>;
		};

		vccio6-supply {
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <1800000>;
		};

		vccio7-supply {
			regulator-min-microvolt = <1800000>;
			regulator-max-microvolt = <1800000>;
		};
	};
};

   };

   vcc_1v8: LDO_REG4 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <1800000>;
    regulator-max-microvolt = <1800000>;
    regulator-name = "vcc_1v8";
    regulator-state-mem {
     regulator-on-in-suspend;
     regulator-suspend-microvolt = <1800000>;
    };
   };

   vcc_dovdd: LDO_REG5 {
    regulator-boot-on;
    regulator-min-microvolt = <1800000>;
    regulator-max-microvolt = <1800000>;
    regulator-name = "vcc_dovdd";
    regulator-state-mem {
     regulator-off-in-suspend;
    };
   };

   vcc_dvdd: LDO_REG6 {
    regulator-min-microvolt = <1250000>;
    regulator-max-microvolt = <1250000>;
    regulator-name = "vcc_dvdd";
    regulator-state-mem {
     regulator-off-in-suspend;
    };
   };

   vcc_avdd: LDO_REG7 {
    regulator-min-microvolt = <2800000>;
    regulator-max-microvolt = <2800000>;
    regulator-name = "vcc_avdd";
    regulator-state-mem {
     regulator-off-in-suspend;
    };
   };

   vccio_sd: LDO_REG8 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <1800000>;
    regulator-max-microvolt = <3300000>;
    regulator-name = "vccio_sd";
    regulator-state-mem {
     regulator-off-in-suspend;
    };
   };

   vcc3v3_sd: LDO_REG9 {
    regulator-always-on;
    regulator-boot-on;
    regulator-min-microvolt = <3300000>;
    regulator-max-microvolt = <3300000>;
    regulator-name = "vcc3v3_sd";
    regulator-state-mem {
     regulator-off-in-suspend;
    };
   };

   vcc5v0_host: SWITCH_REG1 {
    regulator-name = "vcc5v0_host";
   };

   vcc_3v3: SWITCH_REG2 {
    regulator-always-on;
    regulator-boot-on;
    regulator-name = "vcc_3v3";
   };
  };

  rk809_codec: codec {
   #sound-dai-cells = <0>;
   compatible = "rockchip,rk809-codec", "rockchip,rk817-codec";
   clocks = <&cru 66>;
   clock-names = "mclk";
   pinctrl-names = "default";
   assigned-clocks = <&cru 66>;
   assigned-clock-parents = <&cru 61>;
   pinctrl-0 = <&i2s0m0_mclk>;
   hp-volume = <20>;
   spk-volume = <3>;
  };
 };
};

&i2c1 {
 clock-frequency = <100000>;
 status = "okay";
 ar0230: ar0230@10 {
  compatible = "aptina,ar0230";
  reg = <0x10>;
  clocks = <&cru 99>;
  clock-names = "xvclk";
  avdd-supply = <&vcc_avdd>;
  dovdd-supply = <&vcc_dovdd>;
  dvdd-supply = <&vcc_dvdd>;
  power-domains = <&power 9>;
  pwdn-gpios = <&gpio2 6 0>;

  rockchip,grf = <&grf>;
  pinctrl-names = "default";
  pinctrl-0 = <&cifm0_dvp_ctl>;
  rockchip,camera-module-index = <0>;
  rockchip,camera-module-facing = "back";
  rockchip,camera-module-name = "CMK-OT0836-PT2";
  rockchip,camera-module-lens-name = "YT-2929";
  port {
   cam_para_out1: endpoint {

   };
  };
 };

 RN6752_1: RN6752@2c {
        compatible = "sony,imx415";
        reg = <0x2c>;
        clocks = <&cru 103>;
        clock-names = "xvclk";
        power-domains = <&power 9>;
        pinctrl-names = "rockchip,camera_default";
        pinctrl-0 = <&mipicsi_clk0>;
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 2 0>;
        reset-gpios = <&gpio3 11 1>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out1_6752: endpoint {
                remote-endpoint = <&csi_dphy1_input_6752>;
                data-lanes = <1 2>;
            };
        };
    };

 RN6752_2: RN6752@2d {
        compatible = "sony,imx415";
        reg = <0x2d>;
        clocks = <&cru 103>;
        clock-names = "xvclk";
        power-domains = <&power 9>;
        pinctrl-names = "rockchip,camera_default";
        pinctrl-0 = <&mipicsi_clk0>;
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 3 0>;
        reset-gpios = <&gpio3 10 1>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out0_6752: endpoint {
                remote-endpoint = <&mipi_in_ucam0_6752>;
                data-lanes = <1 2>;
            };
        };
    };

 tp2815: tp2815@44 {
        compatible = "techpoint,tp2815";
        reg = <0x44>;
        clocks = <&cru 103>;
        clock-names = "xvclk";
        power-domains = <&power 9>;
        pinctrl-names = "rockchip,camera_default";
        pinctrl-0 = <&mipicsi_clk0>;
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 3 0>;
        reset-gpios = <&gpio3 10 1>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out0_2815: endpoint {
                remote-endpoint = <&mipi_in_ucam0_2815>;
                data-lanes = <1 2>;
            };
        };
    };

 ov4689: ov4689@5f {
  compatible = "ovti,os04a10";
  reg = <0x5f>;
  clocks = <&cru 103>;
  clock-names = "xvclk";
  power-domains = <&power 9>;
  pinctrl-names = "rockchip,camera_default";
  pinctrl-0 = <&mipicsi_clk1>;

  avdd-supply = <&vcc_avdd>;
  dovdd-supply = <&vcc_dovdd>;
  dvdd-supply = <&vcc_dvdd>;
  reset-gpios = <&gpio3 11 1>;
  rockchip,camera-module-index = <1>;
  rockchip,camera-module-facing = "front";
  rockchip,camera-module-name = "JSD3425-C1";
  rockchip,camera-module-lens-name = "JSD3425-C1";
  port {
   ucam_out1: endpoint {
    remote-endpoint = <&csi_dphy1_input>;
    data-lanes = <1 2>;
   };
  };

 };

 os04a10: os04a10@5c {
  compatible = "ovti,os04a10";
  reg = <0x5c>;
  clocks = <&cru 103>;
  clock-names = "xvclk";
  power-domains = <&power 9>;
  pinctrl-names = "rockchip,camera_default";
  pinctrl-0 = <&mipicsi_clk0>;
  avdd-supply = <&vcc_avdd>;
  dovdd-supply = <&vcc_dovdd>;
  dvdd-supply = <&vcc_dvdd>;

  reset-gpios = <&gpio3 10 1>;
  rockchip,camera-module-index = <1>;
  rockchip,camera-module-facing = "front";
  rockchip,camera-module-name = "CMK-OT1607-FV1";
  rockchip,camera-module-lens-name = "M12-40IRC-4MP-F16";

  port {
   ucam_out0: endpoint {
    remote-endpoint = <&mipi_in_ucam0>;
    data-lanes = <1 2>;
   };
  };
 };
};

&i2c2 {
 status = "okay";
 clock-frequency = <100000>;
};

&i2c5 {
 status = "okay";
 clock-frequency = <400000>;

 hym8563: hym8563@51 {
  compatible = "haoyu,hym8563";
  reg = <0x51>;



  #clock-cells = <0>;
 };

};

&i2s0_8ch {
 status = "okay";
 #sound-dai-cells = <0>;
 rockchip,clk-trcm = <1>;
 rockchip,i2s-rx-route = <3 1 2 0>;
 pinctrl-names = "default";
 pinctrl-0 = <&i2s0m0_sclk_tx
       &i2s0m0_lrck_tx
       &i2s0m0_sdo0
       &i2s0m0_sdo1_sdi3>;
};

&iep {
 status = "okay";
};

&iep_mmu {
 status = "okay";
};

&mdio {
 phy: phy@1 {
  compatible = "ethernet-phy-ieee802.3-c22";
  reg = <0x1>;
  clocks = <&cru 138>;
 };
};

&mipi_csi2 {
 status = "okay";

 ports {
  #address-cells = <1>;
  #size-cells = <0>;

  port@0 {
   reg = <0>;
   #address-cells = <1>;
   #size-cells = <0>;

   mipi_csi2_input: endpoint@1 {
    reg = <1>;
    remote-endpoint = <&csidphy0_out>;
    data-lanes = <1 2>;
   };
  };

  port@1 {
   reg = <1>;
   #address-cells = <1>;
   #size-cells = <0>;

   mipi_csi2_output: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&cif_mipi_in>;
    data-lanes = <1 2>;
   };
  };
 };
};

&mipi_dphy {
 status = "okay";
};

&mpp_srv {
 status = "okay";
};

&nandc {
 /delete-property/ pinctrl-names;
 /delete-property/ pinctrl-0;
 #address-cells = <1>;
 #size-cells = <0>;

 nand@0 {
  reg = <0>;
  nand-bus-width = <8>;
  nand-ecc-mode = "hw";
  nand-ecc-strength = <16>;
  nand-ecc-step-size = <1024>;
 };
};

&npu {
 npu-supply = <&vdd_npu>;
 status = "okay";
};

&npu_tsadc {
 status = "okay";
};

&optee {
 status = "disabled";
};

&otp {
 status = "okay";
};

&pdm {
 status = "disabled";
 #sound-dai-cells = <0>;
 pinctrl-names = "default";
 pinctrl-0 = <&pdmm0_clk
       &pdmm0_clk1
       &pdmm0_sdi0
       &pdmm0_sdi1
       &pdmm0_sdi2>;
};

&pinctrl {
 pmic {
  /omit-if-no-ref/
  pmic_int: pmic_int {
   rockchip,pins =
    <0 9 0 &pcfg_pull_up>;
  };

  /omit-if-no-ref/
  soc_slppin_gpio: soc_slppin_gpio {
   rockchip,pins =
    <0 10 0 &pcfg_output_low>;
  };

  /omit-if-no-ref/
  soc_slppin_slp: soc_slppin_slp {
   rockchip,pins =
    <0 10 1 &pcfg_pull_none>;
  };

  /omit-if-no-ref/
  soc_slppin_rst: soc_slppin_rst {
   rockchip,pins =
    <0 10 2 &pcfg_pull_none>;
  };
 };

 sdio-pwrseq {
  /omit-if-no-ref/
  wifi_enable_h: wifi-enable-h {
   rockchip,pins = <0 6 0 &pcfg_pull_none>;
  };
 };

 wireless-wlan {
  /omit-if-no-ref/
  wifi_wake_host: wifi-wake-host {
   rockchip,pins = <0 8 0 &pcfg_pull_up>;
  };
 };
};

&pmu_io_domains {
 status = "okay";

 pmuio0-supply = <&vcc3v3_sys>;
 pmuio1-supply = <&vcc3v3_sys>;
 vccio2-supply = <&vccio_sd>;
 vccio3-supply = <&vcc_1v8>;
 vccio4-supply = <&vcc_1v8>;
 vccio5-supply = <&vcc_3v3>;
 vccio6-supply = <&vcc_1v8>;
 vccio7-supply = <&vcc_1v8>;
};

&pwm0 {
 status = "okay";
 pinctrl-names = "active";
 pinctrl-0 = <&pwm0m0_pins_pull_down>;
};

&pwm1 {
 status = "okay";
 pinctrl-names = "active";
 pinctrl-0 = <&pwm1m0_pins_pull_down>;
};

&pwm3 {
 status = "okay";
};

&ramoops {
 status = "okay";
};

&rk_rga {
 status = "okay";
};

&rkcif {
 status = "okay";
};

&rkcif_mmu {
 status = "disabled";
};

&rkcif_dvp {
 status = "okay";

 port {
// # 1278 "arch/arm64/boot/dts/rockchip/rv1126bp-evb-v10.dtsi"
 };
};

&rkcif_mipi_lvds {
 status = "okay";

 rockchip,cif-monitor = <3 2 10 10 5>;
 port {

  cif_mipi_in: endpoint {
   remote-endpoint = <&mipi_csi2_output>;
   data-lanes = <1 2>;
  };
 };
};

&rkcif_mipi_lvds_sditf {
 status = "okay";

 port {

  mipi_lvds_sditf: endpoint {

   data-lanes = <1 2>;
  };
 };
};

&rkisp {
 status = "okay";
};

&rkisp_vir0 {
 status = "okay";

 ports {
  port@0 {
   reg = <0>;
   #address-cells = <1>;
   #size-cells = <0>;

   isp_in: endpoint@0 {
    reg = <0>;
    remote-endpoint = <&csi_dphy1_output>;
    data-lanes = <1 2>;
   };
  };
 };
};
// # 1346 "arch/arm64/boot/dts/rockchip/rv1126bp-evb-v10.dtsi"
&rkisp_mmu {
 status = "disabled";
};

&rkispp {
 status = "disabled";

 max-input = <1280 720 25>;
};

&rkispp_vir0 {
 status = "disabled";
};

&rkispp_vir1 {
 status = "disabled";
};

&rkispp_mmu {
 status = "okay";
};

&rkvdec {
 status = "okay";
};

&rkvdec_mmu {
 status = "okay";
};

&rkvenc {
 venc-supply = <&vdd_vepu>;
 status = "okay";
};

&rkvenc_mmu {
 status = "okay";
};

&rng {
 status = "okay";
};

&rockchip_suspend {
 status = "okay";
 rockchip,sleep-debug-en = <1>;
 rockchip,sleep-mode-config = <
  (0
  | (1 << (1))
  | (1 << (9))
  | (1 << (10))
  | (1 << (17))
  )
 >;
 rockchip,wakeup-config = <
  (0
  | (1 << (4))
  )
 >;
};

&route_dsi {
 status = "okay";
};

&saradc {
 status = "okay";
 vref-supply = <&vcc_1v8>;
};

&sdmmc {
 bus-width = <4>;
 cap-mmc-highspeed;
 cap-sd-highspeed;
 card-detect-delay = <200>;
 rockchip,default-sample-phase = <90>;
 supports-sd;
 sd-uhs-sdr12;
 sd-uhs-sdr25;
 sd-uhs-sdr104;
 vqmmc-supply = <&vccio_sd>;
 vmmc-supply = <&vcc3v3_sd>;
 status = "okay";
};

&sdio {
 max-frequency = <200000000>;
 bus-width = <4>;
 cap-sd-highspeed;
 cap-sdio-irq;
 keep-power-in-suspend;
 non-removable;
 rockchip,default-sample-phase = <90>;
 sd-uhs-sdr104;
 supports-sdio;
 mmc-pwrseq = <&sdio_pwrseq>;
 status = "okay";
};

&sfc {
 /delete-property/ pinctrl-names;
 /delete-property/ pinctrl-0;
 status = "okay";
};

&u2phy0 {
 status = "okay";
 vup-gpios = <&gpio0 17 1>;
 u2phy_otg: otg-port {
  status = "okay";
 };
};

&u2phy1 {
 status = "okay";
 u2phy_host: host-port {
  status = "okay";
  phy-supply = <&vcc5v0_host>;
 };
};

&uart0 {
 pinctrl-names = "default";
 pinctrl-0 = <&uart0_xfer>;
 rs485enable = <1>;
 rs485-gpio = <&gpio1 16 1>;
 status = "okay";
};

&uart3 {
 status = "okay";
 pinctrl-names = "default";
 pinctrl-0 = <&uart3m0_xfer>;
};

&usb_host0_ehci {
 status = "okay";
};

&usb_host0_ohci {
 status = "okay";
};

&usbdrd {
 status = "okay";
};

&usbdrd_dwc3 {
 status = "okay";
 extcon = <&u2phy0>;
};

&vdpu {
 status = "okay";
};

&vepu {
 status = "okay";
};

&vpu_mmu {
 status = "okay";
};

&vop {
 status = "okay";
};

&vop_mmu {
 status = "okay";
};
// # 7 "arch/arm/boot/dts/rv1126-evb-v12.dtsi" 2
/ {
 /delete-node/ vdd-npu;
 /delete-node/ vdd-vepu;

 vdd_logic: vdd-logic {
  compatible = "regulator-fixed";
  regulator-name = "vdd_logic";
  regulator-always-on;
  regulator-boot-on;
  regulator-min-microvolt = <810000>;
  regulator-max-microvolt = <810000>;
 };

 camera_power{
 compatible = "vispect,mrv220_camera_power";
 status = "okay";
 power-gpios = <&gpio3 8 0>;
 };
};

&rk809 {
 regulators {
  /delete-node/ DCDC_REG1;
  vdd_npu_vepu: DCDC_REG1 {
   regulator-always-on;
   regulator-boot-on;
   regulator-min-microvolt = <650000>;
   regulator-max-microvolt = <950000>;
   regulator-ramp-delay = <6001>;
   regulator-initial-mode = <0x2>;
   regulator-name = "vdd_npu_vepu";
   regulator-state-mem {
    regulator-off-in-suspend;
   };
  };
 };
};

&npu {
 npu-supply = <&vdd_npu_vepu>;
};

&pwm0 {
 status = "disabled";
};

&pwm1 {
 status = "disabled";
};

&rkvenc {
 venc-supply = <&vdd_npu_vepu>;
};

&rkvenc_opp_table {



 rockchip,board-irdrop = <

  500 594 50000
 >;
};
// # 7 "arch/arm/boot/dts/rv1126-evb-v13.dtsi" 2

&backlight {
 pwms = <&pwm0 0 25000 0>;
};

&pwm0 {
 status = "okay";
};

&pwm3 {
 status = "disabled";
};

&u2phy0 {
 vup-gpios = <&gpio0 17 1>;
};
// # 8 "arch/arm/boot/dts/rv1126-evb-ddr3-v13.dts" 2
/ {
 model = "Rockchip RV1126 EVB DDR3 V13 Board";
 compatible = "rockchip,rv1126-evb-ddr3-v13", "rockchip,rv1126";

 chosen {
  bootargs = "earlycon=uart8250,mmio32,0xff570000 console=ttyFIQ0 root=PARTUUID=614e0000-0000 rootwait snd_aloop.index=7";
 };
};
