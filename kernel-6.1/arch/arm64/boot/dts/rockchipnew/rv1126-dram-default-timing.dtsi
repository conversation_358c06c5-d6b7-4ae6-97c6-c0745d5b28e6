/* DTS-XLATE-RV1126B */
// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Fuzhou Rockchip Electronics Co., Ltd
 */

#include <dt-bindings/clock/rockchip-ddr.h>
#include <dt-bindings/memory/rv1126-dram.h>

/ {
	ddr_timing: ddr_timing {
		compatible = "rockchip,ddr-timing";
		ddr2_speed_bin = <DDR2_DEFAULT>;
		ddr3_speed_bin = <DDR3_DEFAULT>;
		ddr4_speed_bin = <DDR4_DEFAULT>;
		pd_idle = <13>;
		sr_idle = <93>;
		sr_mc_gate_idle = <0>;
		srpd_lite_idle = <0>;
		standby_idle = <0>;

		auto_pd_dis_freq = <1066>;
		auto_sr_dis_freq = <800>;
		ddr2_dll_dis_freq = <300>;
		ddr3_dll_dis_freq = <300>;
		ddr4_dll_dis_freq = <625>;
		phy_dll_dis_freq = <400>;

		ddr2_odt_dis_freq = <100>;
		phy_ddr2_odt_dis_freq = <100>;
		ddr2_drv = <DDR2_DS_REDUCE>;
		ddr2_odt = <DDR2_ODT_150ohm>;
		phy_ddr2_ca_drv = <PHY_DDR3_RON_35ohm>;
		phy_ddr2_ck_drv = <PHY_DDR3_RON_41ohm>;
		phy_ddr2_dq_drv = <PHY_DDR3_RON_35ohm>;
		phy_ddr2_odt = <PHY_DDR3_RTT_282ohm>;

		ddr3_odt_dis_freq = <333>;
		phy_ddr3_odt_dis_freq = <333>;
		ddr3_drv = <DDR3_DS_34ohm>;
		ddr3_odt = <DDR3_ODT_120ohm>;
		phy_ddr3_ca_drv = <PHY_DDR3_RON_41ohm>;
		phy_ddr3_ck_drv = <PHY_DDR3_RON_38ohm>;
		phy_ddr3_dq_drv = <PHY_DDR3_RON_30ohm>;
		phy_ddr3_odt = <PHY_DDR3_RTT_141ohm>;

		phy_lpddr2_odt_dis_freq = <333>;
		lpddr2_drv = <LP2_DS_40ohm>;
		phy_lpddr2_ca_drv = <PHY_DDR4_LPDDR3_RON_34ohm>;
		phy_lpddr2_ck_drv = <PHY_DDR4_LPDDR3_RON_40ohm>;
		phy_lpddr2_dq_drv = <PHY_DDR4_LPDDR3_RON_34ohm>;
		phy_lpddr2_odt = <PHY_DDR4_LPDDR3_RTT_DISABLE>;

		lpddr3_odt_dis_freq = <333>;
		phy_lpddr3_odt_dis_freq = <333>;
		lpddr3_drv = <LP3_DS_34ohm>;
		lpddr3_odt = <LP3_ODT_120ohm>;
		phy_lpddr3_ca_drv = <PHY_DDR4_LPDDR3_RON_37ohm>;
		phy_lpddr3_ck_drv = <PHY_DDR4_LPDDR3_RON_34ohm>;
		phy_lpddr3_dq_drv = <PHY_DDR4_LPDDR3_RON_28ohm>;
		phy_lpddr3_odt = <PHY_DDR4_LPDDR3_RTT_148ohm>;

		lpddr4_odt_dis_freq = <333>;
		phy_lpddr4_odt_dis_freq = <333>;
		lpddr4_drv = <LP4_PDDS_40ohm>;
		lpddr4_dq_odt = <LP4_DQ_ODT_240ohm>;
		lpddr4_ca_odt = <LP4_CA_ODT_DIS>;
		phy_lpddr4_ca_drv = <PHY_LPDDR4_RON_46ohm>;
		phy_lpddr4_ck_cs_drv = <PHY_LPDDR4_RON_38ohm>;
		phy_lpddr4_dq_drv = <PHY_LPDDR4_RON_38ohm>;
		phy_lpddr4_odt = <PHY_LPDDR4_RTT_62ohm>;

		ddr4_odt_dis_freq = <625>;
		phy_ddr4_odt_dis_freq = <625>;
		ddr4_drv = <DDR4_DS_34ohm>;
		ddr4_odt = <DDR4_ODT_120ohm>;
		phy_ddr4_ca_drv = <PHY_DDR4_LPDDR3_RON_44ohm>;
		phy_ddr4_ck_drv = <PHY_DDR4_LPDDR3_RON_37ohm>;
		phy_ddr4_dq_drv = <PHY_DDR4_LPDDR3_RON_37ohm>;
		phy_ddr4_odt = <PHY_DDR4_LPDDR3_RTT_148ohm>;

		/*
		 * CA de-skew, one step is 20ps, range 0-63
		 * name rule: ddr4(pad_name)_ddr3_lpddr3_lpddr4_de-skew
		 */
		a0_a3_a3_cke1-a_de-skew = <7>;
		a1_ba1_null_cke0-b_de-skew = <7>;
		a2_a9_a9_a4-a_de-skew = <7>;
		a3_a15_null_a5-b_de-skew = <7>;
		a4_a6_a6_ck-a_de-skew = <7>;
		a5_a12_null_odt0-b_de-skew = <7>;
		a6_ba2_null_a0-a_de-skew = <7>;
		a7_a4_a4_odt0-a_de-skew = <7>;
		a8_a1_a1_cke0-a_de-skew = <7>;
		a9_a5_a5_a5-a_de-skew = <7>;
		a10_a8_a8_clkb-a_de-skew = <7>;
		a11_a7_a7_ca2-a_de-skew = <7>;
		a12_rasn_null_ca1-a_de-skew = <7>;
		a13_a13_null_ca3-a_de-skew = <7>;
		a14_a14_null_csb1-b_de-skew = <7>;
		a15_a10_null_ca0-b_de-skew = <7>;
		a16_a11_null_csb0-b_de-skew = <7>;
		a17_null_null_null_de-skew = <7>;
		ba0_csb1_csb1_csb0-a_de-skew = <7>;
		ba1_wen_null_cke1-b_de-skew = <7>;
		bg0_odt1_odt1_csb1-a_de-skew = <7>;
		bg1_a2_a2_odt1-a_de-skew = <7>;
		cke0_casb_null_ca1-b_de-skew = <7>;
		ck_ck_ck_ck-b_de-skew = <7>;
		ckb_ckb_ckb_ckb-b_de-skew = <7>;
		csb0_odt0_odt0_ca2-b_de-skew = <7>;
		odt0_csb0_csb0_ca4-b_de-skew = <7>;
		resetn_resetn_null-resetn_de-skew = <7>;
		actn_cke_cke_ca3-b_de-skew = <7>;
		cke1_null_null_null_de-skew = <7>;
		csb1_ba0_null_null_de-skew = <7>;
		odt1_a0_a0_odt1-b_de-skew = <7>;

		/* DATA de-skew, one step is 20ps, range 0-63 */
		/* cs0_skew_a */
		cs0_dm0_rx_de-skew = <7>;
		cs0_dq0_rx_de-skew = <7>;
		cs0_dq1_rx_de-skew = <7>;
		cs0_dq2_rx_de-skew = <7>;
		cs0_dq3_rx_de-skew = <7>;
		cs0_dq4_rx_de-skew = <7>;
		cs0_dq5_rx_de-skew = <7>;
		cs0_dq6_rx_de-skew = <7>;
		cs0_dq7_rx_de-skew = <7>;
		cs0_dqs0p_rx_de-skew = <14>;
		cs0_dqs0n_rx_de-skew = <14>;
		cs0_dm1_rx_de-skew = <7>;
		cs0_dq8_rx_de-skew = <7>;
		cs0_dq9_rx_de-skew = <7>;
		cs0_dq10_rx_de-skew = <7>;
		cs0_dq11_rx_de-skew = <7>;
		cs0_dq12_rx_de-skew = <7>;
		cs0_dq13_rx_de-skew = <7>;
		cs0_dq14_rx_de-skew = <7>;
		cs0_dq15_rx_de-skew = <7>;
		cs0_dqs1p_rx_de-skew = <14>;
		cs0_dqs1n_rx_de-skew = <14>;
		cs0_dm0_tx_de-skew = <7>;
		cs0_dq0_tx_de-skew = <7>;
		cs0_dq1_tx_de-skew = <7>;
		cs0_dq2_tx_de-skew = <7>;
		cs0_dq3_tx_de-skew = <7>;
		cs0_dq4_tx_de-skew = <7>;
		cs0_dq5_tx_de-skew = <7>;
		cs0_dq6_tx_de-skew = <7>;
		cs0_dq7_tx_de-skew = <7>;
		cs0_dqs0p_tx_de-skew = <7>;
		cs0_dqs0n_tx_de-skew = <7>;
		cs0_dm1_tx_de-skew = <7>;
		cs0_dq8_tx_de-skew = <7>;
		cs0_dq9_tx_de-skew = <7>;
		cs0_dq10_tx_de-skew = <7>;
		cs0_dq11_tx_de-skew = <7>;
		cs0_dq12_tx_de-skew = <7>;
		cs0_dq13_tx_de-skew = <7>;
		cs0_dq14_tx_de-skew = <7>;
		cs0_dq15_tx_de-skew = <7>;
		cs0_dqs1p_tx_de-skew = <7>;
		cs0_dqs1n_tx_de-skew = <7>;

		/* cs0_skew_b */
		cs0_dm2_rx_de-skew = <7>;
		cs0_dq16_rx_de-skew = <7>;
		cs0_dq17_rx_de-skew = <7>;
		cs0_dq18_rx_de-skew = <7>;
		cs0_dq19_rx_de-skew = <7>;
		cs0_dq20_rx_de-skew = <7>;
		cs0_dq21_rx_de-skew = <7>;
		cs0_dq22_rx_de-skew = <7>;
		cs0_dq23_rx_de-skew = <7>;
		cs0_dqs2p_rx_de-skew = <14>;
		cs0_dqs2n_rx_de-skew = <14>;
		cs0_dm3_rx_de-skew = <7>;
		cs0_dq24_rx_de-skew = <7>;
		cs0_dq25_rx_de-skew = <7>;
		cs0_dq26_rx_de-skew = <7>;
		cs0_dq27_rx_de-skew = <7>;
		cs0_dq28_rx_de-skew = <7>;
		cs0_dq29_rx_de-skew = <7>;
		cs0_dq30_rx_de-skew = <7>;
		cs0_dq31_rx_de-skew = <7>;
		cs0_dqs3p_rx_de-skew = <14>;
		cs0_dqs3n_rx_de-skew = <14>;
		cs0_dm2_tx_de-skew = <7>;
		cs0_dq16_tx_de-skew = <7>;
		cs0_dq17_tx_de-skew = <7>;
		cs0_dq18_tx_de-skew = <7>;
		cs0_dq19_tx_de-skew = <7>;
		cs0_dq20_tx_de-skew = <7>;
		cs0_dq21_tx_de-skew = <7>;
		cs0_dq22_tx_de-skew = <7>;
		cs0_dq23_tx_de-skew = <7>;
		cs0_dqs2p_tx_de-skew = <7>;
		cs0_dqs2n_tx_de-skew = <7>;
		cs0_dm3_tx_de-skew = <7>;
		cs0_dq24_tx_de-skew = <7>;
		cs0_dq25_tx_de-skew = <7>;
		cs0_dq26_tx_de-skew = <7>;
		cs0_dq27_tx_de-skew = <7>;
		cs0_dq28_tx_de-skew = <7>;
		cs0_dq29_tx_de-skew = <7>;
		cs0_dq30_tx_de-skew = <7>;
		cs0_dq31_tx_de-skew = <7>;
		cs0_dqs3p_tx_de-skew = <7>;
		cs0_dqs3n_tx_de-skew = <7>;

		/* cs1_skew_a */
		cs1_dm0_rx_de-skew = <7>;
		cs1_dq0_rx_de-skew = <7>;
		cs1_dq1_rx_de-skew = <7>;
		cs1_dq2_rx_de-skew = <7>;
		cs1_dq3_rx_de-skew = <7>;
		cs1_dq4_rx_de-skew = <7>;
		cs1_dq5_rx_de-skew = <7>;
		cs1_dq6_rx_de-skew = <7>;
		cs1_dq7_rx_de-skew = <7>;
		cs1_dqs0p_rx_de-skew = <14>;
		cs1_dqs0n_rx_de-skew = <14>;
		cs1_dm1_rx_de-skew = <7>;
		cs1_dq8_rx_de-skew = <7>;
		cs1_dq9_rx_de-skew = <7>;
		cs1_dq10_rx_de-skew = <7>;
		cs1_dq11_rx_de-skew = <7>;
		cs1_dq12_rx_de-skew = <7>;
		cs1_dq13_rx_de-skew = <7>;
		cs1_dq14_rx_de-skew = <7>;
		cs1_dq15_rx_de-skew = <7>;
		cs1_dqs1p_rx_de-skew = <14>;
		cs1_dqs1n_rx_de-skew = <14>;
		cs1_dm0_tx_de-skew = <7>;
		cs1_dq0_tx_de-skew = <7>;
		cs1_dq1_tx_de-skew = <7>;
		cs1_dq2_tx_de-skew = <7>;
		cs1_dq3_tx_de-skew = <7>;
		cs1_dq4_tx_de-skew = <7>;
		cs1_dq5_tx_de-skew = <7>;
		cs1_dq6_tx_de-skew = <7>;
		cs1_dq7_tx_de-skew = <7>;
		cs1_dqs0p_tx_de-skew = <7>;
		cs1_dqs0n_tx_de-skew = <7>;
		cs1_dm1_tx_de-skew = <7>;
		cs1_dq8_tx_de-skew = <7>;
		cs1_dq9_tx_de-skew = <7>;
		cs1_dq10_tx_de-skew = <7>;
		cs1_dq11_tx_de-skew = <7>;
		cs1_dq12_tx_de-skew = <7>;
		cs1_dq13_tx_de-skew = <7>;
		cs1_dq14_tx_de-skew = <7>;
		cs1_dq15_tx_de-skew = <7>;
		cs1_dqs1p_tx_de-skew = <7>;
		cs1_dqs1n_tx_de-skew = <7>;

		/* cs1_skew_b */
		cs1_dm2_rx_de-skew = <7>;
		cs1_dq16_rx_de-skew = <7>;
		cs1_dq17_rx_de-skew = <7>;
		cs1_dq18_rx_de-skew = <7>;
		cs1_dq19_rx_de-skew = <7>;
		cs1_dq20_rx_de-skew = <7>;
		cs1_dq21_rx_de-skew = <7>;
		cs1_dq22_rx_de-skew = <7>;
		cs1_dq23_rx_de-skew = <7>;
		cs1_dqs2p_rx_de-skew = <14>;
		cs1_dqs2n_rx_de-skew = <14>;
		cs1_dm3_rx_de-skew = <7>;
		cs1_dq24_rx_de-skew = <7>;
		cs1_dq25_rx_de-skew = <7>;
		cs1_dq26_rx_de-skew = <7>;
		cs1_dq27_rx_de-skew = <7>;
		cs1_dq28_rx_de-skew = <7>;
		cs1_dq29_rx_de-skew = <7>;
		cs1_dq30_rx_de-skew = <7>;
		cs1_dq31_rx_de-skew = <7>;
		cs1_dqs3p_rx_de-skew = <14>;
		cs1_dqs3n_rx_de-skew = <14>;
		cs1_dm2_tx_de-skew = <7>;
		cs1_dq16_tx_de-skew = <7>;
		cs1_dq17_tx_de-skew = <7>;
		cs1_dq18_tx_de-skew = <7>;
		cs1_dq19_tx_de-skew = <7>;
		cs1_dq20_tx_de-skew = <7>;
		cs1_dq21_tx_de-skew = <7>;
		cs1_dq22_tx_de-skew = <7>;
		cs1_dq23_tx_de-skew = <7>;
		cs1_dqs2p_tx_de-skew = <7>;
		cs1_dqs2n_tx_de-skew = <7>;
		cs1_dm3_tx_de-skew = <7>;
		cs1_dq24_tx_de-skew = <7>;
		cs1_dq25_tx_de-skew = <7>;
		cs1_dq26_tx_de-skew = <7>;
		cs1_dq27_tx_de-skew = <7>;
		cs1_dq28_tx_de-skew = <7>;
		cs1_dq29_tx_de-skew = <7>;
		cs1_dq30_tx_de-skew = <7>;
		cs1_dq31_tx_de-skew = <7>;
		cs1_dqs3p_tx_de-skew = <7>;
		cs1_dqs3n_tx_de-skew = <7>;
	};
};