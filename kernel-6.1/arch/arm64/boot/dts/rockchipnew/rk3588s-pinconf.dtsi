// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 */

&pinctrl {
	/omit-if-no-ref/
	pcfg_pull_up: pcfg-pull-up {
		bias-pull-up;
	};

	/omit-if-no-ref/
	pcfg_pull_down: pcfg-pull-down {
		bias-pull-down;
	};

	/omit-if-no-ref/
	pcfg_pull_none: pcfg-pull-none {
		bias-disable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_0: pcfg-pull-none-drv-level-0 {
		bias-disable;
		drive-strength = <0>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_1: pcfg-pull-none-drv-level-1 {
		bias-disable;
		drive-strength = <1>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_2: pcfg-pull-none-drv-level-2 {
		bias-disable;
		drive-strength = <2>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_3: pcfg-pull-none-drv-level-3 {
		bias-disable;
		drive-strength = <3>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_4: pcfg-pull-none-drv-level-4 {
		bias-disable;
		drive-strength = <4>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_5: pcfg-pull-none-drv-level-5 {
		bias-disable;
		drive-strength = <5>;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_6: pcfg-pull-none-drv-level-6 {
		bias-disable;
		drive-strength = <6>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_0: pcfg-pull-up-drv-level-0 {
		bias-pull-up;
		drive-strength = <0>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_1: pcfg-pull-up-drv-level-1 {
		bias-pull-up;
		drive-strength = <1>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_2: pcfg-pull-up-drv-level-2 {
		bias-pull-up;
		drive-strength = <2>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_3: pcfg-pull-up-drv-level-3 {
		bias-pull-up;
		drive-strength = <3>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_4: pcfg-pull-up-drv-level-4 {
		bias-pull-up;
		drive-strength = <4>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_5: pcfg-pull-up-drv-level-5 {
		bias-pull-up;
		drive-strength = <5>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_drv_level_6: pcfg-pull-up-drv-level-6 {
		bias-pull-up;
		drive-strength = <6>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_0: pcfg-pull-down-drv-level-0 {
		bias-pull-down;
		drive-strength = <0>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_1: pcfg-pull-down-drv-level-1 {
		bias-pull-down;
		drive-strength = <1>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_2: pcfg-pull-down-drv-level-2 {
		bias-pull-down;
		drive-strength = <2>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_3: pcfg-pull-down-drv-level-3 {
		bias-pull-down;
		drive-strength = <3>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_4: pcfg-pull-down-drv-level-4 {
		bias-pull-down;
		drive-strength = <4>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_5: pcfg-pull-down-drv-level-5 {
		bias-pull-down;
		drive-strength = <5>;
	};

	/omit-if-no-ref/
	pcfg_pull_down_drv_level_6: pcfg-pull-down-drv-level-6 {
		bias-pull-down;
		drive-strength = <6>;
	};

	/omit-if-no-ref/
	pcfg_pull_up_smt: pcfg-pull-up-smt {
		bias-pull-up;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_down_smt: pcfg-pull-down-smt {
		bias-pull-down;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_smt: pcfg-pull-none-smt {
		bias-disable;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_0_smt: pcfg-pull-none-drv-level-0-smt {
		bias-disable;
		drive-strength = <0>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_1_smt: pcfg-pull-none-drv-level-1-smt {
		bias-disable;
		drive-strength = <1>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_2_smt: pcfg-pull-none-drv-level-2-smt {
		bias-disable;
		drive-strength = <2>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_3_smt: pcfg-pull-none-drv-level-3-smt {
		bias-disable;
		drive-strength = <3>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_4_smt: pcfg-pull-none-drv-level-4-smt {
		bias-disable;
		drive-strength = <4>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_5_smt: pcfg-pull-none-drv-level-5-smt {
		bias-disable;
		drive-strength = <5>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_pull_none_drv_level_6_smt: pcfg-pull-none-drv-level-6-smt {
		bias-disable;
		drive-strength = <6>;
		input-schmitt-enable;
	};

	/omit-if-no-ref/
	pcfg_output_high: pcfg-output-high {
		output-high;
	};

	/omit-if-no-ref/
	pcfg_output_high_pull_up: pcfg-output-high-pull-up {
		output-high;
		bias-pull-up;
	};

	/omit-if-no-ref/
	pcfg_output_high_pull_down: pcfg-output-high-pull-down {
		output-high;
		bias-pull-down;
	};

	/omit-if-no-ref/
	pcfg_output_high_pull_none: pcfg-output-high-pull-none {
		output-high;
		bias-disable;
	};

	/omit-if-no-ref/
	pcfg_output_low: pcfg-output-low {
		output-low;
	};

	/omit-if-no-ref/
	pcfg_output_low_pull_up: pcfg-output-low-pull-up {
		output-low;
		bias-pull-up;
	};

	/omit-if-no-ref/
	pcfg_output_low_pull_down: pcfg-output-low-pull-down {
		output-low;
		bias-pull-down;
	};

	/omit-if-no-ref/
	pcfg_output_low_pull_none: pcfg-output-low-pull-none {
		output-low;
		bias-disable;
	};
};
