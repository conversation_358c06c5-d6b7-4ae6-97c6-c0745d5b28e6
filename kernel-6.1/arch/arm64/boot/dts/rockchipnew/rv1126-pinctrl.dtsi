/* DTS-XLATE-RV1126B */
// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Rockchip Electronics Co., Ltd.
 */

#include <dt-bindings/pinctrl/rockchip.h>
#include "rockchip-pinconf.dtsi"

/*
 * This file is auto generated by pin2dts tool, please keep these code
 * by adding changes at end of this file.
 */
&pinctrl {
	acodec {
		/omit-if-no-ref/
		acodec_pins: acodec-pins {
			rockchip,pins =
				/* acodec_adc_clk */
				<7 RK_PA1 3 &pcfg_pull_none>,
				/* acodec_adc_data */
				<7 RK_PA7 3 &pcfg_pull_none>,
				/* acodec_adc_sync */
				<7 RK_PA4 3 &pcfg_pull_none>,
				/* acodec_dac_clk */
				<7 RK_PA0 3 &pcfg_pull_none>,
				/* acodec_dac_datal */
				<7 RK_PA6 3 &pcfg_pull_none>,
				/* acodec_dac_datar */
				<7 RK_PA5 3 &pcfg_pull_none>,
				/* acodec_dac_sync */
				<7 RK_PA3 3 &pcfg_pull_none>;
		};
	};
	auddsm {
		/omit-if-no-ref/
		auddsm_pins: auddsm-pins {
			rockchip,pins =
				/* auddsm_ln */
				<7 RK_PA3 5 &pcfg_pull_none>,
				/* auddsm_lp */
				<7 RK_PA5 5 &pcfg_pull_none>,
				/* auddsm_rn */
				<7 RK_PB0 5 &pcfg_pull_none>,
				/* auddsm_rp */
				<7 RK_PB1 5 &pcfg_pull_none>;
		};
	};
	audpwm {
		/omit-if-no-ref/
		audpwmm0_pins: audpwmm0-pins {
			rockchip,pins =
				/* audpwm_l_m0 */
				<7 RK_PB0 3 &pcfg_pull_none>,
				/* audpwm_r_m0 */
				<7 RK_PB1 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		audpwmm1_pins: audpwmm1-pins {
			rockchip,pins =
				/* audpwm_l_m1 */
				<7 RK_PA3 4 &pcfg_pull_none>,
				/* audpwm_r_m1 */
				<7 RK_PA5 4 &pcfg_pull_none>;
		};
	};
	can {
		/omit-if-no-ref/
		canm0_pins: canm0-pins {
			rockchip,pins =
				/* can_rxd_m0 */
				<5 RK_PD4 3 &pcfg_pull_none>,
				/* can_txd_m0 */
				<5 RK_PD5 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		canm1_pins: canm1-pins {
			rockchip,pins =
				/* can_rxd_m1 */
				<6 RK_PA2 5 &pcfg_pull_none>,
				/* can_txd_m1 */
				<6 RK_PA3 5 &pcfg_pull_none>;
		};
	};
	cif {
		/omit-if-no-ref/
		cifm0_dvp_ctl: cifm0-dvp-ctl {
			rockchip,pins =
				/* cif_clkin_m0 */
				<6 RK_PC1 1 &pcfg_pull_none>,
				/* cif_clkout_m0 */
				<6 RK_PC2 1 &pcfg_pull_none>,
				/* cif_d0_m0 */
				<6 RK_PA0 1 &pcfg_pull_none>,
				/* cif_d10_m0 */
				<6 RK_PB2 1 &pcfg_pull_none>,
				/* cif_d11_m0 */
				<6 RK_PB3 1 &pcfg_pull_none>,
				/* cif_d12_m0 */
				<6 RK_PB4 1 &pcfg_pull_none>,
				/* cif_d13_m0 */
				<6 RK_PB5 1 &pcfg_pull_none>,
				/* cif_d14_m0 */
				<6 RK_PB6 1 &pcfg_pull_none>,
				/* cif_d15_m0 */
				<6 RK_PB7 1 &pcfg_pull_none>,
				/* cif_d1_m0 */
				<6 RK_PA1 1 &pcfg_pull_none>,
				/* cif_d2_m0 */
				<6 RK_PA2 1 &pcfg_pull_none>,
				/* cif_d3_m0 */
				<6 RK_PA3 1 &pcfg_pull_none>,
				/* cif_d4_m0 */
				<6 RK_PA4 1 &pcfg_pull_none>,
				/* cif_d5_m0 */
				<6 RK_PA5 1 &pcfg_pull_none>,
				/* cif_d6_m0 */
				<6 RK_PA6 1 &pcfg_pull_none>,
				/* cif_d7_m0 */
				<6 RK_PA7 1 &pcfg_pull_none>,
				/* cif_d8_m0 */
				<6 RK_PB0 1 &pcfg_pull_none>,
				/* cif_d9_m0 */
				<6 RK_PB1 1 &pcfg_pull_none>,
				/* cif_hsync_m0 */
				<6 RK_PC3 1 &pcfg_pull_none>,
				/* cif_vsync_m0 */
				<6 RK_PC0 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		cifm1_dvp_ctl: cifm1-dvp-ctl {
			rockchip,pins =
				/* cif_clkin_m1 */
				<5 RK_PC6 3 &pcfg_pull_none>,
				/* cif_clkout_m1 */
				<5 RK_PC5 3 &pcfg_pull_none>,
				/* cif_d0_m1 */
				<5 RK_PA0 3 &pcfg_pull_none>,
				/* cif_d10_m1 */
				<5 RK_PB6 3 &pcfg_pull_none>,
				/* cif_d11_m1 */
				<5 RK_PB7 3 &pcfg_pull_none>,
				/* cif_d12_m1 */
				<5 RK_PC0 3 &pcfg_pull_none>,
				/* cif_d13_m1 */
				<5 RK_PC1 3 &pcfg_pull_none>,
				/* cif_d14_m1 */
				<5 RK_PC2 3 &pcfg_pull_none>,
				/* cif_d15_m1 */
				<5 RK_PC3 3 &pcfg_pull_none>,
				/* cif_d1_m1 */
				<5 RK_PA1 3 &pcfg_pull_none>,
				/* cif_d2_m1 */
				<5 RK_PA2 3 &pcfg_pull_none>,
				/* cif_d3_m1 */
				<5 RK_PA7 3 &pcfg_pull_none>,
				/* cif_d4_m1 */
				<5 RK_PB0 3 &pcfg_pull_none>,
				/* cif_d5_m1 */
				<5 RK_PB1 3 &pcfg_pull_none>,
				/* cif_d6_m1 */
				<5 RK_PB2 3 &pcfg_pull_none>,
				/* cif_d7_m1 */
				<5 RK_PB3 3 &pcfg_pull_none>,
				/* cif_d8_m1 */
				<5 RK_PB4 3 &pcfg_pull_none>,
				/* cif_d9_m1 */
				<5 RK_PB5 3 &pcfg_pull_none>,
				/* cif_hsync_m1 */
				<5 RK_PC7 3 &pcfg_pull_none>,
				/* cif_vsync_m1 */
				<5 RK_PC4 3 &pcfg_pull_none>;
		};
	};
	clk {
		/omit-if-no-ref/
		clkm0_out_ethernet: clkm0-out-ethernet {
			rockchip,pins =
				/* clkm0_out_ethernet */
				<6 RK_PC1 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		clkm1_out_ethernet: clkm1-out-ethernet {
			rockchip,pins =
				/* clkm1_out_ethernet */
				<5 RK_PC1 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		clk_32k: clk-32k {
			rockchip,pins =
				/* clk_32k */
				<0 RK_PA2 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		clk_ref: clk-ref {
			rockchip,pins =
				/* clk_ref */
				<0 RK_PA0 1 &pcfg_pull_none>;
		};
	};
	emmc {
		/omit-if-no-ref/
		emmc_rstnout: emmc-rstnout {
			rockchip,pins =
				/* emmc_rstn */
				<1 RK_PB7 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		emmc_bus8: emmc-bus8 {
			rockchip,pins =
				/* emmc_d0 */
				<1 RK_PA0 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d1 */
				<1 RK_PA1 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d2 */
				<1 RK_PA2 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d3 */
				<1 RK_PA3 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d4 */
				<1 RK_PA4 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d5 */
				<1 RK_PA5 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d6 */
				<1 RK_PA6 2 &pcfg_pull_up_drv_level_2>,
				/* emmc_d7 */
				<1 RK_PA7 2 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		emmc_clk: emmc-clk {
			rockchip,pins =
				/* emmc_clko */
				<1 RK_PB3 2 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		emmc_cmd: emmc-cmd {
			rockchip,pins =
				/* emmc_cmd */
				<1 RK_PB1 2 &pcfg_pull_up_drv_level_2>;
		};
	};
	flash {
		/omit-if-no-ref/
		flash_pins: flash-pins {
			rockchip,pins =
				/* flash_ale */
				<1 RK_PB4 1 &pcfg_pull_none>,
				/* flash_cle */
				<1 RK_PB3 1 &pcfg_pull_none>,
				/* flash_cs0n */
				<1 RK_PB0 1 &pcfg_pull_none>,
				/* flash_d0 */
				<1 RK_PA0 1 &pcfg_pull_none>,
				/* flash_d1 */
				<1 RK_PA1 1 &pcfg_pull_none>,
				/* flash_d2 */
				<1 RK_PA2 1 &pcfg_pull_none>,
				/* flash_d3 */
				<1 RK_PA3 1 &pcfg_pull_none>,
				/* flash_d4 */
				<1 RK_PA4 1 &pcfg_pull_none>,
				/* flash_d5 */
				<1 RK_PA5 1 &pcfg_pull_none>,
				/* flash_d6 */
				<1 RK_PA6 1 &pcfg_pull_none>,
				/* flash_d7 */
				<1 RK_PA7 1 &pcfg_pull_none>,
				/* flash_rdn */
				<1 RK_PB6 1 &pcfg_pull_none>,
				/* flash_rdyn */
				<1 RK_PB5 1 &pcfg_pull_none>,
				/* flash_trig_in */
				<3 RK_PB3 4 &pcfg_pull_none>,
				/* flash_trig_out */
				<3 RK_PB2 4 &pcfg_pull_none>,
				/* flash_vol_sel */
				<0 RK_PB3 1 &pcfg_pull_none>,
				/* flash_wpn */
				<1 RK_PB7 1 &pcfg_pull_none>,
				/* flash_wrn */
				<1 RK_PB1 1 &pcfg_pull_none>;
		};
	};
	fspi {
		/omit-if-no-ref/
		fspi_cs1: fspi-cs1 {
			rockchip,pins =
				/* fspi_cs1n */
				<1 RK_PA5 3 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		fspi_pins: fspi-pins {
			rockchip,pins =
				/* fspi_clk */
				<1 RK_PB7 3 &pcfg_pull_down>,
				/* fspi_cs0n */
				<1 RK_PB0 3 &pcfg_pull_up>,
				/* fspi_d0 */
				<1 RK_PB4 3 &pcfg_pull_up>,
				/* fspi_d1 */
				<1 RK_PB5 3 &pcfg_pull_up>,
				/* fspi_d2 */
				<1 RK_PB2 3 &pcfg_pull_up>,
				/* fspi_d3 */
				<1 RK_PB6 3 &pcfg_pull_up>;
		};
	};
	i2c0 {
		/omit-if-no-ref/
		i2c0_xfer: i2c0-xfer {
			rockchip,pins =
				/* i2c0_scl */
				<0 RK_PC2 1 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c0_sda */
				<0 RK_PC3 1 &pcfg_pull_none_drv_level_0_smt>;
		};
	};
	i2c1 {
		/omit-if-no-ref/
		i2c1_xfer: i2c1-xfer {
			rockchip,pins =
				/* i2c1_scl */
				<4 RK_PA1 1 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c1_sda */
				<4 RK_PA0 1 &pcfg_pull_none_drv_level_0_smt>;
		};
	};
	i2c2 {
		/omit-if-no-ref/
		i2c2_xfer: i2c2-xfer {
			rockchip,pins =
				/* i2c2_scl */
				<0 RK_PD0 1 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c2_sda */
				<0 RK_PD1 1 &pcfg_pull_none_drv_level_0_smt>;
		};
	};
	i2c3 {
		/omit-if-no-ref/
		i2c3m0_xfer: i2c3m0-xfer {
			rockchip,pins =
				/* i2c3_scl_m0 */
				<6 RK_PA0 5 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c3_sda_m0 */
				<6 RK_PA1 5 &pcfg_pull_none_drv_level_0_smt>;
		};
		/omit-if-no-ref/
		i2c3m1_xfer: i2c3m1-xfer {
			rockchip,pins =
				/* i2c3_scl_m1 */
				<5 RK_PD0 7 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c3_sda_m1 */
				<5 RK_PD1 7 &pcfg_pull_none_drv_level_0_smt>;
		};
		/omit-if-no-ref/
		i2c3m2_xfer: i2c3m2-xfer {
			rockchip,pins =
				/* i2c3_scl_m2 */
				<4 RK_PA4 3 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c3_sda_m2 */
				<4 RK_PA5 3 &pcfg_pull_none_drv_level_0_smt>;
		};
	};
	i2c2 {
		/omit-if-no-ref/
		i2c2m0_xfer: i2c2m0-xfer {
			rockchip,pins =
				/* i2c2_scl_m0 */
				<5 RK_PD4 7 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c2_sda_m0 */
				<5 RK_PD5 7 &pcfg_pull_none_drv_level_0_smt>;
		};
		/omit-if-no-ref/
		i2c2m1_xfer: i2c2m1-xfer {
			rockchip,pins =
				/* i2c2_scl_m1 */
				<7 RK_PB0 4 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c2_sda_m1 */
				<7 RK_PB1 4 &pcfg_pull_none_drv_level_0_smt>;
		};
	};
	i2c3 {
		/omit-if-no-ref/
		i2c3m0_xfer: i2c3m0-xfer {
			rockchip,pins =
				/* i2c3_scl_m0 */
				<5 RK_PA1 7 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c3_sda_m0 */
				<5 RK_PA7 7 &pcfg_pull_none_drv_level_0_smt>;
		};
		/omit-if-no-ref/
		i2c3m1_xfer: i2c3m1-xfer {
			rockchip,pins =
				/* i2c3_scl_m1 */
				<6 RK_PA4 5 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c3_sda_m1 */
				<6 RK_PA5 5 &pcfg_pull_none_drv_level_0_smt>;
		};
		/omit-if-no-ref/
		i2c3m2_xfer: i2c3m2-xfer {
			rockchip,pins =
				/* i2c3_scl_m2 */
				<3 RK_PB6 4 &pcfg_pull_none_drv_level_0_smt>,
				/* i2c3_sda_m2 */
				<3 RK_PB7 4 &pcfg_pull_none_drv_level_0_smt>;
		};
	};
	i2c4 {
		/omit-if-no-ref/
		i2c4m0_xfer: i2c4m0-pins {
			rockchip,pins =
				/* i2c4_scl_m0 */
				<3 RK_PB4 5 &pcfg_pull_none>,
				/* i2c4_sda_m0 */
				<3 RK_PB5 5 &pcfg_pull_none>;
		};

		/omit-if-no-ref/
		i2c4m1_pins: i2c4m1-pins {
			rockchip,pins =
				/* i2c4_scl_m1 */
				<6 RK_PA2 7 &pcfg_pull_none>,
				/* i2c4_sda_m1 */
				<6 RK_PA3 7 &pcfg_pull_none>;
		};

		/omit-if-no-ref/
		i2c4m2_pins: i2c4m2-pins {
			rockchip,pins =
				/* i2c4_scl_m2 */
				<4 RK_PA7 6 &pcfg_pull_none>,
				/* i2c4_sda_m2 */
				<4 RK_PA6 6 &pcfg_pull_none>;
		};

		/omit-if-no-ref/
		i2c4m3_pins: i2c4m3-pins {
			rockchip,pins =
				/* i2c4_scl_m3 */
				<7 RK_PA1 2 &pcfg_pull_none>,
				/* i2c4_sda_m3 */
				<7 RK_PA4 2 &pcfg_pull_none>;
		};
	};

	i2c5 {
		/omit-if-no-ref/
		i2c5m0_xfer: i2c5m0-pins {
			rockchip,pins =
				/* i2c5_scl_m0 */
				<0 RK_PC4 4 &pcfg_pull_none>,
				/* i2c5_sda_m0 */
				<0 RK_PC5 4 &pcfg_pull_none>;
		};

		/omit-if-no-ref/
		i2c5m1_pins: i2c5m1-pins {
			rockchip,pins =
				/* i2c5_scl_m1 */
				<3 RK_PB6 5 &pcfg_pull_none>,
				/* i2c5_sda_m1 */
				<3 RK_PB7 5 &pcfg_pull_none>;
		};

		/omit-if-no-ref/
		i2c5m2_pins: i2c5m2-pins {
			rockchip,pins =
				/* i2c5_scl_m2 */
				<5 RK_PA1 2 &pcfg_pull_none>,
				/* i2c5_sda_m2 */
				<5 RK_PA7 6 &pcfg_pull_none>;
		};

		/omit-if-no-ref/
		i2c5m3_pins: i2c5m3-pins {
			rockchip,pins =
				/* i2c5_scl_m3 */
				<6 RK_PA4 7 &pcfg_pull_none>,
				/* i2c5_sda_m3 */
				<6 RK_PA5 7 &pcfg_pull_none>;
		};
	};

	i2s0 {
		/omit-if-no-ref/
		i2s0m0_lrck_rx: i2s0m0-lrck-rx {
			rockchip,pins =
				/* i2s0m0_lrck_rx */
				<7 RK_PA4 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_lrck_tx: i2s0m0-lrck-tx {
			rockchip,pins =
				/* sai0m0_lrck_pins */
				<7 RK_PA3 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai0m0_mclk_pins: i2s0m0-mclk {
			rockchip,pins =
				/* sai0m0_mclk_pins */
				<7 RK_PA2 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_sclk_rx: i2s0m0-sclk-rx {
			rockchip,pins =
				/* i2s0m0_sclk_rx */
				<7 RK_PA1 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_sclk_tx: i2s0m0-sclk-tx {
			rockchip,pins =
				/* sai0m0_sclk_pins */
				<7 RK_PA0 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_sdi0: i2s0m0-sdi0 {
			rockchip,pins =
				/* sai0m0_sdi0_pins */
				<7 RK_PA6 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_sdo0: i2s0m0-sdo0 {
			rockchip,pins =
				/* sai0m0_sdo0_pins */
				<7 RK_PA5 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_sdo1_sdi3: i2s0m0-sdo1-sdi3 {
			rockchip,pins =
				/* i2s0m0_sdo1_sdi3 */
				<7 RK_PA7 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_sdo2_sdi2: i2s0m0-sdo2-sdi2 {
			rockchip,pins =
				/* i2s0m0_sdo2_sdi2 */
				<7 RK_PB0 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m0_sdo3_sdi1: i2s0m0-sdo3-sdi1 {
			rockchip,pins =
				/* i2s0m0_sdo3_sdi1 */
				<7 RK_PB1 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m1_lrck_rx: i2s0m1-lrck-rx {
			rockchip,pins =
				/* i2s0m1_lrck_rx */
				<6 RK_PA6 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai0m1_lrck_pins: i2s0m1-lrck-tx {
			rockchip,pins =
				/* sai0m1_lrck_pins */
				<6 RK_PA1 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai0m1_mclk_pins: i2s0m1-mclk {
			rockchip,pins =
				/* sai0m1_mclk_pins */
				<6 RK_PA4 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m1_sclk_rx: i2s0m1-sclk-rx {
			rockchip,pins =
				/* i2s0m1_sclk_rx */
				<6 RK_PA5 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai0m1_sclk_pins: i2s0m1-sclk-tx {
			rockchip,pins =
				/* sai0m1_sclk_pins */
				<6 RK_PA0 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai0m1_sdi0_pins: i2s0m1-sdi0 {
			rockchip,pins =
				/* sai0m1_sdi0_pins */
				<6 RK_PA3 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai0m1_sdo0_pins: i2s0m1-sdo0 {
			rockchip,pins =
				/* sai0m1_sdo0_pins */
				<6 RK_PA2 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m1_sdo1_sdi3: i2s0m1-sdo1-sdi3 {
			rockchip,pins =
				/* i2s0m1_sdo1_sdi3 */
				<6 RK_PA7 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m1_sdo2_sdi2: i2s0m1-sdo2-sdi2 {
			rockchip,pins =
				/* i2s0m1_sdo2_sdi2 */
				<6 RK_PB0 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s0m1_sdo3_sdi1: i2s0m1-sdo3-sdi1 {
			rockchip,pins =
				/* i2s0m1_sdo3_sdi1 */
				<6 RK_PB1 3 &pcfg_pull_none>;
		};
	};
	i2s1 {
		/omit-if-no-ref/
		i2s1m0_lrck: i2s1m0-lrck {
			rockchip,pins =
				/* i2s1m0_lrck */
				<1 RK_PB4 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai1m0_mclk_pins: i2s1m0-mclk {
			rockchip,pins =
				/* sai1m0_mclk_pins */
				<1 RK_PB0 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m0_sclk: i2s1m0-sclk {
			rockchip,pins =
				/* i2s1m0_sclk */
				<1 RK_PB5 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m0_sdi: i2s1m0-sdi {
			rockchip,pins =
				/* i2s1m0_sdi */
				<1 RK_PB6 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m0_sdo: i2s1m0-sdo {
			rockchip,pins =
				/* i2s1m0_sdo */
				<1 RK_PB2 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m1_lrck: i2s1m1-lrck {
			rockchip,pins =
				/* i2s1m1_lrck */
				<4 RK_PA5 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai1m1_mclk_pins: i2s1m1-mclk {
			rockchip,pins =
				/* sai1m1_mclk_pins */
				<4 RK_PA3 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m1_sclk: i2s1m1-sclk {
			rockchip,pins =
				/* i2s1m1_sclk */
				<4 RK_PA4 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m1_sdi: i2s1m1-sdi {
			rockchip,pins =
				/* i2s1m1_sdi */
				<4 RK_PA6 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m1_sdo: i2s1m1-sdo {
			rockchip,pins =
				/* i2s1m1_sdo */
				<4 RK_PA7 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m2_lrck: i2s1m2-lrck {
			rockchip,pins =
				/* i2s1m2_lrck */
				<5 RK_PC6 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m2_mclk: i2s1m2-mclk {
			rockchip,pins =
				/* i2s1m2_mclk */
				<5 RK_PC3 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m2_sclk: i2s1m2-sclk {
			rockchip,pins =
				/* i2s1m2_sclk */
				<5 RK_PC5 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m2_sdi: i2s1m2-sdi {
			rockchip,pins =
				/* i2s1m2_sdi */
				<5 RK_PC7 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s1m2_sdo: i2s1m2-sdo {
			rockchip,pins =
				/* i2s1m2_sdo */
				<5 RK_PC4 6 &pcfg_pull_none>;
		};
	};
	i2s2 {
		/omit-if-no-ref/
		i2s2m0_lrck: i2s2m0-lrck {
			rockchip,pins =
				/* i2s2m0_lrck */
				<3 RK_PB5 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai2m0_mclk_pins: i2s2m0-mclk {
			rockchip,pins =
				/* sai2m0_mclk_pins */
				<3 RK_PB6 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s2m0_sclk: i2s2m0-sclk {
			rockchip,pins =
				/* i2s2m0_sclk */
				<3 RK_PB4 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s2m0_sdi: i2s2m0-sdi {
			rockchip,pins =
				/* i2s2m0_sdi */
				<3 RK_PB3 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s2m0_sdo: i2s2m0-sdo {
			rockchip,pins =
				/* i2s2m0_sdo */
				<3 RK_PB2 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s2m1_lrck: i2s2m1-lrck {
			rockchip,pins =
				/* i2s2m1_lrck */
				<5 RK_PA6 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sai2m1_mclk_pins: i2s2m1-mclk {
			rockchip,pins =
				/* sai2m1_mclk_pins */
				<5 RK_PA7 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s2m1_sclk: i2s2m1-sclk {
			rockchip,pins =
				/* i2s2m1_sclk */
				<5 RK_PA5 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s2m1_sdi: i2s2m1-sdi {
			rockchip,pins =
				/* i2s2m1_sdi */
				<5 RK_PA4 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		i2s2m1_sdo: i2s2m1-sdo {
			rockchip,pins =
				/* i2s2m1_sdo */
				<5 RK_PA3 2 &pcfg_pull_none>;
		};
	};
	lcdc {
		/omit-if-no-ref/
		lcdc_ctl: lcdc-ctl {
			rockchip,pins =
				/* lcdc_clk */
				<5 RK_PD3 1 &pcfg_pull_none_drv_level_8>,
				/* lcdc_d0 */
				<5 RK_PA0 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d1 */
				<5 RK_PA1 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d2 */
				<5 RK_PA2 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d3 */
				<5 RK_PA3 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d4 */
				<5 RK_PA4 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d5 */
				<5 RK_PA5 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d6 */
				<5 RK_PA6 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d7 */
				<5 RK_PA7 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d8 */
				<5 RK_PB0 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d9 */
				<5 RK_PB1 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d10 */
				<5 RK_PB2 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d11 */
				<5 RK_PB3 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d12 */
				<5 RK_PB4 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d13 */
				<5 RK_PB5 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d14 */
				<5 RK_PB6 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d15 */
				<5 RK_PB7 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d16 */
				<5 RK_PC0 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d17 */
				<5 RK_PC1 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d18 */
				<5 RK_PC2 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d19 */
				<5 RK_PC3 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d20 */
				<5 RK_PC4 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d21 */
				<5 RK_PC5 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d22 */
				<5 RK_PC6 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_d23 */
				<5 RK_PC7 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_den */
				<5 RK_PD0 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_hsync */
				<5 RK_PD1 1 &pcfg_pull_none_drv_level_2>,
				/* lcdc_vsync */
				<5 RK_PD2 1 &pcfg_pull_none_drv_level_2>;
		};
	};
	mcu {
		/omit-if-no-ref/
		mcu_pins: mcu-pins {
			rockchip,pins =
				/* mcu_jtag_tck */
				<2 RK_PA2 4 &pcfg_pull_none>,
				/* mcu_jtag_tdi */
				<2 RK_PA5 4 &pcfg_pull_none>,
				/* mcu_jtag_tdo */
				<2 RK_PA4 4 &pcfg_pull_none>,
				/* mcu_jtag_tms */
				<2 RK_PA3 4 &pcfg_pull_none>,
				/* mcu_jtag_trstn */
				<2 RK_PA1 4 &pcfg_pull_none>;
		};
	};
	mipicsi {
		/omit-if-no-ref/
		mipicsi_clk0: mipicsi-clk0 {
			rockchip,pins =
				/* mipicsi_clk0 */
				<4 RK_PB1 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		mipicsi_clk1: mipicsi-clk1 {
			rockchip,pins =
				/* mipicsi_clk1 */
				<4 RK_PB0 1 &pcfg_pull_none>;
		};
	};
	pdm {
		/omit-if-no-ref/
		pdmm0_clk0_pins: pdmm0-clk {
			rockchip,pins =
				/* pdm_clk0_m0 */
				<7 RK_PA4 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm0_clk1_pins: pdmm0-clk1 {
			rockchip,pins =
				/* pdmm0_clk1_pins */
				<7 RK_PA1 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm0_sdi0_pins: pdmm0-sdi0 {
			rockchip,pins =
				/* pdmm0_sdi0_pins */
				<7 RK_PA6 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm0_sdi1_pins: pdmm0-sdi1 {
			rockchip,pins =
				/* pdmm0_sdi1_pins */
				<7 RK_PB1 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm0_sdi2_pins: pdmm0-sdi2 {
			rockchip,pins =
				/* pdmm0_sdi2_pins */
				<7 RK_PB0 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm0_sdi3_pins: pdmm0-sdi3 {
			rockchip,pins =
				/* pdmm0_sdi3_pins */
				<7 RK_PA7 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm1_clk0_pins: pdmm1-clk {
			rockchip,pins =
				/* pdm_clk0_m1 */
				<6 RK_PB4 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm1_clk1_pins: pdmm1-clk1 {
			rockchip,pins =
				/* pdmm1_clk1_pins */
				<6 RK_PB7 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm1_sdi0_pins: pdmm1-sdi0 {
			rockchip,pins =
				/* pdmm1_sdi0_pins */
				<6 RK_PB5 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm1_sdi1_pins: pdmm1-sdi1 {
			rockchip,pins =
				/* pdmm1_sdi1_pins */
				<6 RK_PB6 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm1_sdi2_pins: pdmm1-sdi2 {
			rockchip,pins =
				/* pdmm1_sdi2_pins */
				<6 RK_PB2 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pdmm1_sdi3_pins: pdmm1-sdi3 {
			rockchip,pins =
				/* pdmm1_sdi3_pins */
				<6 RK_PB3 3 &pcfg_pull_none>;
		};
	};
	pmic {
		/omit-if-no-ref/
		pmic_pins: pmic-pins {
			rockchip,pins =
				/* pmic_int */
				<0 RK_PC0 1 &pcfg_pull_none>,
				/* pmic_sleep */
				<0 RK_PC1 1 &pcfg_pull_none>;
		};
	};
	pmu {
		/omit-if-no-ref/
		pmu_pins: pmu-pins {
			rockchip,pins =
				/* pmu_debug */
				<0 RK_PC7 1 &pcfg_pull_none>;
		};
	};
	prelight {
		/omit-if-no-ref/
		prelight_pins: prelight-pins {
			rockchip,pins =
				/* prelight_trig_out */
				<3 RK_PB4 4 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_0{
		/omit-if-no-ref/
		pwm0m0_pins: pwm0m0-pins {
			rockchip,pins =
				/* pwm0_pin_m0 */
				<0 RK_PC4 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm0m1_pins: pwm0m1-pins {
			rockchip,pins =
				/* pwm0_pin_m1 */
				<5 RK_PA7 5 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_1{
		/omit-if-no-ref/
		pwm1m0_pins: pwm1m0-pins {
			rockchip,pins =
				/* pwm1_pin_m0 */
				<0 RK_PC5 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm1m1_pins: pwm1m1-pins {
			rockchip,pins =
				/* pwm1_pin_m1 */
				<5 RK_PA6 5 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_2{
		/omit-if-no-ref/
		pwm2m0_pins: pwm2m0-pins {
			rockchip,pins =
				/* pwm2_pin_m0 */
				<0 RK_PC6 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm2m1_pins: pwm2m1-pins {
			rockchip,pins =
				/* pwm2_pin_m1 */
				<5 RK_PA5 5 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_3{
		/omit-if-no-ref/
		pwm3m0_pins: pwm3m0-pins {
			rockchip,pins =
				/* pwm3_pin_m0 */
				<0 RK_PC7 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm3m1_pins: pwm3m1-pins {
			rockchip,pins =
				/* pwm3_pin_m1 */
				<5 RK_PA4 5 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_4{
		/omit-if-no-ref/
		pwm4m0_pins: pwm4m0-pins {
			rockchip,pins =
				/* pwm4_pin_m0 */
				<0 RK_PD0 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm4m1_pins: pwm4m1-pins {
			rockchip,pins =
				/* pwm4_pin_m1 */
				<5 RK_PA3 5 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_5{
		/omit-if-no-ref/
		pwm5m0_pins: pwm5m0-pins {
			rockchip,pins =
				/* pwm5_pin_m0 */
				<0 RK_PD1 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm5m1_pins: pwm5m1-pins {
			rockchip,pins =
				/* pwm5_pin_m1 */
				<5 RK_PA2 5 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_6{
		/omit-if-no-ref/
		pwm6m0_pins: pwm6m0-pins {
			rockchip,pins =
				/* pwm6_pin_m0 */
				<0 RK_PC1 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm6m1_pins: pwm6m1-pins {
			rockchip,pins =
				/* pwm6_pin_m1 */
				<5 RK_PD0 5 &pcfg_pull_none>;
		};
	};
	pwm0_8ch_7{
		/omit-if-no-ref/
		pwm7m0_pins: pwm7m0-pins {
			rockchip,pins =
				/* pwm7_pin_m0 */
				<0 RK_PC0 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm7m1_pins: pwm7m1-pins {
			rockchip,pins =
				/* pwm7_pin_m1 */
				<5 RK_PD4 5 &pcfg_pull_none>;
		};
	};
	pwm1_4ch_0{
		/omit-if-no-ref/
		pwm8m0_pins: pwm8m0-pins {
			rockchip,pins =
				/* pwm8_pin_m0 */
				<6 RK_PA0 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm8m1_pins: pwm8m1-pins {
			rockchip,pins =
				/* pwm8_pin_m1 */
				<5 RK_PD3 5 &pcfg_pull_none>;
		};
	};
	pwm1_4ch_1{
		/omit-if-no-ref/
		pwm9m0_pins: pwm9m0-pins {
			rockchip,pins =
				/* pwm9_pin_m0 */
				<6 RK_PA1 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm9m1_pins: pwm9m1-pins {
			rockchip,pins =
				/* pwm9_pin_m1 */
				<5 RK_PD2 5 &pcfg_pull_none>;
		};
	};
	pwm1_4ch_2{
		/omit-if-no-ref/
		pwm10m0_pins: pwm10m0-pins {
			rockchip,pins =
				/* pwm10_pin_m0 */
				<6 RK_PA2 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm10m1_pins: pwm10m1-pins {
			rockchip,pins =
				/* pwm10_pin_m1 */
				<5 RK_PD1 5 &pcfg_pull_none>;
		};
	};
	pwm1_4ch_3{
		/omit-if-no-ref/
		pwm11m0_pins: pwm11m0-pins {
			rockchip,pins =
				/* pwm11_pin_m0 */
				<6 RK_PA3 6 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		pwm11m1_pins: pwm11m1-pins {
			rockchip,pins =
				/* pwm11_pin_m1 */
				<5 RK_PD5 5 &pcfg_pull_none>;
		};
	};
	rgmii {
		/omit-if-no-ref/
		rgmiim0_miim: rgmiim0-miim {
			rockchip,pins =
				/* rgmii_mdc_m0 */
				<6 RK_PC0 2 &pcfg_pull_none>,
				/* rgmii_mdio_m0 */
				<6 RK_PB7 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		rgmiim0_rxer: rgmiim0-rxer {
			rockchip,pins =
				/* rgmii_rxer_m0 */
				<6 RK_PB6 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		rgmiim0_bus2: rgmiim0-bus2 {
			rockchip,pins =
				/* rgmii_rxd0_m0 */
				<6 RK_PB2 2 &pcfg_pull_none>,
				/* rgmii_rxd1_m0 */
				<6 RK_PB3 2 &pcfg_pull_none>,
				/* rgmii_rxdv_m0 */
				<6 RK_PB5 2 &pcfg_pull_none>,
				/* rgmii_txd0_m0 */
				<6 RK_PA7 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd1_m0 */
				<6 RK_PB0 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txen_m0 */
				<6 RK_PB1 2 &pcfg_pull_none_drv_level_3>;
		};
		/omit-if-no-ref/
		rgmiim0_bus4: rgmiim0-bus4 {
			rockchip,pins =
				/* rgmii_rxclk_m0 */
				<6 RK_PC3 2 &pcfg_pull_none>,
				/* rgmii_rxd2_m0 */
				<6 RK_PA3 2 &pcfg_pull_none>,
				/* rgmii_rxd3_m0 */
				<6 RK_PA4 2 &pcfg_pull_none>,
				/* rgmii_txclk_m0 */
				<6 RK_PC2 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd2_m0 */
				<6 RK_PA5 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd3_m0 */
				<6 RK_PA6 2 &pcfg_pull_none_drv_level_3>;
		};
		/omit-if-no-ref/
		rgmiim0_mclkinout: rgmiim0-mclkinout {
			rockchip,pins =
				/* rgmii_clk_m0 */
				<6 RK_PB4 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		rgmiim1_miim: rgmiim1-miim {
			rockchip,pins =
				/* rgmii_mdc_m1 */
				<5 RK_PB6 2 &pcfg_pull_none>,
				/* rgmii_mdio_m1 */
				<5 RK_PB5 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		rgmiim1_rxer: rgmiim1-rxer {
			rockchip,pins =
				/* rgmii_rxer_m1 */
				<5 RK_PB4 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		rgmiim1_bus2: rgmiim1-bus2 {
			rockchip,pins =
				/* rgmii_rxd0_m1 */
				<5 RK_PB1 2 &pcfg_pull_none>,
				/* rgmii_rxd1_m1 */
				<5 RK_PB2 2 &pcfg_pull_none>,
				/* rgmii_rxdv_m1 */
				<5 RK_PB0 2 &pcfg_pull_none>,
				/* rgmii_txd0_m1 */
				<5 RK_PB7 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd1_m1 */
				<5 RK_PC0 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txen_m1 */
				<5 RK_PC2 2 &pcfg_pull_none_drv_level_3>;
		};
		/omit-if-no-ref/
		rgmiim1_bus4: rgmiim1-bus4 {
			rockchip,pins =
				/* rgmii_rxclk_m1 */
				<5 RK_PC7 2 &pcfg_pull_none>,
				/* rgmii_rxd2_m1 */
				<5 RK_PC3 2 &pcfg_pull_none>,
				/* rgmii_rxd3_m1 */
				<5 RK_PC4 2 &pcfg_pull_none>,
				/* rgmii_txclk_m1 */
				<5 RK_PC6 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd2_m1 */
				<5 RK_PC5 2 &pcfg_pull_none_drv_level_3>,
				/* rgmii_txd3_m1 */
				<5 RK_PA0 2 &pcfg_pull_none_drv_level_3>;
		};
		/omit-if-no-ref/
		rgmiim1_mclkinout: rgmiim1-mclkinout {
			rockchip,pins =
				/* rgmii_clk_m1 */
				<5 RK_PB3 2 &pcfg_pull_none>;
		};
	};
	sdio {
		/omit-if-no-ref/
		sdio_bus4: sdio-bus4 {
			rockchip,pins =
				/* sdio_d0 */
				<3 RK_PA2 1 &pcfg_pull_up_drv_level_2>,
				/* sdio_d1 */
				<3 RK_PA3 1 &pcfg_pull_up_drv_level_2>,
				/* sdio_d2 */
				<3 RK_PA4 1 &pcfg_pull_up_drv_level_2>,
				/* sdio_d3 */
				<3 RK_PA5 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdio_clk: sdio-clk {
			rockchip,pins =
				/* sdio_clk */
				<3 RK_PA0 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdio_cmd: sdio-cmd {
			rockchip,pins =
				/* sdio_cmd */
				<3 RK_PA1 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdio_det: sdio-det {
			rockchip,pins =
				/* sdio_det */
				<3 RK_PB6 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sdio_pwr: sdio-pwr {
			rockchip,pins =
				/* sdio_pwr */
				<3 RK_PB7 2 &pcfg_pull_none>;
		};
	};
	sdmmc0 {
		/omit-if-no-ref/
		sdmmc0_bus4: sdmmc0-bus4 {
			rockchip,pins =
				/* sdmmc0_d0 */
				<2 RK_PA0 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc0_d1 */
				<2 RK_PA1 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc0_d2 */
				<2 RK_PA2 1 &pcfg_pull_up_drv_level_2>,
				/* sdmmc0_d3 */
				<2 RK_PA3 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc0_clk: sdmmc0-clk {
			rockchip,pins =
				/* sdmmc0_clk */
				<2 RK_PA4 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc0_cmd: sdmmc0-cmd {
			rockchip,pins =
				/* sdmmc0_cmd */
				<2 RK_PA5 1 &pcfg_pull_up_drv_level_2>;
		};
		/omit-if-no-ref/
		sdmmc0_det: sdmmc0-det {
			rockchip,pins =
				/* sdmmc0_det */
				<0 RK_PA5 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		sdmmc0_pwr: sdmmc0-pwr {
			rockchip,pins =
				/* sdmmc0_pwr */
				<0 RK_PC6 1 &pcfg_pull_none>;
		};
	};
	spi0 {
		/omit-if-no-ref/
		spi0m0_pins: spi0m0-pins {
			rockchip,pins =
				/* spi0_clk_m0 */
				<0 RK_PB2 1 &pcfg_pull_up_drv_level_0>,
				/* spi0_miso_m0 */
				<0 RK_PB1 1 &pcfg_pull_up_drv_level_0>,
				/* spi0_mosi_m0 */
				<0 RK_PB0 1 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m0_cs0: spi0m0-cs0 {
			rockchip,pins =
				/* spi0_cs0n_m0 */
				<0 RK_PA7 1 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m0_cs1: spi0m0-cs1 {
			rockchip,pins =
				/* spi0_cs1n_m0 */
				<0 RK_PA6 1 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m1_pins: spi0m1-pins {
			rockchip,pins =
				/* spi0_clk_m1 */
				<4 RK_PA7 1 &pcfg_pull_up_drv_level_0>,
				/* spi0_miso_m1 */
				<4 RK_PA5 1 &pcfg_pull_up_drv_level_0>,
				/* spi0_mosi_m1 */
				<4 RK_PA4 1 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m1_cs0: spi0m1-cs0 {
			rockchip,pins =
				/* spi0_cs0n_m1 */
				<4 RK_PA6 1 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m1_cs1: spi0m1-cs1 {
			rockchip,pins =
				/* spi0_cs1n_m1 */
				<4 RK_PA3 1 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m2_pins: spi0m2-pins {
			rockchip,pins =
				/* spi0_clk_m2 */
				<5 RK_PA6 6 &pcfg_pull_up_drv_level_0>,
				/* spi0_miso_m2 */
				<5 RK_PA5 6 &pcfg_pull_up_drv_level_0>,
				/* spi0_mosi_m2 */
				<5 RK_PA4 6 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m2_cs0: spi0m2-cs0 {
			rockchip,pins =
				/* spi0_cs0n_m2 */
				<5 RK_PA3 6 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi0m2_csn1_pins: spi0m2-cs1 {
			rockchip,pins =
				/* spi0_cs1n_m2 */
				<5 RK_PA7 6 &pcfg_pull_up_drv_level_0>;
		};
	};
	spi1 {
		/omit-if-no-ref/
		spi1m0_pins: spi1m0-pins {
			rockchip,pins =
				/* spi1_clk_m0 */
				<6 RK_PB4 5 &pcfg_pull_up_drv_level_0>,
				/* spi1_miso_m0 */
				<6 RK_PB3 5 &pcfg_pull_up_drv_level_0>,
				/* spi1_mosi_m0 */
				<6 RK_PB2 5 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m0_cs0: spi1m0-cs0 {
			rockchip,pins =
				/* spi1_cs0n_m0 */
				<6 RK_PB1 5 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m0_cs1: spi1m0-cs1 {
			rockchip,pins =
				/* spi1_cs1n_m0 */
				<6 RK_PB0 5 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m1_clk_pins: spi1m1-pins {
			rockchip,pins =
				/* spi1_clk_m1 */
				<3 RK_PB4 3 &pcfg_pull_up_drv_level_0>,
				/* spi1_miso_m1 */
				<3 RK_PB3 3 &pcfg_pull_up_drv_level_0>,
				/* spi1_mosi_m1 */
				<3 RK_PB2 3 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m1_csn0_pins: spi1m1-cs0 {
			rockchip,pins =
				/* spi1_cs0n_m1 */
				<3 RK_PB5 3 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m1_csn1_pins: spi1m1-cs1 {
			rockchip,pins =
				/* spi1_cs1n_m1 */
				<3 RK_PB6 3 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m2_clk_pins: spi1m2-pins {
			rockchip,pins =
				/* spi1_clk_m2 */
				<5 RK_PD1 6 &pcfg_pull_up_drv_level_0>,
				/* spi1_miso_m2 */
				<5 RK_PD3 6 &pcfg_pull_up_drv_level_0>,
				/* spi1_mosi_m2 */
				<5 RK_PD2 6 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m2_csn0_pins: spi1m2-cs0 {
			rockchip,pins =
				/* spi1_cs0n_m2 */
				<5 RK_PD0 6 &pcfg_pull_up_drv_level_0>;
		};
		/omit-if-no-ref/
		spi1m2_csn1_pins: spi1m2-cs1 {
			rockchip,pins =
				/* spi1_cs1n_m2 */
				<5 RK_PD4 6 &pcfg_pull_up_drv_level_0>;
		};
	};
	tsadc {
		/omit-if-no-ref/
		tsadcm0_shut: tsadcm0-shut {
			rockchip,pins =
				/* tsadcm0_shut */
				<0 RK_PA1 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		tsadcm1_shut: tsadcm1-shut {
			rockchip,pins =
				/* tsadcm1_shut */
				<0 RK_PC1 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		tsadc_shutorg: tsadc-shutorg {
			rockchip,pins =
				/* tsadc_shutorg */
				<0 RK_PA1 2 &pcfg_pull_none>;
		};
	};
	uart2{
		/omit-if-no-ref/
		uart2m0_xfer_pins: uart0-xfer {
			rockchip,pins =
				/* uart0_rx */
				<3 RK_PB0 1 &pcfg_pull_up>,
				/* uart0_tx */
				<3 RK_PB1 1 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart2m0_ctsn_pins: uart0-ctsn {
			rockchip,pins =
				/* uart2m0_ctsn_pins */
				<3 RK_PA7 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart2m0_rtsn_pins: uart0-rtsn {
			rockchip,pins =
				/* uart2m0_rtsn_pins */
				<3 RK_PA6 1 &pcfg_pull_none>;
		};
	};
	uart1 {
		/omit-if-no-ref/
		uart1m0_xfer: uart1m0-xfer {
			rockchip,pins =
				/* uart1_rx_m0 */
				<0 RK_PC5 2 &pcfg_pull_up>,
				/* uart1_tx_m0 */
				<0 RK_PC4 2 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart1m0_ctsn: uart1m0-ctsn {
			rockchip,pins =
				/* uart1m0_ctsn_pins */
				<0 RK_PC7 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart1m0_rtsn: uart1m0-rtsn {
			rockchip,pins =
				/* uart1m0_rtsn_pins */
				<0 RK_PC6 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart1m1_xfer: uart1m1-xfer {
			rockchip,pins =
				/* uart1_rx_m1 */
				<3 RK_PB7 5 &pcfg_pull_up>,
				/* uart1_tx_m1 */
				<3 RK_PB6 5 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart1m1_ctsn: uart1m1-ctsn {
			rockchip,pins =
				/* uart1m1_ctsn_pins */
				<3 RK_PB5 5 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart1m1_rtsn: uart1m1-rtsn {
			rockchip,pins =
				/* uart1m1_rtsn_pins */
				<3 RK_PB4 5 &pcfg_pull_none>;
		};
	};
	uart0{
		/omit-if-no-ref/
		uart0_xfer: uart2m0-xfer {
			rockchip,pins =
				/* uart2_rx_m0 */
				<2 RK_PA0 3 &pcfg_pull_up>,
				/* uart2_tx_m0 */
				<2 RK_PA1 3 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart0_ctsn: uart0-ctsn {
			rockchip,pins =
				/* uart0_ctsn */
				<1 RK_PC1 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart0_rtsn: uart0-rtsn {
			rockchip,pins =
				/* uart0_rtsn */
				<1 RK_PC0 1 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart0m1_xfer: uart2m1-xfer {
			rockchip,pins =
				/* uart2_rx_m1 */
				<5 RK_PD7 1 &pcfg_pull_up>,
				/* uart2_tx_m1 */
				<5 RK_PD6 1 &pcfg_pull_up>;
		};
	};
	uart3 {
		/omit-if-no-ref/
		uart3m2_xfer: uart3m0-xfer {
			rockchip,pins =
				/* uart3_rx_m0 */
				<6 RK_PC3 4 &pcfg_pull_up>,
				/* uart3_tx_m0 */
				<6 RK_PC2 4 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart3m2_ctsn: uart3m0-ctsn {
			rockchip,pins =
				/* uart3m2_ctsn_pins */
				<6 RK_PC1 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart3m2_rtsn: uart3m0-rtsn {
			rockchip,pins =
				/* uart3m2_rtsn_pins */
				<6 RK_PC0 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart3m0_xfer: uart3m1-xfer {
			rockchip,pins =
				/* uart3_rx_m1 */
				<2 RK_PA2 2 &pcfg_pull_up>,
				/* uart3_tx_m1 */
				<2 RK_PA3 2 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart3m0_ctsn: uart3m1-ctsn {
			rockchip,pins =
				/* uart3m0_ctsn_pins */
				<2 RK_PA5 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart3m0_rtsn: uart3m1-rtsn {
			rockchip,pins =
				/* uart3m0_rtsn_pins */
				<2 RK_PA4 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart3m1_xfer: uart3m2-xfer {
			rockchip,pins =
				/* uart3_rx_m2 */
				<5 RK_PD5 4 &pcfg_pull_up>,
				/* uart3_tx_m2 */
				<5 RK_PD4 4 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart3m1_ctsn: uart3m2-ctsn {
			rockchip,pins =
				/* uart3m1_ctsn_pins */
				<5 RK_PD3 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart3m1_rtsn: uart3m2-rtsn {
			rockchip,pins =
				/* uart3m1_rtsn_pins */
				<5 RK_PD2 4 &pcfg_pull_none>;
		};
	};
	uart4 {
		/omit-if-no-ref/
		uart4m2_xfer: uart4m0-xfer {
			rockchip,pins =
				/* uart4_rx_m0 */
				<6 RK_PA1 4 &pcfg_pull_up>,
				/* uart4_tx_m0 */
				<6 RK_PA0 4 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart4m2_ctsn: uart4m0-ctsn {
			rockchip,pins =
				/* uart4m2_ctsn_pins */
				<6 RK_PA7 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart4m2_rtsn: uart4m0-rtsn {
			rockchip,pins =
				/* uart4m2_rtsn_pins */
				<6 RK_PA6 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart4m1_xfer: uart4m1-xfer {
			rockchip,pins =
				/* uart4_rx_m1 */
				<5 RK_PA3 4 &pcfg_pull_up>,
				/* uart4_tx_m1 */
				<5 RK_PA2 4 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart4m1_ctsn: uart4m1-ctsn {
			rockchip,pins =
				/* uart4m1_ctsn_pins */
				<5 RK_PA1 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart4m1_rtsn: uart4m1-rtsn {
			rockchip,pins =
				/* uart4m1_rtsn_pins */
				<5 RK_PA0 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart4m0_xfer: uart4m2-xfer {
			rockchip,pins =
				/* uart4_rx_m2 */
				<4 RK_PA2 3 &pcfg_pull_up>,
				/* uart4_tx_m2 */
				<4 RK_PA3 3 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart4m0_ctsn: uart4m2-ctsn {
			rockchip,pins =
				/* uart4m0_ctsn_pins */
				<4 RK_PA1 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart4m0_rtsn: uart4m2-rtsn {
			rockchip,pins =
				/* uart4m0_rtsn_pins */
				<4 RK_PA0 3 &pcfg_pull_none>;
		};
	};
	uart5 {
		/omit-if-no-ref/
		uart5m2_xfer: uart5m0-xfer {
			rockchip,pins =
				/* uart5_rx_m0 */
				<6 RK_PA3 4 &pcfg_pull_up>,
				/* uart5_tx_m0 */
				<6 RK_PA2 4 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart5m2_ctsn: uart5m0-ctsn {
			rockchip,pins =
				/* uart5m2_ctsn_pins */
				<6 RK_PA5 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart5m2_rtsn: uart5m0-rtsn {
			rockchip,pins =
				/* uart5m2_rtsn_pins */
				<6 RK_PA4 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart5m1_xfer: uart5m1-xfer {
			rockchip,pins =
				/* uart5_rx_m1 */
				<5 RK_PA5 4 &pcfg_pull_up>,
				/* uart5_tx_m1 */
				<5 RK_PA4 4 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart5m1_ctsn: uart5m1-ctsn {
			rockchip,pins =
				/* uart5m1_ctsn_pins */
				<5 RK_PA7 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart5m1_rtsn: uart5m1-rtsn {
			rockchip,pins =
				/* uart5m1_rtsn_pins */
				<5 RK_PA6 4 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart5m0_xfer: uart5m2-xfer {
			rockchip,pins =
				/* uart5_rx_m2 */
				<4 RK_PA7 3 &pcfg_pull_up>,
				/* uart5_tx_m2 */
				<4 RK_PA6 3 &pcfg_pull_up>;
		};
		/omit-if-no-ref/
		uart5m0_ctsn: uart5m2-ctsn {
			rockchip,pins =
				/* uart5m0_ctsn_pins */
				<4 RK_PB1 3 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		uart5m0_rtsn: uart5m2-rtsn {
			rockchip,pins =
				/* uart5m0_rtsn_pins */
				<4 RK_PB0 3 &pcfg_pull_none>;
		};
	};
};
&pinctrl {
	gpio {
		/omit-if-no-ref/
		uart2m0_rtsn_pins_gpio: uart0-rts-pin {
			rockchip,pins =
				<3 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
	// pwm-pull-down {
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch0_pins: pwm0m0-pins {
	// 		rockchip,pins =
	// 			/* pwm0_pin_m0 */
	// 			<0 RK_PC4 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m1_ch0_pins: pwm0m1-pins {
	// 		rockchip,pins =
	// 			/* pwm0_pin_m1 */
	// 			<5 RK_PA7 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch1_pins: pwm1m0-pins {
	// 		rockchip,pins =
	// 			/* pwm1_pin_m0 */
	// 			<0 RK_PC5 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m1_ch1_pins: pwm1m1-pins {
	// 		rockchip,pins =
	// 			/* pwm1_pin_m1 */
	// 			<5 RK_PA6 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch2_pins: pwm2m0-pins {
	// 		rockchip,pins =
	// 			/* pwm2_pin_m0 */
	// 			<0 RK_PC6 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m1_ch2_pins: pwm2m1-pins {
	// 		rockchip,pins =
	// 			/* pwm2_pin_m1 */
	// 			<5 RK_PA5 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch3_pins: pwm3m0-pins {
	// 		rockchip,pins =
	// 			/* pwm3_pin_m0 */
	// 			<0 RK_PC7 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m1_ch3_pins: pwm3m1-pins {
	// 		rockchip,pins =
	// 			/* pwm3_pin_m1 */
	// 			<5 RK_PA4 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch4_pins: pwm4m0-pins {
	// 		rockchip,pins =
	// 			/* pwm4_pin_m0 */
	// 			<0 RK_PD0 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m2_ch4_pins: pwm4m1-pins {
	// 		rockchip,pins =
	// 			/* pwm4_pin_m1 */
	// 			<5 RK_PA3 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch5_pins: pwm5m0-pins {
	// 		rockchip,pins =
	// 			/* pwm5_pin_m0 */
	// 			<0 RK_PD1 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m2_ch5_pins: pwm5m1-pins {
	// 		rockchip,pins =
	// 			/* pwm5_pin_m1 */
	// 			<5 RK_PA2 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch6_pins: pwm6m0-pins {
	// 		rockchip,pins =
	// 			/* pwm6_pin_m0 */
	// 			<0 RK_PC1 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m2_ch6_pins_pull_up: pwm6m1-pins-pull-up {
	// 		rockchip,pins =
	// 			/* pwm6_pin_m1 */
	// 			<5 RK_PD0 5 &pcfg_pull_up>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m0_ch7_pins: pwm7m0-pins {
	// 		rockchip,pins =
	// 			/* pwm7_pin_m0 */
	// 			<0 RK_PC0 3 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm0m2_ch7_pins_pull_up: pwm7m1-pins-pull-up {
	// 		rockchip,pins =
	// 			/* pwm7_pin_m1 */
	// 			<5 RK_PD4 5 &pcfg_pull_up>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m2_ch0_pins: pwm8m0-pins {
	// 		rockchip,pins =
	// 			/* pwm8_pin_m0 */
	// 			<6 RK_PA0 6 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m1_ch0_pins: pwm8m1-pins {
	// 		rockchip,pins =
	// 			/* pwm8_pin_m1 */
	// 			<5 RK_PD3 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m2_ch1_pins: pwm9m0-pins {
	// 		rockchip,pins =
	// 			/* pwm9_pin_m0 */
	// 			<6 RK_PA1 6 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m1_ch1_pins: pwm9m1-pins {
	// 		rockchip,pins =
	// 			/* pwm9_pin_m1 */
	// 			<5 RK_PD2 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m2_ch2_pins: pwm10m0-pins {
	// 		rockchip,pins =
	// 			/* pwm10_pin_m0 */
	// 			<6 RK_PA2 6 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m1_ch2_pins: pwm10m1-pins {
	// 		rockchip,pins =
	// 			/* pwm10_pin_m1 */
	// 			<5 RK_PD1 5 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m2_ch3_pins: pwm11m0-pins {
	// 		rockchip,pins =
	// 			/* pwm11_pin_m0 */
	// 			<6 RK_PA3 6 &pcfg_pull_down>;
	// 	};
	// 	/omit-if-no-ref/
	// 	pwm1m1_ch3_pins: pwm11m1-pins {
	// 		rockchip,pins =
	// 			/* pwm11_pin_m1 */
	// 			<5 RK_PD5 5 &pcfg_pull_down>;
	// 	};
	// };
	spi0-hs {
		/omit-if-no-ref/
		spi0m0_pins_hs: spi0m0-pins {
			rockchip,pins =
				/* spi0_clk_m0 */
				<0 RK_PB2 1 &pcfg_pull_up_drv_level_1>,
				/* spi0_miso_m0 */
				<0 RK_PB1 1 &pcfg_pull_up_drv_level_1>,
				/* spi0_mosi_m0 */
				<0 RK_PB0 1 &pcfg_pull_up_drv_level_1>;
		};
		/omit-if-no-ref/
		spi0m1_clk_pins_hs: spi0m1-pins {
			rockchip,pins =
				/* spi0_clk_m1 */
				<4 RK_PA7 1 &pcfg_pull_up_drv_level_1>,
				/* spi0_miso_m1 */
				<4 RK_PA5 1 &pcfg_pull_up_drv_level_1>,
				/* spi0_mosi_m1 */
				<4 RK_PA4 1 &pcfg_pull_up_drv_level_1>;
		};
		/omit-if-no-ref/
		spi0m2_clk_pins_hs: spi0m2-pins {
			rockchip,pins =
				/* spi0_clk_m2 */
				<5 RK_PA6 6 &pcfg_pull_up_drv_level_1>,
				/* spi0_miso_m2 */
				<5 RK_PA5 6 &pcfg_pull_up_drv_level_1>,
				/* spi0_mosi_m2 */
				<5 RK_PA4 6 &pcfg_pull_up_drv_level_1>;
		};
	};
	spi1-hs {
		/omit-if-no-ref/
		spi1m0_pins_hs: spi1m0-pins {
			rockchip,pins =
				/* spi1_clk_m0 */
				<6 RK_PB4 5 &pcfg_pull_up_drv_level_1>,
				/* spi1_miso_m0 */
				<6 RK_PB3 5 &pcfg_pull_up_drv_level_1>,
				/* spi1_mosi_m0 */
				<6 RK_PB2 5 &pcfg_pull_up_drv_level_1>;
		};
		/omit-if-no-ref/
		spi1m1_clk_pins_hs: spi1m1-pins {
			rockchip,pins =
				/* spi1_clk_m1 */
				<3 RK_PB4 3 &pcfg_pull_up_drv_level_1>,
				/* spi1_miso_m1 */
				<3 RK_PB3 3 &pcfg_pull_up_drv_level_1>,
				/* spi1_mosi_m1 */
				<3 RK_PB2 3 &pcfg_pull_up_drv_level_1>;
		};
		/omit-if-no-ref/
		spi1m2_clk_pins_hs: spi1m2-pins {
			rockchip,pins =
				/* spi1_clk_m2 */
				<5 RK_PD1 6 &pcfg_pull_up_drv_level_1>,
				/* spi1_miso_m2 */
				<5 RK_PD3 6 &pcfg_pull_up_drv_level_1>,
				/* spi1_mosi_m2 */
				<5 RK_PD2 6 &pcfg_pull_up_drv_level_1>;
		};
	};
	gmac_clk {
		/omit-if-no-ref/
		rgmiim0_mclkinout_level0: rgmiim0_mclkinout-level0 {
			rockchip,pins =
				/* rgmiim0_clk */
				<6 RK_PB4 2 &pcfg_pull_none_drv_level_0>;
		};
		/omit-if-no-ref/
		rgmiim0_mclkinout_level3: rgmiim0_mclkinout-level3 {
			rockchip,pins =
				/* rgmiim0_clk */
				<6 RK_PB4 2 &pcfg_pull_none_drv_level_3>;
		};
		/omit-if-no-ref/
		rgmiim1_mclkinout_level0: rgmiim1_mclkinout-level0 {
			rockchip,pins =
				/* rgmiim1_clk */
				<5 RK_PB3 2 &pcfg_pull_none_drv_level_0>;
		};
		/omit-if-no-ref/
		rgmiim1_mclkinout_level3: rgmiim1_mclkinout-level3 {
			rockchip,pins =
				/* rgmiim1_clk */
				<5 RK_PB3 2 &pcfg_pull_none_drv_level_3>;
		};
	};
	rmii {
		/omit-if-no-ref/
		rmiim0_miim: rmiim0-miim {
			rockchip,pins =
				/* rgmii_mdc_m0 */
				<6 RK_PC0 2 &pcfg_pull_none_drv_level_0>,
				/* rgmii_mdio_m0 */
				<6 RK_PB7 2 &pcfg_pull_none>;
		};

		/omit-if-no-ref/
		rmiim0_bus2: rmiim0-bus2 {
			rockchip,pins =
				/* rgmii_rxd0_m0 */
				<6 RK_PB2 2 &pcfg_pull_none>,
				/* rgmii_rxd1_m0 */
				<6 RK_PB3 2 &pcfg_pull_none>,
				/* rgmii_rxdv_m0 */
				<6 RK_PB5 2 &pcfg_pull_none>,
				/* rgmii_txd0_m0 */
				<6 RK_PA7 2 &pcfg_pull_none_drv_level_0>,
				/* rgmii_txd1_m0 */
				<6 RK_PB0 2 &pcfg_pull_none_drv_level_0>,
				/* rgmii_txen_m0 */
				<6 RK_PB1 2 &pcfg_pull_none_drv_level_0>;
		};
		/omit-if-no-ref/
		rmiim1_miim: rmiim1-miim {
			rockchip,pins =
				/* rgmii_mdc_m1 */
				<5 RK_PB6 2 &pcfg_pull_none_drv_level_0>,
				/* rgmii_mdio_m1 */
				<5 RK_PB5 2 &pcfg_pull_none>;
		};
		/omit-if-no-ref/
		rmiim1_bus2: rmiim1-bus2 {
			rockchip,pins =
				/* rgmii_rxd0_m1 */
				<5 RK_PB1 2 &pcfg_pull_none>,
				/* rgmii_rxd1_m1 */
				<5 RK_PB2 2 &pcfg_pull_none>,
				/* rgmii_rxdv_m1 */
				<5 RK_PB0 2 &pcfg_pull_none>,
				/* rgmii_txd0_m1 */
				<5 RK_PB7 2 &pcfg_pull_none_drv_level_0>,
				/* rgmii_txd1_m1 */
				<5 RK_PC0 2 &pcfg_pull_none_drv_level_0>,
				/* rgmii_txen_m1 */
				<5 RK_PC2 2 &pcfg_pull_none_drv_level_0>;
		};
	};
};