// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2024 Rockchip Electronics Co., Ltd.
 *
 */

/dts-v1/;

#include "rk3576.dtsi"
#include "rk3576-test1.dtsi"
#include "rk3576-linux.dtsi"

/ {
	model = "Rockchip RK3576 TEST1 V10 Board";
	compatible = "rockchip,rk3576-test1-v10", "rockchip,rk3576";
};

&dp {
	status = "disabled";
};

&dsi_in_vopl {
	status = "okay";
};

&dsi_in_vp1 {
	status = "disabled";
};

&hdmi {
	status = "disabled";
};

&vop {
	status = "disabled";
};

&vopl {
	status = "okay";
};

&route_dsi {
	status = "okay";
	connect = <&vopl_out_dsi>;
};
