// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

/dts-v1/;

#include "rk3588-vehicle-evb-v20.dtsi"
#include "rk3588-vehicle-evb-mipi-nvp6188.dtsi"
#include "rk3588-vehicle-evb-image-reverse.dtsi"
#include "rk3588-vehicle-serdes-display-v20.dtsi"
#include "rk3588-android.dtsi"

/ {
	model = "Rockchip RK3588 VEHICLE EVB V20 Board";
	compatible = "rockchip,rk3588-vehicle-evb-v20", "rockchip,rk3588";

	bt-sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "dsp_a";
		simple-audio-card,bitclock-inversion;
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,name = "rockchip,bt";
		simple-audio-card,cpu {
			sound-dai = <&i2s2_2ch>;
		};

		simple-audio-card,codec {
			sound-dai = <&bt_sco 1>;
		};
	};

	bt_sco: bt-sco {
		compatible = "delta,dfbmcs320";
		#sound-dai-cells = <1>;
		status = "okay";
	};

	nvp6188_osc: oscillator {
		compatible = "fixed-clock";
		#clock-cells = <1>;
		clock-frequency  = <27000000>;
		clock-output-names = "nvp6188-osc";
	};
};

&cif_sensor {
	nvp6188 {
		powerdown-gpios = <&gpio1 RK_PB2 GPIO_ACTIVE_HIGH>;
	};
};

&i2c7 {
	status = "okay";
	/delete-node/ nvp6188@33;
	nvp6188: nvp6188@31 {
		compatible = "nvp6188";
		status = "okay";
		reg = <0x31>;
		clocks = <&nvp6188_osc 0>;
		clock-names = "xvclk";
		power-domains = <&power RK3588_PD_VI>;
		rockchip,grf = <&sys_grf>;
		/*power-gpios = <&gpio3 RK_PC6 GPIO_ACTIVE_HIGH>;*/
		reset-gpios = <&gpio1 RK_PB2 GPIO_ACTIVE_HIGH>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "nvp6188";
		rockchip,camera-module-lens-name = "nvp6188";

		port {
			nvp6188_out: endpoint {
				remote-endpoint = <&mipi_dphy0_in_nvp6188>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&i2s2_2ch {
	pinctrl-0 = <&i2s2m1_lrck
		     &i2s2m1_sclk
		     &i2s2m1_sdi
		     &i2s2m1_sdo>;
	status = "okay";
};

&pinctrl {

	bl {
		bl0_enable_pin: bl0-enable-pin {
			rockchip,pins =
				<1 RK_PA7 RK_FUNC_GPIO &pcfg_pull_none>,
				<4 RK_PD2 RK_FUNC_GPIO &pcfg_pull_none>,
				<4 RK_PD3 RK_FUNC_GPIO &pcfg_pull_none>;

		};

		bl1_enable_pin: bl1-enable-pin {
			rockchip,pins = <1 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl2_enable_pin: bl2-enable-pin {
			rockchip,pins = <3 RK_PC4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl3_enable_pin: bl3-enable-pin {
			rockchip,pins = <3 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl4_enable_pin: bl4-enable-pin {
			rockchip,pins = <0 RK_PD5 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl5_enable_pin: bl5-enable-pin {
			rockchip,pins = <1 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	serdes {
		//dsi0
		ser0_rst_pin: ser0-rst-pin {
			rockchip,pins = <1 RK_PA4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		//dsi1
		ser1_rst_pin: ser1-rst-pin {
			rockchip,pins = <1 RK_PA5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	touch {
		//dsi0-i2c2
		touch_gpio_dsi0: touch-gpio-dsi0 {
			rockchip,pins =
				<1 RK_PB0 RK_FUNC_GPIO &pcfg_pull_up>;  //rst
		};
		//dsi1-i2c6
		touch_gpio_dsi1: touch-gpio-dsi1 {
			rockchip,pins =
				<1 RK_PB7 RK_FUNC_GPIO &pcfg_pull_up>,  //rst
				<1 RK_PB1 RK_FUNC_GPIO &pcfg_pull_up>;  //int
		};
		//dp0-i2c4
		touch_gpio_dp0: touch-gpio-dp0 {
			rockchip,pins =
				<3 RK_PC5 RK_FUNC_GPIO &pcfg_pull_up>,	//rst
				<0 RK_PC0 RK_FUNC_GPIO &pcfg_pull_up>;	//int
		};
		//edp0-i2c5
		touch_gpio_edp0: touch-gpio-edp0 {
			rockchip,pins =
				<0 RK_PD4 RK_FUNC_GPIO &pcfg_pull_up>,  //rst
				<0 RK_PD1 RK_FUNC_GPIO &pcfg_pull_up>;  //int
		};
	};
};

&rockchip_suspend {
	rockchip,sleep-mode-config = <
		(0
		| RKPM_SLP_ARMOFF_DDRPD
		| RKPM_SLP_PMU_PMUALIVE_32K
		| RKPM_SLP_PMU_DIS_OSC
		| RKPM_SLP_32K_EXT
		)
	>;
	rockchip,wakeup-config = <
		(0
		| RKPM_GPIO_WKUP_EN
		)
	>;
	status = "okay";
};

&vdd_log_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <800000>;
	};
};

&vcc_3v3_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <3300000>;
	};
};

&vcc_1v8_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <1800000>;
	};
};
