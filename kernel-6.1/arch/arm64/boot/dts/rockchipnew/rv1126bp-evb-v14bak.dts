// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 */

/dts-v1/;
#include "rv1126b.dtsi"
#include "rv1126bp-evb.dtsi"
#include "rv1126bp-evb-v14.dtsi"
#include "rv1126bp-evb-cam-csi0.dtsi"
/ {
	model = "Rockchip RV1126B-P EVB V14 Board";
	compatible = "rockchip,rv1126bp-evb-v14", "rockchip,rv1126b";
};

&fspi0 {
	status = "okay";

	flash@0 {
		compatible = "spi-nand";
		reg = <0>;
		spi-max-frequency = <75000000>;
		spi-rx-bus-width = <4>;
		spi-tx-bus-width = <1>;
	};
};
