// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2019 Fuzhou Rockchip Electronics Co., Ltd.
 */

#include <dt-bindings/display/drm_mipi_dsi.h>
#include <dt-bindings/input/input.h>

/ {
	leds {
		compatible = "gpio-leds";

		system_led {
			gpios = <&gpio3 RK_PC2 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};

    camera_power{
        compatible = "vispect,mrv220_camera_power";
        status = "okay";
        power-gpios = <&gpio3 RK_PB0 GPIO_ACTIVE_HIGH>;
	};

	adc-keys {
		compatible = "adc-keys";
		io-channels = <&saradc 0>;
		io-channel-names = "buttons";
		poll-interval = <100>;
		keyup-threshold-microvolt = <1800000>;

		esc-key {
			label = "esc";
			linux,code = <KEY_ESC>;
			press-threshold-microvolt = <0>;
		};

		right-key {
			label = "right";
			linux,code = <KEY_RIGHT>;
			press-threshold-microvolt = <400781>;
		};

		left-key {
			label = "left";
			linux,code = <KEY_LEFT>;
			press-threshold-microvolt = <801562>;
		};

		menu-key {
			label = "menu";
			linux,code = <KEY_MENU>;
			press-threshold-microvolt = <1198828>;
		};
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm3 0 25000 0>;
		brightness-levels = <
			  0   1   2   3   4   5   6   7
			  8   9  10  11  12  13  14  15
			 16  17  18  19  20  21  22  23
			 24  25  26  27  28  29  30  31
			 32  33  34  35  36  37  38  39
			 40  41  42  43  44  45  46  47
			 48  49  50  51  52  53  54  55
			 56  57  58  59  60  61  62  63
			 64  65  66  67  68  69  70  71
			 72  73  74  75  76  77  78  79
			 80  81  82  83  84  85  86  87
			 88  89  90  91  92  93  94  95
			 96  97  98  99 100 101 102 103
			104 105 106 107 108 109 110 111
			112 113 114 115 116 117 118 119
			120 121 122 123 124 125 126 127
			128 129 130 131 132 133 134 135
			136 137 138 139 140 141 142 143
			144 145 146 147 148 149 150 151
			152 153 154 155 156 157 158 159
			160 161 162 163 164 165 166 167
			168 169 170 171 172 173 174 175
			176 177 178 179 180 181 182 183
			184 185 186 187 188 189 190 191
			192 193 194 195 196 197 198 199
			200 201 202 203 204 205 206 207
			208 209 210 211 212 213 214 215
			216 217 218 219 220 221 222 223
			224 225 226 227 228 229 230 231
			232 233 234 235 236 237 238 239
			240 241 242 243 244 245 246 247
			248 249 250 251 252 253 254 255>;
		default-brightness-level = <200>;
	};

	cam_ircut0: cam_ircut {
		status = "okay";
		compatible = "rockchip,ircut";
		ircut-open-gpios = <&gpio2 RK_PA7 GPIO_ACTIVE_HIGH>;
		ircut-close-gpios  = <&gpio2 RK_PA6 GPIO_ACTIVE_HIGH>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
	};

	dummy_codec: dummy-codec {
		compatible = "rockchip,dummy-codec";
		#sound-dai-cells = <0>;
	};

	// pdm_mic_array: pdm-mic_array {
	// 	status = "disabled";
	// 	compatible = "simple-audio-card";
	// 	simple-audio-card,name = "rockchip,pdm-mic-array";
	// 	simple-audio-card,cpu {
	// 		sound-dai = <&pdm>;
	// 	};
	// 	simple-audio-card,codec {
	// 		sound-dai = <&dummy_codec>;
	// 	};
	// };

	rk809_sound: rk809-sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,name = "rockchip,rk809-codec";
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,widgets =
			"Microphone", "Mic Jack",
			"Headphone", "Headphone Jack";
		simple-audio-card,routing =
			"Mic Jack", "MICBIAS1",
			"IN1P", "Mic Jack",
			"Headphone Jack", "HPOL",
			"Headphone Jack", "HPOR";
		simple-audio-card,cpu {
			sound-dai = <&i2s0_8ch>;
		};
		simple-audio-card,codec {
			sound-dai = <&rk809_codec>;
		};
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;

		/*
		 * On the module itself this is one of these (depending
		 * on the actual card populated):
		 * - SDIO_RESET_L_WL_REG_ON
		 * - PDN (power down when low)
		 */
		reset-gpios = <&gpio0 RK_PA6 GPIO_ACTIVE_LOW>;
	};

	vcc18_lcd_n: vcc18-lcd-n {
		compatible = "regulator-fixed";
		regulator-name = "vcc18_lcd_n";
		gpio = <&gpio3 RK_PD5 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
	};

	vcc5v0_sys: vccsys {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	vdd_npu: vdd-npu {
		compatible = "pwm-regulator";
		pwms = <&pwm0 0 5000 1>;
		regulator-name = "vdd_npu";
		regulator-min-microvolt = <650000>;
		regulator-max-microvolt = <950000>;
		regulator-init-microvolt = <800000>;
		regulator-always-on;
		regulator-boot-on;
		regulator-settling-time-up-us = <250>;
		pwm-supply = <&vcc5v0_sys>;
		status = "okay";
	};

	vdd_vepu: vdd-vepu {
		compatible = "pwm-regulator";
		pwms = <&pwm1 0 5000 1>;
		regulator-name = "vdd_vepu";
		regulator-min-microvolt = <650000>;
		regulator-max-microvolt = <950000>;
		regulator-init-microvolt = <800000>;
		regulator-always-on;
		regulator-boot-on;
		regulator-settling-time-up-us = <250>;
		pwm-supply = <&vcc5v0_sys>;
		status = "okay";
	};

	wireless-bluetooth {
		compatible = "bluetooth-platdata";
		uart_rts_gpios = <&gpio1 RK_PC0 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart0_rtsn>;
		pinctrl-1 = <&uart0_rtsn_gpio>;
		BT,power_gpio    = <&gpio0 RK_PA7 GPIO_ACTIVE_HIGH>;
		BT,wake_host_irq = <&gpio0 RK_PA5 GPIO_ACTIVE_HIGH>;
		status = "disabled";
	};

	wireless_wlan: wireless-wlan {
		compatible = "wlan-platdata";
		rockchip,grf = <&grf>;
		clocks = <&rk809 1>;
		clock-names = "clk_wifi";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_wake_host>;
		wifi_chip_type = "ap6255";
		/* WIFI,poweren_gpio = <&gpio0 RK_PA6 GPIO_ACTIVE_HIGH>; */
		WIFI,host_wake_irq = <&gpio0 RK_PB0 GPIO_ACTIVE_HIGH>;
		status = "disabled";
	};
};

&cpu0 {
	cpu-supply = <&vdd_arm>;
};

&tsadc {
	status = "okay";
};

&display_subsystem {
	status = "okay";
};

&dsi {
	status = "okay";

	rockchip,lane-rate = <480>;
	panel@0 {
		compatible = "ilitek,ili9881d", "simple-panel-dsi";
		reg = <0>;
		backlight = <&backlight>;
		power-supply = <&vcc18_lcd_n>;
		prepare-delay-ms = <5>;
		reset-delay-ms = <1>;
		init-delay-ms = <80>;
		disable-delay-ms = <10>;
		unprepare-delay-ms = <5>;

		width-mm = <68>;
		height-mm = <121>;

		// dsi,flags = <(MIPI_DSI_MODE_VIDEO | MIPI_DSI_MODE_VIDEO_BURST |
		// 	      MIPI_DSI_MODE_LPM | MIPI_DSI_MODE_EOT_PACKET)>;
		dsi,format = <MIPI_DSI_FMT_RGB888>;
		dsi,lanes = <4>;

		panel-init-sequence = [
			39 00 04 ff 98 81 03
			15 00 02 01 00
			15 00 02 02 00
			15 00 02 03 53
			15 00 02 04 53
			15 00 02 05 13
			15 00 02 06 04
			15 00 02 07 02
			15 00 02 08 02
			15 00 02 09 00
			15 00 02 0a 00
			15 00 02 0b 00
			15 00 02 0c 00
			15 00 02 0d 00
			15 00 02 0e 00
			15 00 02 0f 00
			15 00 02 10 00
			15 00 02 11 00
			15 00 02 12 00
			15 00 02 13 00
			15 00 02 14 00
			15 00 02 15 08
			15 00 02 16 10
			15 00 02 17 00
			15 00 02 18 08
			15 00 02 19 00
			15 00 02 1a 00
			15 00 02 1b 00
			15 00 02 1c 00
			15 00 02 1d 00
			15 00 02 1e c0
			15 00 02 1f 80
			15 00 02 20 02
			15 00 02 21 09
			15 00 02 22 00
			15 00 02 23 00
			15 00 02 24 00
			15 00 02 25 00
			15 00 02 26 00
			15 00 02 27 00
			15 00 02 28 55
			15 00 02 29 03
			15 00 02 2a 00
			15 00 02 2b 00
			15 00 02 2c 00
			15 00 02 2d 00
			15 00 02 2e 00
			15 00 02 2f 00
			15 00 02 30 00
			15 00 02 31 00
			15 00 02 32 00
			15 00 02 33 00
			15 00 02 34 04
			15 00 02 35 05
			15 00 02 36 05
			15 00 02 37 00
			15 00 02 38 3c
			15 00 02 39 35
			15 00 02 3a 00
			15 00 02 3b 40
			15 00 02 3c 00
			15 00 02 3d 00
			15 00 02 3e 00
			15 00 02 3f 00
			15 00 02 40 00
			15 00 02 41 88
			15 00 02 42 00
			15 00 02 43 00
			15 00 02 44 1f
			15 00 02 50 01
			15 00 02 51 23
			15 00 02 52 45
			15 00 02 53 67
			15 00 02 54 89
			15 00 02 55 ab
			15 00 02 56 01
			15 00 02 57 23
			15 00 02 58 45
			15 00 02 59 67
			15 00 02 5a 89
			15 00 02 5b ab
			15 00 02 5c cd
			15 00 02 5d ef
			15 00 02 5e 03
			15 00 02 5f 14
			15 00 02 60 15
			15 00 02 61 0c
			15 00 02 62 0d
			15 00 02 63 0e
			15 00 02 64 0f
			15 00 02 65 10
			15 00 02 66 11
			15 00 02 67 08
			15 00 02 68 02
			15 00 02 69 0a
			15 00 02 6a 02
			15 00 02 6b 02
			15 00 02 6c 02
			15 00 02 6d 02
			15 00 02 6e 02
			15 00 02 6f 02
			15 00 02 70 02
			15 00 02 71 02
			15 00 02 72 06
			15 00 02 73 02
			15 00 02 74 02
			15 00 02 75 14
			15 00 02 76 15
			15 00 02 77 0f
			15 00 02 78 0e
			15 00 02 79 0d
			15 00 02 7a 0c
			15 00 02 7b 11
			15 00 02 7c 10
			15 00 02 7d 06
			15 00 02 7e 02
			15 00 02 7f 0a
			15 00 02 80 02
			15 00 02 81 02
			15 00 02 82 02
			15 00 02 83 02
			15 00 02 84 02
			15 00 02 85 02
			15 00 02 86 02
			15 00 02 87 02
			15 00 02 88 08
			15 00 02 89 02
			15 00 02 8a 02
			39 00 04 ff 98 81 04
			15 00 02 00 80
			15 00 02 70 00
			15 00 02 71 00
			15 00 02 66 fe
			15 00 02 82 15
			15 00 02 84 15
			15 00 02 85 15
			15 00 02 3a 24
			15 00 02 32 ac
			15 00 02 8c 80
			15 00 02 3c f5
			15 00 02 88 33
			39 00 04 ff 98 81 01
			15 00 02 22 0a
			15 00 02 31 00
			15 00 02 53 78
			15 00 02 55 7b
			15 00 02 60 20
			15 00 02 61 00
			15 00 02 62 0d
			15 00 02 63 00
			15 00 02 a0 00
			15 00 02 a1 10
			15 00 02 a2 1c
			15 00 02 a3 13
			15 00 02 a4 15
			15 00 02 a5 26
			15 00 02 a6 1a
			15 00 02 a7 1d
			15 00 02 a8 67
			15 00 02 a9 1c
			15 00 02 aa 29
			15 00 02 ab 5b
			15 00 02 ac 26
			15 00 02 ad 28
			15 00 02 ae 5c
			15 00 02 af 30
			15 00 02 b0 31
			15 00 02 b1 32
			15 00 02 b2 00
			15 00 02 b1 2e
			15 00 02 b2 32
			15 00 02 b3 00
			15 00 02 c0 00
			15 00 02 c1 10
			15 00 02 c2 1c
			15 00 02 c3 13
			15 00 02 c4 15
			15 00 02 c5 26
			15 00 02 c6 1a
			15 00 02 c7 1d
			15 00 02 c8 67
			15 00 02 c9 1c
			15 00 02 ca 29
			15 00 02 cb 5b
			15 00 02 cc 26
			15 00 02 cd 28
			15 00 02 ce 5c
			15 00 02 cf 30
			15 00 02 d0 31
			15 00 02 d1 2e
			15 00 02 d2 32
			15 00 02 d3 00
			39 00 04 ff 98 81 00
			05 00 01 11
			05 01 01 29
		];

		display-timings {
			native-mode = <&timing0>;

			timing0: timing0 {
				clock-frequency = <65000000>;
				hactive = <720>;
				vactive = <1280>;
				hfront-porch = <48>;
				hsync-len = <8>;
				hback-porch = <52>;
				vfront-porch = <16>;
				vsync-len = <6>;
				vback-porch = <15>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <0>;
				pixelclk-active = <0>;
			};
		};

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				panel_in_dsi: endpoint {
					remote-endpoint = <&dsi_out_panel>;
				};
			};
		};
	};

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@1 {
			reg = <1>;
			dsi_out_panel: endpoint {
				remote-endpoint = <&panel_in_dsi>;
			};
		};
	};
};

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_in_ucam0: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out0>;
				data-lanes = <1 2>;
			};

			mipi_in_ucam0_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};

			mipi_in_ucam0_2815: endpoint@3 {
				reg = <3>;
				remote-endpoint = <&ucam_out0_2815>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

#if 1
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
#else
			csi_dphy1_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_in>;
			};
#endif
		};
	};
};

&csi2_dphy1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			csi_dphy1_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ucam_out1>;
				data-lanes = <1 2>;
			};

			
			csi_dphy1_input_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

#if 1
			csi_dphy1_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&isp_in>;
				data-lanes = <1 2>;
			};
#else
			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
#endif
		};
	};
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	mmc-hs200-1_8v;
	rockchip,default-sample-phase = <90>;
	supports-emmc;
	/delete-property/ pinctrl-names;
	/delete-property/ pinctrl-0;
	status = "okay";
};

&fiq_debugger {
	status = "okay";
};

&gmac {
	phy-mode = "rmii";
	clock_in_out = "output";

	snps,reset-gpio = <&gpio2 RK_PA4 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	/* Reset time is 20ms, 100ms for rtl8211f */
	snps,reset-delays-us = <100000 200000 100000>;

	assigned-clocks = <&cru CLK_MAC_OUT2IO>;
	assigned-clock-rates = <25000000>;

	// assigned-clocks = <&cru CLK_GMAC_SRC_M1>, <&cru CLK_GMAC_SRC>, <&cru CLK_GMAC_TX_RX>;
	// assigned-clock-rates = <0>, <50000000>;
	// assigned-clock-parents = <&cru CLK_GMAC_RGMII_M1>, <&cru CLK_GMAC_SRC_M1>, <&cru RMII_MODE_CLK>;

	pinctrl-names = "default";
	pinctrl-0 = <&rmiim1_miim &rgmiim1_rxer &rmiim1_bus2 &rgmiim1_mclkinout>;
	// pinctrl-0 = <&rgmiim1_miim &rgmiim1_bus2 &rgmiim1_bus4 &clkm1_out_ethernet>;

	phy-handle = <&phy>;
	status = "okay";
};

&i2c0 {
	status = "okay";
	clock-frequency = <400000>;

	rk809: pmic@20 {
		compatible = "rockchip,rk809";
		reg = <0x20>;
		interrupt-parent = <&gpio0>;
		interrupts = <9 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default", "pmic-sleep",
			"pmic-power-off", "pmic-reset";
		pinctrl-0 = <&pmic_int>;
		pinctrl-1 = <&soc_slppin_gpio>, <&rk817_slppin_slp>;
		pinctrl-2 = <&soc_slppin_gpio>, <&rk817_slppin_pwrdn>;
		pinctrl-3 = <&soc_slppin_slp>, <&rk817_slppin_rst>;
		rockchip,system-power-controller;
		wakeup-source;
		#clock-cells = <1>;
		clock-output-names = "rk808-clkout1", "rk808-clkout2";
		/* 0: rst the pmic, 1: rst regs (default in codes) */
		pmic-reset-func = <0>;

		vcc1-supply = <&vcc5v0_sys>;
		vcc2-supply = <&vcc5v0_sys>;
		vcc3-supply = <&vcc5v0_sys>;
		vcc4-supply = <&vcc5v0_sys>;
		vcc5-supply = <&vcc_buck5>;
		vcc6-supply = <&vcc_buck5>;
		vcc7-supply = <&vcc5v0_sys>;
		vcc8-supply = <&vcc3v3_sys>;
		vcc9-supply = <&vcc5v0_sys>;

		pwrkey {
			status = "okay";
		};

		pinctrl_rk8xx: pinctrl_rk8xx {
			gpio-controller;
			#gpio-cells = <2>;

			/omit-if-no-ref/
			rk817_slppin_null: rk817_slppin_null {
				pins = "gpio_slp";
				function = "pin_fun0";
			};

			/omit-if-no-ref/
			rk817_slppin_slp: rk817_slppin_slp {
				pins = "gpio_slp";
				function = "pin_fun1";
			};

			/omit-if-no-ref/
			rk817_slppin_pwrdn: rk817_slppin_pwrdn {
				pins = "gpio_slp";
				function = "pin_fun2";
			};

			/omit-if-no-ref/
			rk817_slppin_rst: rk817_slppin_rst {
				pins = "gpio_slp";
				function = "pin_fun3";
			};
		};

		regulators {
			vdd_logic: DCDC_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <725000>;
				regulator-max-microvolt = <1350000>;
				regulator-ramp-delay = <6001>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vdd_logic";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <800000>;
				};
			};

			vdd_arm: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <725000>;
				regulator-max-microvolt = <1350000>;
				regulator-ramp-delay = <6001>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vdd_arm";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-initial-mode = <0x2>;
				regulator-name = "vcc_ddr";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc3v3_sys: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-initial-mode = <0x2>;
				regulator-name = "vcc3v3_sys";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_buck5: DCDC_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2200000>;
				regulator-max-microvolt = <2200000>;
				regulator-name = "vcc_buck5";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <2200000>;
				};
			};

			vcc_0v8: LDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <800000>;
				regulator-name = "vcc_0v8";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc1v8_pmu: LDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc1v8_pmu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vdd0v8_pmu: LDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <800000>;
				regulator-name = "vcc0v8_pmu";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <800000>;
				};
			};

			vcc_1v8: LDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc_1v8";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_dovdd: LDO_REG5 {
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "vcc_dovdd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_dvdd: LDO_REG6 {
				regulator-min-microvolt = <1250000>;
				regulator-max-microvolt = <1250000>;
				regulator-name = "vcc_dvdd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_avdd: LDO_REG7 {
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-name = "vcc_avdd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vccio_sd: LDO_REG8 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vccio_sd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc3v3_sd: LDO_REG9 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-name = "vcc3v3_sd";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc5v0_host: SWITCH_REG1 {
				regulator-name = "vcc5v0_host";
			};

			vcc_3v3: SWITCH_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vcc_3v3";
			};
		};

		rk809_codec: codec {
			#sound-dai-cells = <0>;
			compatible = "rockchip,rk809-codec", "rockchip,rk817-codec";
			clocks = <&sai0_mclkout>;
			clock-names = "mclk";
			assigned-clocks = <&sai0_mclkout>;
			assigned-clock-rates = <12288000>;
			pinctrl-names = "default";
			pinctrl-0 = <&sai0m0_mclk_pins>;
			hp-volume = <20>;
			spk-volume = <3>;
		};
	};
};

&i2c1 {
	clock-frequency = <100000>;
	status = "okay";
	ar0230: ar0230@10 {
		compatible = "aptina,ar0230";
		reg = <0x10>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		// dvdd-supply = <&vcc_dvdd>;
		// power-domains = <&power RV1126_PD_VI>;
		pwdn-gpios = <&gpio2 RK_PA6 GPIO_ACTIVE_HIGH>;
		/*reset-gpios = <&gpio2 RK_PC5 GPIO_ACTIVE_HIGH>;*/
		rockchip,grf = <&grf>;
		pinctrl-names = "default";
		pinctrl-0 = <&cifm0_dvp_ctl>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "CMK-OT0836-PT2";
		rockchip,camera-module-lens-name = "YT-2929";
		port {
			cam_para_out1: endpoint {
				/* remote-endpoint = <&cif_para_in>; */
			};
		};
	};

	RN6752_1: RN6752@2c {
        compatible = "sony,imx415";
        reg = <0x2c>;
        clocks = <&cru CLK_MIPI0_OUT2IO>;
        clock-names = "xvclk";
        // power-domains = <&power RV1126_PD_VI>;
        pinctrl-names = "rockchip,camera_default";
        pinctrl-0 = <&mipicsi_clk0>;
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 RK_PA2 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpio3 RK_PB3 GPIO_ACTIVE_LOW>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out1_6752: endpoint {
                remote-endpoint = <&csi_dphy1_input_6752>;
                data-lanes = <1 2>;
            };
        };
    };

	RN6752_2: RN6752@2d {
        compatible = "sony,imx415";
        reg = <0x2d>;
       clocks = <&cru CLK_MIPI0_OUT2IO>;
        clock-names = "xvclk";
        // power-domains = <&power RV1126_PD_VI>;
        pinctrl-names = "rockchip,camera_default";
        pinctrl-0 = <&mipicsi_clk0>;
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 RK_PA3 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpio3 RK_PB2 GPIO_ACTIVE_LOW>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out0_6752: endpoint {
                remote-endpoint = <&mipi_in_ucam0_6752>;
                data-lanes = <1 2>;
            };
        };
    };

	tp2815: tp2815@44 {
        compatible = "techpoint,tp2815";
        reg = <0x44>;
        clocks = <&cru CLK_MIPI0_OUT2IO>;
        clock-names = "xvclk";
        // power-domains = <&power RV1126_PD_VI>;
        pinctrl-names = "rockchip,camera_default";
        pinctrl-0 = <&mipicsi_clk0>;
        avdd-supply = <&vcc_avdd>;
        dovdd-supply = <&vcc_dovdd>;
        dvdd-supply = <&vcc_dvdd>;
        power-gpios = <&gpio2 RK_PA3 GPIO_ACTIVE_HIGH>;
        reset-gpios = <&gpio3 RK_PB2 GPIO_ACTIVE_LOW>;
        rockchip,camera-module-index = <0>;
        rockchip,camera-module-facing = "front";
        rockchip,camera-module-name = "YT10092";
        rockchip,camera-module-lens-name = "IR0147-60IRC-8M-F20-hdr3";
        port {
            ucam_out0_2815: endpoint {
                remote-endpoint = <&mipi_in_ucam0_2815>;
                data-lanes = <1 2>;
            };
        };
    };

	ov4689: ov4689@5f {
		compatible = "ovti,os04a10";
		reg = <0x5f>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		// power-domains = <&power RV1126_PD_VI>;
		pinctrl-names = "rockchip,camera_default";
		pinctrl-0 = <&mipicsi_clk1>;
		/*pinctrl-0 = <&mipicsi_clk0>;*/
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		reset-gpios = <&gpio3 RK_PB3 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "JSD3425-C1";
		rockchip,camera-module-lens-name = "JSD3425-C1";
		port {
			ucam_out1: endpoint {
				remote-endpoint = <&csi_dphy1_input>;
				data-lanes = <1 2>;
			};
		};

	};

	os04a10: os04a10@5c {
		compatible = "ovti,os04a10";
		reg = <0x5c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		// power-domains = <&power RV1126_PD_VI>;
		pinctrl-names = "rockchip,camera_default";
		pinctrl-0 = <&mipicsi_clk0>;
		avdd-supply = <&vcc_avdd>;
		dovdd-supply = <&vcc_dovdd>;
		dvdd-supply = <&vcc_dvdd>;
		// pwdn-gpios = <&gpio1 RK_PD4 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio3 RK_PB2 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "CMK-OT1607-FV1";
		rockchip,camera-module-lens-name = "M12-40IRC-4MP-F16";
		// ir-cut = <&cam_ircut0>;
		port {
			ucam_out0: endpoint {
				remote-endpoint = <&mipi_in_ucam0>;
				data-lanes = <1 2>;
			};
		};
	};
};

&i2c2 {
	status = "okay";
	clock-frequency = <100000>;
};

&i2c5 {
	status = "okay";
	clock-frequency = <400000>;

	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;

		// interrupts = <13 IRQ_TYPE_EDGE_FALLING>;

		#clock-cells = <0>;
	};

};

// &i2s0_8ch {
// 	status = "okay";
// 	#sound-dai-cells = <0>;
// 	rockchip,clk-trcm = <1>;
// 	rockchip,i2s-rx-route = <3 1 2 0>;
// 	pinctrl-names = "default";
// 	pinctrl-0 = <&i2s0m0_sclk_tx
// 		     &i2s0m0_lrck_tx
// 		     &i2s0m0_sdo0
// 		     &i2s0m0_sdo1_sdi3>;
// };

// &iep {
// 	status = "okay";
// };

// &iep_mmu {
// 	status = "okay";
// };

&mdio {
	phy: phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0x1>;
		clocks = <&cru CLK_MAC_OUT2IO>;
	};
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in>;
				data-lanes = <1 2>;
			};
		};
	};
};

// &mipi0_dphy {
// 	status = "okay";
// };

&mpp_srv {
	status = "okay";
};

// &nandc {
// 	/delete-property/ pinctrl-names;
// 	/delete-property/ pinctrl-0;
// 	#address-cells = <1>;
// 	#size-cells = <0>;

// 	nand@0 {
// 		reg = <0>;
// 		nand-bus-width = <8>;
// 		nand-ecc-mode = "hw";
// 		nand-ecc-strength = <16>;
// 		nand-ecc-step-size = <1024>;
// 	};
// };

&rknpu {
	npu-supply = <&vdd_npu>;
	status = "okay";
};

&tsadc {
	status = "okay";
};

&optee {
	status = "disabled";
};

&otp {
	status = "okay";
};

// &pdm {
// 	status = "disabled";
// };

&pinctrl {
	pmic {
		/omit-if-no-ref/
		pmic_int: pmic_int {
			rockchip,pins =
				<0 RK_PB1 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		/omit-if-no-ref/
		soc_slppin_gpio: soc_slppin_gpio {
			rockchip,pins =
				<0 RK_PB2 RK_FUNC_GPIO &pcfg_output_low>;
		};

		// /omit-if-no-ref/
		// soc_slppin_slp: soc_slppin_slp {
		// 	rockchip,pins =
		// 		<0 RK_PB2 RK_FUNC_1 &pcfg_pull_none>;
		// };

		// /omit-if-no-ref/
		// soc_slppin_rst: soc_slppin_rst {
		// 	rockchip,pins =
		// 		<0 RK_PB2 RK_FUNC_2 &pcfg_pull_none>;
		// };
	};

	sdio-pwrseq {
		/omit-if-no-ref/
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <0 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-wlan {
		/omit-if-no-ref/
		wifi_wake_host: wifi-wake-host {
			rockchip,pins = <0 RK_PB0 0 &pcfg_pull_up>;
		};
	};
};

// &pmu_io_domains {
// 	status = "okay";

// 	pmuio0-supply = <&vcc3v3_sys>;	// 从1.8v改到3.3v，硬件那边也需要改电路
// 	pmuio1-supply = <&vcc3v3_sys>;
// 	vccio2-supply = <&vccio_sd>;
// 	vccio3-supply = <&vcc_1v8>;
// 	vccio4-supply = <&vcc_1v8>;
// 	vccio5-supply = <&vcc_3v3>;
// 	vccio6-supply = <&vcc_1v8>;
// 	vccio7-supply = <&vcc_1v8>;
// };

&pwm0_8ch_0 {
	status = "okay";
	// pinctrl-names = "active";
	// pinctrl-0 = <&pwm0m0_pins_pull_down>;
};

&pwm0_8ch_1 {
	status = "okay";
	// pinctrl-names = "active";
	// pinctrl-0 = <&pwm1m0_pins_pull_down>;
};

&pwm0_8ch_3 {
	status = "okay";
};

&ramoops {
	status = "okay";
};

&rga2_core0 {
	status = "okay";
};

&rkcif {
	status = "okay";
};

&rkcif_mmu {
	status = "disabled";
};

&rkcif_dvp {
	status = "okay";

	port {
		/* Parallel bus endpoint */
		/*
		cif_para_in: endpoint {
			remote-endpoint = <&cam_para_out1>;
			bus-width = <12>;
			hsync-active = <1>;
			vsync-active = <0>;
		};
		*/
	};
};

&rkcif_mipi_lvds {
	status = "okay";
	// rockchip,cif-monitor = <2 2 5 100 1>;
	rockchip,cif-monitor = <3 2 10 10 5>;
	port {
		/* MIPI CSI-2 endpoint */
		cif_mipi_in: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
			data-lanes = <1 2>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";

	port {
		/* MIPI CSI-2 endpoint */
		mipi_lvds_sditf: endpoint {
			// remote-endpoint = <&isp_in>;
			data-lanes = <1 2>;
		};
	};
};

&rkisp {
	status = "okay";
};

&rkisp_vir0 {
	status = "okay";

	ports {
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			isp_in: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&csi_dphy1_output>;
				data-lanes = <1 2>;
			};
		};
	};
};

// &rkisp_vir1 {
// 	status = "okay";

// 	ports {
// 		port@0 {
// 			reg = <0>;
// 			#address-cells = <1>;
// 			#size-cells = <0>;

// 			isp1_in: endpoint@0 {
// 				reg = <0>;
// 				remote-endpoint = <&csi_dphy1_output>;
// 				data-lanes = <1 2>;
// 			};
// 		};
// 	};
// };

// &rkisp_mmu {
// 	status = "disabled";
// };

// &rkispp {
// 	status = "disabled";
// 	/* the max input w h and fps of mulit sensor */
// 	max-input = <1280 720 25>;
// };

// &rkispp_vir0 {
// 	status = "disabled";
// };

// &rkispp_vir1 {
// 	status = "disabled";
// };

// &rkispp_mmu {
// 	status = "okay";
// };

&rkvdec {
	status = "okay";
};

&rkvdec_mmu {
	status = "okay";
};

&rkvenc {
	venc-supply = <&vdd_vepu>;
	status = "okay";
};

&rkvenc_mmu {
	status = "okay";
};

&rng {
	status = "okay";
};

&rockchip_suspend {
	status = "okay";
	rockchip,sleep-debug-en = <1>;
	rockchip,sleep-mode-config = <
		(0
		| RKPM_SLP_ARMOFF
		| RKPM_SLP_PMU_PMUALIVE_32K
		| RKPM_SLP_PMU_DIS_OSC
		| RKPM_SLP_PMIC_LP
		)
	>;
	rockchip,wakeup-config = <
			(0
			| RKPM_GPIO0_WKUP_EN
			)
		>;
};

&route_dsi {
	status = "okay";
};

&saradc1 {
	status = "okay";
	vref-supply = <&vcc_1v8>;
};

&sdmmc0 {
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	card-detect-delay = <200>;
	rockchip,default-sample-phase = <90>;
	supports-sd;
	sd-uhs-sdr12;
	sd-uhs-sdr25;
	sd-uhs-sdr104;
	vqmmc-supply = <&vccio_sd>;
	vmmc-supply = <&vcc3v3_sd>;
	status = "okay";
};

&sdmmc0 {
	max-frequency = <200000000>;
	bus-width = <4>;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	non-removable;
	rockchip,default-sample-phase = <90>;
	sd-uhs-sdr104;
	supports-sdio;
	mmc-pwrseq = <&sdio_pwrseq>;
	status = "okay";
};

&fspi0 {
	/delete-property/ pinctrl-names;
	/delete-property/ pinctrl-0;
	status = "okay";
};

&usb2phy {
	status = "okay";
	vup-gpios = <&gpio0 RK_PC1 GPIO_ACTIVE_LOW>;
	u2phy_otg: otg-port {
		status = "okay";
	};
};

&usb3phy {
	status = "okay";
	u2phy_host: host-port {
		status = "okay";
		phy-supply = <&vcc5v0_host>;
	};
};

&uart2 {

	rs485enable = <1>;
	rs485-gpio = <&gpio3 RK_PA6 GPIO_ACTIVE_LOW>;
	status = "okay";
};

&uart3 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart3m0_xfer>;
};
&usb_host_ehci {
	status = "okay";
};

&usb_host_ohci {
	status = "okay";
};

// &usbdrd {
// 	status = "okay";
// };

&usb_drd_dwc3 {
	status = "okay";
	extcon = <&u2phy0>;
};

// &vdpu {
// 	status = "okay";
// };

// &vepu {
// 	status = "okay";
// };

// &vpu_mmu {
// 	status = "okay";
// };

&vop {
	status = "okay";
};

&vop_mmu {
	status = "okay";
};
