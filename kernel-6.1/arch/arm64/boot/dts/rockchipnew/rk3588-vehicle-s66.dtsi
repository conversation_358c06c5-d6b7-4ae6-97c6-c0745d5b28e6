// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pwm/pwm.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/display/drm_mipi_dsi.h>
#include <dt-bindings/display/rockchip_vop.h>
#include <dt-bindings/sensor-dev.h>
/ {
	backlight: backlight {
		compatible = "pwm-backlight";
		brightness-levels = <
			0  20  20  21  21  22  22  23
			23  24  24  25  25  26  26  27
			27  28  28  29  29  30  30  31
			31  32  32  33  33  34  34  35
			35  36  36  37  37  38  38  39
			40  41  42  43  44  45  46  47
			48  49  50  51  52  53  54  55
			56  57  58  59  60  61  62  63
			64  65  66  67  68  69  70  71
			72  73  74  75  76  77  78  79
			80  81  82  83  84  85  86  87
			88  89  90  91  92  93  94  95
			96  97  98  99 100 101 102 103
			104 105 106 107 108 109 110 111
			112 113 114 115 116 117 118 119
			120 121 122 123 124 125 126 127
			128 129 130 131 132 133 134 135
			136 137 138 139 140 141 142 143
			144 145 146 147 148 149 150 151
			152 153 154 155 156 157 158 159
			160 161 162 163 164 165 166 167
			168 169 170 171 172 173 174 175
			176 177 178 179 180 181 182 183
			184 185 186 187 188 189 190 191
			192 193 194 195 196 197 198 199
			200 201 202 203 204 205 206 207
			208 209 210 211 212 213 214 215
			216 217 218 219 220 221 222 223
			224 225 226 227 228 229 230 231
			232 233 234 235 236 237 238 239
			240 241 242 243 244 245 246 247
			248 249 250 251 252 253 254 255
		>;
		default-brightness-level = <200>;
	};

	test-power {
		status = "okay";
	};

	vcc12v_dcin: vcc12v-dcin {
		compatible = "regulator-fixed";
		regulator-name = "vcc12v_dcin";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
	};

	vcc5v0_sys: vcc5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vcc12v_dcin>;
	};

	vcc5v0_usbdcin: vcc5v0-usbdcin {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_usbdcin";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vcc12v_dcin>;
	};

	vcc5v0_usb: vcc5v0-usb {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_usb";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vcc5v0_usbdcin>;
	};
};

&av1d_mmu {
	status = "okay";
};

&cpu_l0 {
	cpu-supply = <&vdd_cpu_lit_s0>;
	mem-supply = <&vdd_cpu_lit_s0>;
};

&cpu_b0 {
	cpu-supply = <&vdd_cpu_big0_s0>;
	mem-supply = <&vdd_cpu_big0_s0>;
};

&cpu_b2 {
	cpu-supply = <&vdd_cpu_big1_s0>;
	mem-supply = <&vdd_cpu_big1_s0>;
};

&gpu {
	mali-supply = <&vdd_gpu_s0>;
	mem-supply = <&vdd_gpu_s0>;
	status = "okay";
};

&i2s0_8ch {
	status = "disabled";
};

&iep {
	status = "okay";
};

&iep_mmu {
	status = "okay";
};

&jpegd {
	status = "okay";
};

&jpegd_mmu {
	status = "okay";
};

&jpege_ccu {
	status = "okay";
};

&jpege0 {
	status = "okay";
};

&jpege0_mmu {
	status = "okay";
};

&jpege1 {
	status = "okay";
};

&jpege1_mmu {
	status = "okay";
};

&jpege2 {
	status = "okay";
};

&jpege2_mmu {
	status = "okay";
};

&jpege3 {
	status = "okay";
};

&jpege3_mmu {
	status = "okay";
};

&mpp_srv {
	status = "okay";
};

&rga3_core0 {
	status = "okay";
};

&rga3_0_mmu {
	status = "okay";
};

&rga3_core1 {
	status = "okay";
};

&rga3_1_mmu {
	status = "okay";
};

&rga2 {
	status = "okay";
};

&rknpu {
	rknpu-supply = <&vdd_npu_s0>;
	mem-supply = <&vdd_npu_s0>;
	status = "okay";
};

&rknpu_mmu {
	status = "okay";
};

&rkvdec_ccu {
	status = "okay";
};

&rkvdec0 {
	status = "okay";
};

&rkvdec0_mmu {
	status = "okay";
};

&rkvdec1 {
	status = "okay";
};

&rkvdec1_mmu {
	status = "okay";
};

&rkvenc_ccu {
	status = "okay";
};

&rkvenc0 {
	status = "okay";
};

&rkvenc0_mmu {
	status = "okay";
};

&rkvenc1 {
	status = "okay";
};

&rkvenc1_mmu {
	status = "okay";
};

&rockchip_suspend {
	status = "okay";
	rockchip,sleep-debug-en = <1>;
};

&saradc {
	status = "okay";
	vref-supply = <&vcc_1v8_s0>;
};

&sdhci {
	bus-width = <8>;
	no-sdio;
	no-sd;
	non-removable;
	max-frequency = <200000000>;
	mmc-hs400-1_8v;
	mmc-hs400-enhanced-strobe;
	status = "okay";
};

&sdmmc {
	max-frequency = <150000000>;
	no-sdio;
	no-mmc;
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
	sd-uhs-sdr104;
	vqmmc-supply = <&vccio_sd_s0>;
	status = "disabled";
};

&tsadc {
	status = "okay";
};

&u2phy0 {
	status = "okay";
};

&u2phy1 {
	status = "okay";
};

&u2phy2 {
	status = "okay";
};

&u2phy3 {
	status = "okay";
};

&u2phy0_otg {
	status = "okay";
};

&u2phy1_otg {
	rockchip,sel-pipe-phystatus;
	status = "okay";
};

&u2phy2_host {
	status = "okay";
};

&u2phy3_host {
	status = "okay";
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host0_ohci {
	status = "okay";
};

&usb_host1_ehci {
	status = "okay";
};

&usb_host1_ohci {
	status = "okay";
};

&usbdp_phy0 {
	rockchip,dp-lane-mux = <2 3>;
	status = "okay";
};

&usbdp_phy0_dp {
	status = "disabled";
};

&usbdp_phy0_u3 {
	status = "okay";
};

&usbdp_phy1 {
	status = "okay";
};

&usbdp_phy1_dp {
	status = "okay";
};

&usbdp_phy1_u3 {
	status = "okay";
};

&usbdrd3_0 {
	status = "okay";
};

&usbdrd_dwc3_0 {
	dr_mode = "otg";
	extcon=<&u2phy0>;
	status = "okay";
};

&usbhost3_0 {
	status = "okay";
};

&usbhost_dwc3_0 {
	status = "okay";
};

&usbdrd3_1 {
	status = "okay";
};

&usbdrd_dwc3_1 {
	dr_mode = "host";
	maximum-speed = "high-speed";
	phys = <&u2phy1_otg>;
	phy-names = "usb2-phy";
	snps,dis_u2_susphy_quirk;
	status = "okay";
};

&vdpu {
	status = "okay";
};

&vdpu_mmu {
	status = "okay";
};

&vepu {
	status = "okay";
};

&vop {
	status = "okay";
	vop-supply = <&vdd_log_s0>;
};

&vop_mmu {
	status = "okay";
};

/* vp0 & vp1 splice for 8K output */
&vp0 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER0 | 1 << ROCKCHIP_VOP2_ESMART0)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART0>;
};

&vp1 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER1 | 1 << ROCKCHIP_VOP2_ESMART1)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART1>;
};

&vp2 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER2 | 1 << ROCKCHIP_VOP2_ESMART2)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART2>;
};

&vp3 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER3 | 1 << ROCKCHIP_VOP2_ESMART3)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART3>;
};
