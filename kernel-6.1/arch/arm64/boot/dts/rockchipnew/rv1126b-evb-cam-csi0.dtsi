// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			csi_dphy_input0: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&sc450ai_out>;
				data-lanes = <1 2>;
			};

			csi_dphy_input1: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&imx415_out>;
				data-lanes = <1 2 3 4>;
			};

			csi_dphy_input2: endpoint@3 {
				reg = <3>;
				remote-endpoint = <&sc850sl_out>;
				data-lanes = <1 2 3 4>;
			};

			csi_dphy_input3: endpoint@4 {
				reg = <4>;
				remote-endpoint = <&tp2815_out>;
				data-lanes = <1 2 3 4>;
			};

			csi_dphy_input4: endpoint@5 {
				reg = <5>;
				remote-endpoint = <&imx327_out>;
				data-lanes = <4>;
			};

			csi_dphy_input5: endpoint@6 {
				reg = <6>;
				remote-endpoint = <&gc8613_out>;
				data-lanes = <1 2 3 4>;
			};

			csi_dphy_input6: endpoint@7 {
				reg = <7>;
				remote-endpoint = <&sc635hai_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

&i2c3 {
	status = "okay";
	pinctrl-0 = <&i2c3m1_pins>;

	imx327: imx327@1a {
		compatible = "sony,imx327";
		reg = <0x1a>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		reset-gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			imx327_out: endpoint {
				remote-endpoint = <&csi_dphy_input4>;
				bus-type = <3>;
				data-lanes = <4>;
			};
		};
	};

	imx415: imx415@1a {
		compatible = "sony,imx415";
		reg = <0x1a>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		reset-gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			imx415_out: endpoint {
				remote-endpoint = <&csi_dphy_input1>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	sc450ai: sc450ai@30 {
		compatible = "smartsens,sc450ai";
		status = "okay";
		reg = <0x30>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		reset-gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
		pwdn-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			sc450ai_out: endpoint {
				remote-endpoint = <&csi_dphy_input0>;
				data-lanes = <1 2>;
			};
		};
	};

	sc850sl: sc850sl@30 {
		compatible = "smartsens,sc850sl";
		status = "okay";
		reg = <0x30>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		reset-gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_LOW>;
		pwdn-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			sc850sl_out: endpoint {
				remote-endpoint = <&csi_dphy_input2>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	sc635hai: sc635hai@30 {
		compatible = "smartsens,sc635hai";
		status = "okay";
		reg = <0x30>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		reset-gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
		pwdn-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			sc635hai_out: endpoint {
				remote-endpoint = <&csi_dphy_input6>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	gc8613: gc8613@31 {
		compatible = "galaxycore,gc8613";
		reg = <0x31>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		reset-gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
		pwdn-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			gc8613_out: endpoint {
				remote-endpoint = <&csi_dphy_input5>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	tp2815: tp2815@44 {
		compatible = "techpoint,tp2815";
		status = "okay";
		reg = <0x44>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		power-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";

		port {
			tp2815_out: endpoint {
				remote-endpoint = <&csi_dphy_input3>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in0>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in0: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";

	port {
		mipi_lvds_sditf: endpoint {
			remote-endpoint = <&isp_vir0>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp {
	status = "okay";
};

&rkisp_mmu {
	status = "okay";
};

&rkisp_vir0 {
	status = "okay";

	port {
		#address-cells = <1>;
		#size-cells = <0>;

		isp_vir0: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf>;
		};
	};
};

&rkisp_vir0_sditf {
	status = "okay";
};

&rkvpss {
	status = "okay";
	dvbm = <&rkdvbm>;
};

&rkvpss_mmu {
	status = "okay";
};

&rkvpss_vir0 {
	status = "okay";
};
