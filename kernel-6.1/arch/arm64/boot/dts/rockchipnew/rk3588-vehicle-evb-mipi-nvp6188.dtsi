// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2022 Rockchip Electronics Co., Ltd.
 *
 */

&csi2_dphy0_hw {
	status = "okay";
};

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy0_in_nvp6188: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&nvp6188_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_input>;
			};
		};
	};
};

&i2c7 {
	status = "okay";


	nvp6188: nvp6188@31 {
		compatible = "nvp6188";
		status = "okay";
		reg = <0x31>;
		clocks = <&cru CLK_MIPI_CAMARAOUT_M2>;
		clock-names = "xvclk";
		power-domains = <&power RK3588_PD_VI>;
		pinctrl-names = "default";
		pinctrl-0 = <&mipim1_camera1_clk>;
		rockchip,grf = <&sys_grf>;
		/*power-gpios = <&gpio3 RK_PC6 GPIO_ACTIVE_HIGH>;*/
		reset-gpios = <&gpio3 RK_PB3 GPIO_ACTIVE_HIGH>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "nvp6188";
		rockchip,camera-module-lens-name = "nvp6188";

		port {
			nvp6188_out: endpoint {
				remote-endpoint = <&mipi_dphy0_in_nvp6188>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&mipi2_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi2_in>;
			};
		};
	};
};

&rkcif_mipi_lvds2 {
	status = "okay";
	/* parameters for do cif reset detecting:
	 * index0: monitor mode,
		   0 for idle,
		   1 for continue,
		   2 for trigger,
		   3 for hotplug (for nextchip)
	 * index1: the frame id to start timer,
		   min is 2
	 * index2: frame num of monitoring cycle
	 * index3: err time for keep monitoring
		   after finding out err (ms)
	 * index4: csi2 err reference val for resetting
	 */
	rockchip,cif-monitor = <3 2 1 1000 5>;

	port {
		cif_mipi2_in: endpoint {
			remote-endpoint = <&mipi2_csi2_output>;
		};
	};
};

&rkcif {
	status = "okay";
	rockchip,android-usb-camerahal-enable;
	// memory-region = <&cif_reserved>;
};

&rkcif_mmu {
	status = "okay";
};

