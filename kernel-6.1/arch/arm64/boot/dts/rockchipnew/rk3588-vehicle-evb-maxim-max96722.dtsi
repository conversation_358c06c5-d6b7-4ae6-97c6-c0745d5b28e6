// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

/ {
	max96722_dphy0_osc0: max96722-dphy0-oscillator@0 {
		compatible = "fixed-clock";
		#clock-cells = <1>;
		clock-frequency = <25000000>;
		clock-output-names = "max96722-dphy0-osc0";
	};
};

&csi2_dphy0_hw {
	status = "okay";
};

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy0_in_max96722: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&max96722_dphy0_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_input>;
			};
		};
	};
};

&i2c7 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c7m3_xfer>;

	max96722_dphy0: max96722@29 {
		compatible = "maxim,max96722";
		status = "okay";
		reg = <0x29>;
		clock-names = "xvclk";
		clocks = <&max96722_dphy0_osc0 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96722_dphy0_power>, <&max96722_dphy0_errb>, <&max96722_dphy0_lock>;
		power-domains = <&power RK3588_PD_VI>;
		rockchip,grf = <&sys_grf>;
		power-gpios = <&gpio1 RK_PB2 GPIO_ACTIVE_HIGH>;
		pocen-gpios = <&gpio3 RK_PB0 GPIO_ACTIVE_HIGH>;
		lock-gpios = <&gpio3 RK_PB7 GPIO_ACTIVE_HIGH>;
		link-mask = <0x0F>;
		auto-init-deskew-mask = <0x03>;
		frame-sync-period = <0>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "max96722";
		rockchip,camera-module-lens-name = "max96722";

		port {
			max96722_dphy0_out: endpoint {
				remote-endpoint = <&mipi_dphy0_in_max96722>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&mipi2_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi2_in>;
			};
		};
	};
};

&rkcif_mipi_lvds2 {
	status = "okay";
	/* parameters for do cif reset detecting:
	 * index0: monitor mode,
		   0 for idle,
		   1 for continue,
		   2 for trigger,
		   3 for hotplug (for nextchip)
	 * index1: the frame id to start timer,
		   min is 2
	 * index2: frame num of monitoring cycle
	 * index3: err time for keep monitoring
		   after finding out err (ms)
	 * index4: csi2 err reference val for resetting
	 */
	rockchip,cif-monitor = <3 2 1 1000 5>;

	port {
		cif_mipi2_in: endpoint {
			remote-endpoint = <&mipi2_csi2_output>;
		};
	};
};

&rkcif {
	status = "okay";
	rockchip,android-usb-camerahal-enable;
};

&rkcif_mmu {
	status = "okay";
};

&pinctrl {
	max96722-dphy0 {
		max96722_dphy0_power: max96722-dphy0-power {
			rockchip,pins = <1 RK_PB2 RK_FUNC_GPIO &pcfg_output_low>;
		};

		max96722_dphy0_errb: max96722-dphy0-errb {
			rockchip,pins = <3 RK_PD1 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};

		max96722_dphy0_lock: max96722-dphy0-lock {
			rockchip,pins = <3 RK_PB7 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};
	};
};
