// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */
#include <dt-bindings/display/media-bus-format.h>

/ {
	max96722_dphy0_osc0: max96722-dphy0-oscillator@0 {
		compatible = "fixed-clock";
		#clock-cells = <1>;
		clock-frequency = <25000000>;
		clock-output-names = "max96722-dphy0-osc0";
	};

	max96722_dphy0_vcc1v2: max96722-dphy0-vcc1v2 {
		compatible = "regulator-fixed";
		regulator-name = "max96722_dphy0_vcc1v2";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		startup-delay-us = <850>;
		vin-supply = <&vcc5v0_sys>;
	};

	max96722_dphy0_vcc1v8: max96722-dphy0-vcc1v8 {
		compatible = "regulator-fixed";
		regulator-name = "max96722_dphy0_vcc1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		startup-delay-us = <200>;
		vin-supply = <&vcc_3v3_s3>;
	};

	max96722_dphy0_pwdn_regulator: max96722-dphy0-pwdn-regulator {
		compatible = "regulator-fixed";
		regulator-name = "max96722_dphy0_pwdn";
		gpio = <&gpio1 RK_PB2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96722_dphy0_pwdn>;
		enable-active-high;
		startup-delay-us = <10000>;
		off-on-delay-us = <5000>;
		vin-supply = <&max96722_dphy0_vcc1v8>;
	};

	max96722_dphy0_poc_regulator: max96722-dphy0-poc-regulator {
		compatible = "regulator-fixed";
		regulator-name = "max96722_dphy0_poc";
		gpio = <&gpio3 RK_PB0 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		startup-delay-us = <10000>;
		off-on-delay-us = <5000>;
		vin-supply = <&vcc12v_dcin>;
	};
};

&csi2_dphy0_hw {
	status = "okay";
};

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy0_in_max96722: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&max96722_dphy0_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_input>;
			};
		};
	};
};

&i2c7 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c7m3_xfer>;

	max96722_dphy0: max96722@29 {
		compatible = "maxim4c,max96722";
		status = "okay";
		reg = <0x29>;
		clock-names = "xvclk";
		clocks = <&max96722_dphy0_osc0 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96722_dphy0_errb>, <&max96722_dphy0_lock>;
		power-domains = <&power RK3588_PD_VI>;
		rockchip,grf = <&sys_grf>;
		vcc1v2-supply = <&max96722_dphy0_vcc1v2>;
		vcc1v8-supply = <&max96722_dphy0_vcc1v8>;
		pwdn-supply = <&max96722_dphy0_pwdn_regulator>;
		lock-gpios = <&gpio3 RK_PB7 GPIO_ACTIVE_HIGH>;

		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";

		port {
			max96722_dphy0_out: endpoint {
				remote-endpoint = <&mipi_dphy0_in_max96722>;
				data-lanes = <1 2 3 4>;
			};
		};

		/* support mode config start */
		support-mode-config {
			status = "okay";

			bus-format = <MEDIA_BUS_FMT_UYVY8_2X8>;
			sensor-width = <1280>;
			sensor-height = <800>;
			max-fps-numerator = <10000>;
			max-fps-denominator = <300000>;
			bpp = <16>;
			link-freq-idx = <20>;
		};
		/* support mode config end */

		/* serdes local device start */
		serdes-local-device {
			status = "okay";

			/* GMSL LINK config start */
			gmsl-links {
				status = "okay";

				link-vdd-ldo1-en = <1>;
				link-vdd-ldo2-en = <1>;

				// Link A: link-id = 0
				gmsl-link-config-0 {
					status = "okay";
					link-id = <0>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96722_dphy0_cam0>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							14 D1 03 00 00 // VGAHiGain
							14 45 00 00 00 // Disable SSC
							0B 06 ef 00 00 // HIM on
							0B 07 84 00 00 // Enable HVEN and DBL
							0B 0F 01 00 00 // Disable processing DE signals
						];
					};
				};

				// Link B: link-id = 1
				gmsl-link-config-1 {
					status = "okay";
					link-id = <1>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96722_dphy0_cam1>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							15 D1 03 00 00 // VGAHiGain
							15 45 00 00 00 // Disable SSC
							0C 06 ef 00 00 // HIM on
							0C 07 84 00 00 // Enable HVEN and DBL
							0C 0F 01 00 00 // Disable processing DE signals
						];
					};
				};

				// Link C: link-id = 2
				gmsl-link-config-2 {
					status = "okay";
					link-id = <2>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96722_dphy0_cam2>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							16 D1 03 00 00 // VGAHiGain
							16 45 00 00 00 // Disable SSC
							0D 06 ef 00 00 // HIM on
							0D 07 84 00 00 // Enable HVEN and DBL
							0D 0F 01 00 00 // Disable processing DE signals
						];
					};
				};

				// Link D: link-id = 3
				gmsl-link-config-3 {
					status = "okay";
					link-id = <3>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96722_dphy0_cam3>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							17 D1 03 00 00 // VGAHiGain
							17 45 00 00 00 // Disable SSC
							0E 06 ef 00 00 // HIM on
							0E 07 84 00 00 // Enable HVEN and DBL
							0E 0F 01 00 00 // Disable processing DE signals
						];
					};
				};
			};
			/* GMSL LINK config end */

			/* VIDEO PIPE config start */
			video-pipes {
				status = "okay";

				// Video Pipe 0
				video-pipe-config-0 {
					status = "okay";
					pipe-id = <0>; // Video Pipe ID: 0/1/2/3/4/5/6/7

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <0>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 0 to Controller 1
							09 0B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 2D 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 0D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 0E 1e 00 00 // DST0 VC = 0, DT = YUV422 8bit
							09 0F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 10 00 00 00 // DST1 VC = 0, DT = Frame Start
							09 11 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 12 01 00 00 // DST2 VC = 0, DT = Frame End
						];
					};
				};

				// Video Pipe 1
				video-pipe-config-1 {
					status = "okay";
					pipe-id = <1>; // Video Pipe 1: pipe-id = 1

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <1>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 1 to Controller 1
							09 4B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 6D 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 4D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 4E 5e 00 00 // DST0 VC = 1, DT = YUV422 8bit
							09 4F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 50 40 00 00 // DST1 VC = 1, DT = Frame Start
							09 51 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 52 41 00 00 // DST2 VC = 1, DT = Frame End
						];
					};
				};

				// Video Pipe 2
				video-pipe-config-2 {
					status = "okay";
					pipe-id = <2>; // Video Pipe ID: 0/1/2/3/4/5/6/7

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <2>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 2 to Controller 1
							09 8B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 AD 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 8D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 8E 9e 00 00 // DST0 VC = 2, DT = YUV422 8bit
							09 8F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 90 80 00 00 // DST1 VC = 2, DT = Frame Start
							09 91 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 92 81 00 00 // DST2 VC = 2, DT = Frame End
						];
					};
				};

				// Video Pipe 3
				video-pipe-config-3 {
					status = "okay";
					pipe-id = <3>; // Video Pipe ID: 0/1/2/3/4/5/6/7

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <3>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 3 to Controller 1
							09 CB 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 ED 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 CD 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 CE de 00 00 // DST0 VC = 3, DT = YUV422 8bit
							09 CF 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 D0 c0 00 00 // DST1 VC = 3, DT = Frame Start
							09 D1 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 D2 c1 00 00 // DST2 VC = 3, DT = Frame End
						];
					};
				};

				// Software override for parallel mode
				parallel-mode-config {
					status = "okay";

					parallel-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Enable software override for all pipes since GMSL1 data is parallel mode, bpp=8, dt=0x1e(yuv-8)
							04 1A f0 00 00 // pipe 0/1/2/3: Enable YUV8-/10-bit mux mode
							04 0B 40 00 00 // pipe 0 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 0C 00 00 00 // pipe 0 and 1 VC software override: 0x00
							04 0D 00 00 00 // pipe 2 and 3 VC software override: 0x00
							04 0E 5e 00 00 // pipe 0 DT=0x1E: YUV422 8-bit
							04 0F 7e 00 00 // pipe 1 DT=0x1E: YUV422 8-bit
							04 10 7a 00 00 // pipe 2 DT=0x1E, pipe 3 DT=0x1E: YUV422 8-bit
							04 11 48 00 00 // pipe 1 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 12 20 00 00 // pipe 2 bpp=0x08, pipe 3 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 15 c0 c0 00 // pipe 0/1 enable software overide
							04 18 c0 c0 00 // pipe 2/3 enable software overide
						];
					};
				};
			};
			/* VIDEO PIPE config end */

			/* MIPI TXPHY config start */
			mipi-txphys {
				status = "okay";

				phy-mode = <0>;
				phy-force-clock-out = <1>;
				phy-force-clk0-en = <1>;
				phy-force-clk3-en = <0>;

				// MIPI TXPHY A: phy-id = 0
				mipi-txphy-config-0 {
					status = "okay";
					phy-id = <0>; // MIPI TXPHY ID: 0/1/2/3

					phy-type = <0>;
					auto-deskew = <0x80>;
					data-lane-num = <4>;
					data-lane-map = <0x4>;
					vc-ext-en = <0>;
				};

				// MIPI TXPHY B: phy-id = 1
				mipi-txphy-config-1 {
					status = "okay";
					phy-id = <1>; // MIPI TXPHY ID: 0/1/2/3

					phy-type = <0>;
					auto-deskew = <0x80>;
					data-lane-num = <4>;
					data-lane-map = <0xe>;
					vc-ext-en = <0>;
				};
			};
			/* MIPI TXPHY config end */

			/* local device extra init sequence */
			extra-init-sequence {
				status = "disabled";

				seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
				reg-addr-len = <2>; // 1: 8bits, 2: 16bits
				reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

				// reg_addr reg_val val_mask delay
				init-sequence = [
					// common init sequence such as fsync / gpio and so on
				];
			};
		};
		/* serdes local device end */

		/* i2c-mux start */
		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;

				// Note: Serializer node defined before camera node
				max96722_dphy0_ser0: max96715@41 {
					compatible = "maxim,ser,max96715";
					reg = <0x41>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96722_dphy0_cam0: ox01f10@31 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x31>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96722_dphy0_ser0>; // remote serializer

					poc-supply = <&max96722_dphy0_poc_regulator>;

					rockchip,camera-module-index = <0>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96722_dphy0_cam0_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf */
							//remote-endpoint = <&mipi_lvds_sditf_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};

			i2c@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;

				// Note: Serializer node defined before camera node
				max96722_dphy0_ser1: max96715@42 {
					compatible = "maxim,ser,max96715";
					reg = <0x42>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96722_dphy0_cam1: ox01f10@32 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x32>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96722_dphy0_ser1>; // remote serializer

					poc-supply = <&max96722_dphy0_poc_regulator>;

					rockchip,camera-module-index = <0>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96722_dphy0_cam1_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf_vir1 */
							//remote-endpoint = <&mipi_lvds_sditf_vir1_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};

			i2c@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <2>;

				// Note: Serializer node defined before camera node
				max96722_dphy0_ser2: max96715@43 {
					compatible = "maxim,ser,max96715";
					reg = <0x43>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96722_dphy0_cam2: ox01f10@33 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x33>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96722_dphy0_ser2>; // remote serializer

					poc-supply = <&max96722_dphy0_poc_regulator>;

					rockchip,camera-module-index = <2>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96722_dphy0_cam2_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf_vir2 */
							//remote-endpoint = <&mipi_lvds_sditf_vir2_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};

			i2c@3 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <3>;

				// Note: Serializer node defined before camera node
				max96722_dphy0_ser3: max96715@44 {
					compatible = "maxim,ser,max96715";
					reg = <0x44>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96722_dphy0_cam3: ox01f10@34 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x34>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96722_dphy0_ser3>; // remote serializer

					poc-supply = <&max96722_dphy0_poc_regulator>;

					rockchip,camera-module-index = <3>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96722_dphy0_cam3_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf_vir3 */
							//remote-endpoint = <&mipi_lvds_sditf_vir3_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};
		};
		/* i2c-mux end */
	};
};

&mipi2_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi2_in>;
			};
		};
	};
};

&rkcif_mipi_lvds2 {
	status = "okay";
	/* parameters for do cif reset detecting:
	 * index0: monitor mode,
		   0 for idle,
		   1 for continue,
		   2 for trigger,
		   3 for hotplug (for nextchip)
	 * index1: the frame id to start timer,
		   min is 2
	 * index2: frame num of monitoring cycle
	 * index3: err time for keep monitoring
		   after finding out err (ms)
	 * index4: csi2 err reference val for resetting
	 */
	rockchip,cif-monitor = <3 2 1 1000 5>;

	port {
		cif_mipi2_in: endpoint {
			remote-endpoint = <&mipi2_csi2_output>;
		};
	};
};

&rkcif {
	status = "okay";
	rockchip,android-usb-camerahal-enable;
};

&rkcif_mmu {
	status = "okay";
};

&pinctrl {
	max96722-dphy0 {
		max96722_dphy0_pwdn: max96722-dphy0-pwdn {
			rockchip,pins = <1 RK_PB2 RK_FUNC_GPIO &pcfg_output_low>;
		};

		max96722_dphy0_errb: max96722-dphy0-errb {
			rockchip,pins = <3 RK_PD1 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};

		max96722_dphy0_lock: max96722-dphy0-lock {
			rockchip,pins = <3 RK_PB7 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};
	};
};
