// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

/dts-v1/;

#include "rk3588-vehicle-s66-v10.dtsi"
#include "rk3588-vehicle-adsp-audio-s66.dtsi"
#include "rk3588-vehicle-maxim-serdes-display-s66.dtsi"
#include "rk3588-vehicle-maxim-cameras-s66.dtsi"
#include "rk3588-android.dtsi"

/ {
	model = "Rockchip RK3588 VEHICLE S66 Board V10";
	compatible = "rockchip,rk3588-vehicle-s66-v10", "rockchip,rk3588";
};

&rockchip_suspend {
	rockchip,sleep-mode-config = <
		(0
		| RKPM_SLP_ARMOFF_DDRPD
		| RKPM_SLP_PMU_PMUALIVE_32K
		| RKPM_SLP_PMU_DIS_OSC
		| RKPM_SLP_32K_EXT
		)
	>;
	rockchip,wakeup-config = <
		(0
		| RKPM_GPIO_WKUP_EN
		)
	>;
	status = "okay";
};

&vdd_log_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <800000>;
	};
};

&vcc_3v3_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <3300000>;
	};
};

&vcc_1v8_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <1800000>;
	};
};

&vccio_sd_s0 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};

&vdd_0v75_hdmi_edp_s0 {
	regulator-min-microvolt = <837500>;
	regulator-max-microvolt = <837500>;
};

&vdd_cpu_big1_mem_s0 {
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};

&vdd_cpu_big0_mem_s0 {
	regulator-min-microvolt = <1000000>;
	regulator-max-microvolt = <1000000>;
};

&vdd_cpu_lit_mem_s0 {
	regulator-min-microvolt = <1200000>;
	regulator-max-microvolt = <1200000>;
};

&vdd_gpu_mem_s0 {
	regulator-min-microvolt = <1800000>;
	regulator-max-microvolt = <1800000>;
	regulator-state-mem {
		regulator-on-in-suspend;
	};
};

&pinctrl {
	pinctrl-names = "init";
	pinctrl-0 = <&max96712_dphy0_pwdn
			&max96712_dphy0_errb
			&max96712_dphy0_lock
			&max96722_dphy3_pwdn
			&max96722_dphy3_errb
			&max96722_dphy3_lock>;
};
