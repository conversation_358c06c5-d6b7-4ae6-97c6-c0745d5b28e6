// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */
#include <dt-bindings/display/media-bus-format.h>

/ {
	max96712_dphy0_osc0: max96712-dphy0-oscillator@0 {
		compatible = "fixed-clock";
		#clock-cells = <1>;
		clock-frequency = <25000000>;
		clock-output-names = "max96712-dphy0-osc0";
	};

	max96722_dphy3_osc0: max96722-dphy3-oscillator@0 {
		compatible = "fixed-clock";
		#clock-cells = <1>;
		clock-frequency = <25000000>;
		clock-output-names = "max96722-dphy3-osc0";
	};

	maxim_dphy_vcc1v2: maxim-dphy-vcc1v2 {
		compatible = "regulator-fixed";
		regulator-name = "maxim_dphy_vcc1v2";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		startup-delay-us = <850>;
		vin-supply = <&vcc5v0_sys>;
	};

	maxim_dphy_vcc1v8: maxim-dphy-vcc1v8 {
		compatible = "regulator-fixed";
		regulator-name = "maxim_dphy_vcc1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		startup-delay-us = <200>;
		vin-supply = <&vcc_3v3_s3>;
	};

	max96712_dphy0_pwdn_regulator: max96712-dphy0-pwdn-regulator {
		compatible = "regulator-fixed";
		regulator-name = "max96712_dphy0_pwdn";
		gpio = <&gpio1 RK_PC4 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96712_dphy0_pwdn>;
		enable-active-high;
		startup-delay-us = <10000>;
		off-on-delay-us = <5000>;
	};

	max96722_dphy3_pwdn_regulator: max96722-dphy3-pwdn-regulator {
		compatible = "regulator-fixed";
		regulator-name = "max96722_dphy3_pwdn";
		gpio = <&gpio1 RK_PC7 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96722_dphy3_pwdn>;
		enable-active-high;
		startup-delay-us = <10000>;
		off-on-delay-us = <5000>;
	};
};

/**
 * ============================================================================
 * Inno DPHY0: full mode
 * ============================================================================
 */
&csi2_dphy0_hw {
	status = "okay";
};

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy0_in_max96712: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&max96712_dphy0_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi2_csi2_input>;
			};
		};
	};
};

&mipi2_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi2_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi2_in>;
			};
		};
	};
};

&rkcif_mipi_lvds2 {
	status = "okay";
	/* parameters for do cif reset detecting:
	 * index0: monitor mode,
		   0 for idle,
		   1 for continue,
		   2 for trigger,
		   3 for hotplug (for nextchip)
	 * index1: the frame id to start timer,
		   min is 2
	 * index2: frame num of monitoring cycle
	 * index3: err time for keep monitoring
		   after finding out err (ms)
	 * index4: csi2 err reference val for resetting
	 */
	rockchip,cif-monitor = <3 2 1 1000 5>;

	port {
		cif_mipi2_in: endpoint {
			remote-endpoint = <&mipi2_csi2_output>;
		};
	};
};

/**
 * ============================================================================
 * Inno DPHY1: full mode
 * ============================================================================
 */
&csi2_dphy1_hw {
	status = "okay";
};

&csi2_dphy3 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy3_in_max96722: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&max96722_dphy3_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy3_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi4_csi2_input>;
			};
		};
	};
};

&mipi4_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi4_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy3_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi4_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi4_in>;
			};
		};
	};
};

&rkcif_mipi_lvds4 {
	status = "okay";
	/* parameters for do cif reset detecting:
	 * index0: monitor mode,
		   0 for idle,
		   1 for continue,
		   2 for trigger,
		   3 for hotplug (for nextchip)
	 * index1: the frame id to start timer,
		   min is 2
	 * index2: frame num of monitoring cycle
	 * index3: err time for keep monitoring
		   after finding out err (ms)
	 * index4: csi2 err reference val for resetting
	 */
	rockchip,cif-monitor = <3 2 1 1000 5>;

	port {
		cif_mipi4_in: endpoint {
			remote-endpoint = <&mipi4_csi2_output>;
		};
	};
};

/**
 * =============================================================================
 * Common
 * =============================================================================
 */
&rkcif {
	status = "okay";
	rockchip,android-usb-camerahal-enable;
};

&rkcif_mmu {
	status = "okay";
};

&i2c2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2m4_xfer>;
	clock-frequency = <400000>;

	// AVM Camera x4
	max96712_dphy0: max96712@29 {
		compatible = "maxim4c,max96712";
		status = "okay";
		reg = <0x29>;
		clock-names = "xvclk";
		clocks = <&max96712_dphy0_osc0 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96712_dphy0_errb>, <&max96712_dphy0_lock>;
		power-domains = <&power RK3588_PD_VI>;
		rockchip,grf = <&sys_grf>;
		vcc1v2-supply = <&maxim_dphy_vcc1v2>;
		vcc1v8-supply = <&maxim_dphy_vcc1v8>;
		pwdn-supply = <&max96712_dphy0_pwdn_regulator>;
		lock-gpios = <&gpio1 RK_PC6 GPIO_ACTIVE_HIGH>;

		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";

		port {
			max96712_dphy0_out: endpoint {
				remote-endpoint = <&mipi_dphy0_in_max96712>;
				data-lanes = <1 2 3 4>;
			};
		};

		/* support mode config start */
		support-mode-config {
			status = "okay";

			bus-format = <MEDIA_BUS_FMT_UYVY8_2X8>;
			sensor-width = <1280>;
			sensor-height = <800>;
			max-fps-numerator = <10000>;
			max-fps-denominator = <300000>;
			bpp = <16>;
			link-freq-idx = <20>;
			vc-array = <0x10 0x20 0x40 0x80>; // VC0~3: bit4~7
		};
		/* support mode config end */

		/* serdes local device start */
		serdes-local-device {
			status = "okay";

			/* GMSL LINK config start */
			gmsl-links {
				status = "okay";

				link-vdd-ldo1-en = <1>;
				link-vdd-ldo2-en = <1>;

				// Link A: link-id = 0
				gmsl-link-config-0 {
					status = "okay";
					link-id = <0>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96712_dphy0_cam0>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							14 D1 03 00 00 // VGAHiGain
							14 45 00 00 00 // Disable SSC
							0B 06 ef 00 00 // HIM on
							0B 07 84 00 00 // Enable HVEN and DBL
							0B 0F 01 00 00 // Disable processing DE signals
						];
					};
				};

				// Link B: link-id = 1
				gmsl-link-config-1 {
					status = "okay";
					link-id = <1>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96712_dphy0_cam1>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							15 D1 03 00 00 // VGAHiGain
							15 45 00 00 00 // Disable SSC
							0C 06 ef 00 00 // HIM on
							0C 07 84 00 00 // Enable HVEN and DBL
							0C 0F 01 00 00 // Disable processing DE signals
						];
					};
				};

				// Link C: link-id = 2
				gmsl-link-config-2 {
					status = "okay";
					link-id = <2>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96712_dphy0_cam2>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							16 D1 03 00 00 // VGAHiGain
							16 45 00 00 00 // Disable SSC
							0D 06 ef 00 00 // HIM on
							0D 07 84 00 00 // Enable HVEN and DBL
							0D 0F 01 00 00 // Disable processing DE signals
						];
					};
				};

				// Link D: link-id = 3
				gmsl-link-config-3 {
					status = "okay";
					link-id = <3>; // Link ID: 0/1/2/3

					link-type = <0>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96712_dphy0_cam3>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							17 D1 03 00 00 // VGAHiGain
							17 45 00 00 00 // Disable SSC
							0E 06 ef 00 00 // HIM on
							0E 07 84 00 00 // Enable HVEN and DBL
							0E 0F 01 00 00 // Disable processing DE signals
						];
					};
				};
			};
			/* GMSL LINK config end */

			/* VIDEO PIPE config start */
			video-pipes {
				status = "okay";

				// Video Pipe 0
				video-pipe-config-0 {
					status = "okay";
					pipe-id = <0>; // Video Pipe ID: 0/1/2/3/4/5/6/7

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <0>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 0 to Controller 1
							09 0B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 2D 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 0D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 0E 1e 00 00 // DST0 VC = 0, DT = YUV422 8bit
							09 0F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 10 00 00 00 // DST1 VC = 0, DT = Frame Start
							09 11 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 12 01 00 00 // DST2 VC = 0, DT = Frame End
						];
					};
				};

				// Video Pipe 1
				video-pipe-config-1 {
					status = "okay";
					pipe-id = <1>; // Video Pipe 1: pipe-id = 1

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <1>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 1 to Controller 1
							09 4B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 6D 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 4D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 4E 5e 00 00 // DST0 VC = 1, DT = YUV422 8bit
							09 4F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 50 40 00 00 // DST1 VC = 1, DT = Frame Start
							09 51 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 52 41 00 00 // DST2 VC = 1, DT = Frame End
						];
					};
				};

				// Video Pipe 2
				video-pipe-config-2 {
					status = "okay";
					pipe-id = <2>; // Video Pipe ID: 0/1/2/3/4/5/6/7

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <2>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 2 to Controller 1
							09 8B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 AD 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 8D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 8E 9e 00 00 // DST0 VC = 2, DT = YUV422 8bit
							09 8F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 90 80 00 00 // DST1 VC = 2, DT = Frame Start
							09 91 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 92 81 00 00 // DST2 VC = 2, DT = Frame End
						];
					};
				};

				// Video Pipe 3
				video-pipe-config-3 {
					status = "okay";
					pipe-id = <3>; // Video Pipe ID: 0/1/2/3/4/5/6/7

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <3>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 3 to Controller 1
							09 CB 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 ED 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 CD 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 CE de 00 00 // DST0 VC = 3, DT = YUV422 8bit
							09 CF 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 D0 c0 00 00 // DST1 VC = 3, DT = Frame Start
							09 D1 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 D2 c1 00 00 // DST2 VC = 3, DT = Frame End
						];
					};
				};

				// Software override for parallel mode
				parallel-mode-config {
					status = "okay";

					parallel-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Enable software override for all pipes since GMSL1 data is parallel mode, bpp=8, dt=0x1e(yuv-8)
							04 1A f0 00 00 // pipe 0/1/2/3: Enable YUV8-/10-bit mux mode
							04 0B 40 00 00 // pipe 0 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 0C 00 00 00 // pipe 0 and 1 VC software override: 0x00
							04 0D 00 00 00 // pipe 2 and 3 VC software override: 0x00
							04 0E 5e 00 00 // pipe 0 DT=0x1E: YUV422 8-bit
							04 0F 7e 00 00 // pipe 1 DT=0x1E: YUV422 8-bit
							04 10 7a 00 00 // pipe 2 DT=0x1E, pipe 3 DT=0x1E: YUV422 8-bit
							04 11 48 00 00 // pipe 1 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 12 20 00 00 // pipe 2 bpp=0x08, pipe 3 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 15 c0 c0 00 // pipe 0/1 enable software overide
							04 18 c0 c0 00 // pipe 2/3 enable software overide
						];
					};
				};
			};
			/* VIDEO PIPE config end */

			/* MIPI TXPHY config start */
			mipi-txphys {
				status = "okay";

				phy-mode = <0>;
				phy-force-clock-out = <1>;
				phy-force-clk0-en = <0>;
				phy-force-clk3-en = <0>;

				// MIPI TXPHY A: phy-id = 0
				mipi-txphy-config-0 {
					status = "okay";
					phy-id = <0>; // MIPI TXPHY ID: 0/1/2/3

					phy-type = <0>;
					auto-deskew = <0x80>;
					data-lane-num = <4>;
					data-lane-map = <0x4>;
					vc-ext-en = <0>;
				};

				// MIPI TXPHY B: phy-id = 1
				mipi-txphy-config-1 {
					status = "okay";
					phy-id = <1>; // MIPI TXPHY ID: 0/1/2/3

					phy-type = <0>;
					auto-deskew = <0x80>;
					data-lane-num = <4>;
					data-lane-map = <0xe>;
					vc-ext-en = <0>;
				};
			};
			/* MIPI TXPHY config end */

			/* local device extra init sequence */
			extra-init-sequence {
				status = "disabled";

				seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
				reg-addr-len = <2>; // 1: 8bits, 2: 16bits
				reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

				// reg_addr reg_val val_mask delay
				init-sequence = [
					// common init sequence such as fsync / gpio and so on
				];
			};
		};
		/* serdes local device end */

		/* i2c-mux start */
		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;

				// Note: Serializer node defined before camera node
				max96712_dphy0_ser0: max96715@41 {
					compatible = "maxim,ser,max96715";
					reg = <0x41>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96712_dphy0_cam0: ox01f10@31 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x31>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96712_dphy0_ser0>; // remote serializer

					//poc-supply = <&max96712_dphy0_poc_regulator>;

					rockchip,camera-module-index = <0>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96712_dphy0_cam0_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf */
							//remote-endpoint = <&mipi_lvds_sditf_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};

			i2c@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;

				// Note: Serializer node defined before camera node
				max96712_dphy0_ser1: max96715@41 {
					compatible = "maxim,ser,max96715";
					reg = <0x41>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96712_dphy0_cam1: ox01f10@32 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x32>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96712_dphy0_ser1>; // remote serializer

					//poc-supply = <&max96712_dphy0_poc_regulator>;

					rockchip,camera-module-index = <0>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96712_dphy0_cam1_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf_vir1 */
							//remote-endpoint = <&mipi_lvds_sditf_vir1_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};

			i2c@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <2>;

				// Note: Serializer node defined before camera node
				max96712_dphy0_ser2: max96715@43 {
					compatible = "maxim,ser,max96715";
					reg = <0x43>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96712_dphy0_cam2: ox01f10@33 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x33>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96712_dphy0_ser2>; // remote serializer

					//poc-supply = <&max96712_dphy0_poc_regulator>;

					rockchip,camera-module-index = <2>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96712_dphy0_cam2_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf_vir2 */
							//remote-endpoint = <&mipi_lvds_sditf_vir2_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};

			i2c@3 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <3>;

				// Note: Serializer node defined before camera node
				max96712_dphy0_ser3: max96715@44 {
					compatible = "maxim,ser,max96715";
					reg = <0x44>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <4>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <1>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							07 84 00 00
							67 c4 00 00
							0F bf 00 00
							3F 08 00 00
							40 2d 00 00
							20 10 00 00
							21 11 00 00
							22 12 00 00
							23 13 00 00
							24 14 00 00
							25 15 00 00
							26 16 00 00
							27 17 00 00
							30 00 00 00
							31 01 00 00
							32 02 00 00
							33 03 00 00
							34 04 00 00
							35 05 00 00
							36 06 00 00
							37 07 00 00
						];
					};
				};

				max96712_dphy0_cam3: ox01f10@34 {
					compatible = "maxim,ovti,ox01f10";
					reg = <0x34>;

					cam-i2c-addr-def = <0x36>;

					cam-remote-ser = <&max96712_dphy0_ser3>; // remote serializer

					//poc-supply = <&max96712_dphy0_poc_regulator>;

					rockchip,camera-module-index = <3>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96712_dphy0_cam3_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf_vir3 */
							//remote-endpoint = <&mipi_lvds_sditf_vir3_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};
		};
		/* i2c-mux end */
	};

	// DMS Camera x1 + OMS Camera x3
	max96722_dphy3: max96722@6b {
		compatible = "maxim4c,max96722";
		status = "okay";
		reg = <0x6b>;
		clock-names = "xvclk";
		clocks = <&max96722_dphy3_osc0 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96722_dphy3_pwdn>, <&max96722_dphy3_errb>, <&max96722_dphy3_lock>;
		power-domains = <&power RK3588_PD_VI>;
		rockchip,grf = <&sys_grf>;
		vcc1v2-supply = <&maxim_dphy_vcc1v2>;
		vcc1v8-supply = <&maxim_dphy_vcc1v8>;
		pwdn-supply = <&max96722_dphy3_pwdn_regulator>;
		lock-gpios = <&gpio1 RK_PD5 GPIO_ACTIVE_HIGH>;

		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";

		port {
			max96722_dphy3_out: endpoint {
				remote-endpoint = <&mipi_dphy3_in_max96722>;
				data-lanes = <1 2 3 4>;
			};
		};

		/* support mode config start */
		support-mode-config {
			status = "okay";

			bus-format = <MEDIA_BUS_FMT_UYVY8_2X8>;
			sensor-width = <1600>;
			sensor-height = <1300>;
			max-fps-numerator = <10000>;
			max-fps-denominator = <300000>;
			bpp = <16>;
			link-freq-idx = <20>;
			vc-array = <0x10 0x20 0x40 0x80>; // VC0~3: bit4~7
		};
		/* support mode config end */

		/* serdes local device start */
		serdes-local-device {
			status = "okay";

			/* GMSL LINK config start */
			gmsl-links {
				status = "okay";

				link-vdd-ldo1-en = <1>;
				link-vdd-ldo2-en = <1>;

				// Link A: link-id = 0
				gmsl-link-config-0 {
					status = "okay";
					link-id = <0>; // Link ID: 0/1/2/3

					link-type = <1>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96722_dphy3_cam0>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							14 D1 03 00 00 // VGAHiGain
							14 45 00 00 00 // Disable SSC
						];
					};
				};

				// Link B: link-id = 1
				gmsl-link-config-1 {
					status = "okay";
					link-id = <1>; // Link ID: 0/1/2/3

					link-type = <1>;
					link-rx-rate = <0>;
					link-tx-rate = <0>;

					link-remote-cam = <&max96722_dphy3_cam1>; // remote camera

					link-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							15 D1 03 00 00 // VGAHiGain
							15 45 00 00 00 // Disable SSC
						];
					};
				};
			};
			/* GMSL LINK config end */

			/* VIDEO PIPE config start */
			video-pipes {
				status = "okay";

				// Video Pipe 0
				video-pipe-config-0 {
					status = "okay";
					pipe-id = <0>; // Video Pipe ID: 0/1/2/3/4/5/6/7

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <0>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 0 to Controller 1
							09 0B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 2D 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 0D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 0E 1e 00 00 // DST0 VC = 0, DT = YUV422 8bit
							09 0F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 10 00 00 00 // DST1 VC = 0, DT = Frame Start
							09 11 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 12 01 00 00 // DST2 VC = 0, DT = Frame End
							// pipe Cross
							01 D9 59 00 00 // pipe 0: Inverts Cross VS
						];
					};
				};

				// Video Pipe 1
				video-pipe-config-1 {
					status = "okay";
					pipe-id = <1>; // Video Pipe 1: pipe-id = 1

					pipe-idx = <0>; // Video Pipe X/Y/Z/U: 0/1/2/3
					link-idx = <1>; // Link A/B/C/D: 0/1/2/3

					pipe-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Send YUV422, FS, and FE from Video Pipe 1 to Controller 1
							09 4B 07 00 00 // Enable 0/1/2 SRC/DST Mappings
							09 6D 15 00 00 // SRC/DST 0/1/2 -> CSI2 Controller 1;
							// For the following MSB 2 bits = VC, LSB 6 bits = DT
							09 4D 1e 00 00 // SRC0 VC = 0, DT = YUV422 8bit
							09 4E 5e 00 00 // DST0 VC = 1, DT = YUV422 8bit
							09 4F 00 00 00 // SRC1 VC = 0, DT = Frame Start
							09 50 40 00 00 // DST1 VC = 1, DT = Frame Start
							09 51 01 00 00 // SRC2 VC = 0, DT = Frame End
							09 52 41 00 00 // DST2 VC = 1, DT = Frame End
							// pipe Cross
							01 F9 59 00 00 // pipe 1: Inverts Cross VS
						];
					};
				};

				// Software override for parallel mode
				parallel-mode-config {
					status = "okay";

					parallel-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							// Enable software override for all pipes since GMSL1 data is parallel mode, bpp=8, dt=0x1e(yuv-8)
							04 1A f0 00 00 // pipe 0/1/2/3: Enable YUV8-/10-bit mux mode
							04 0B 40 00 00 // pipe 0 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 0C 00 00 00 // pipe 0 and 1 VC software override: 0x00
							04 0D 00 00 00 // pipe 2 and 3 VC software override: 0x00
							04 0E 5e 00 00 // pipe 0 DT=0x1E: YUV422 8-bit
							04 0F 7e 00 00 // pipe 1 DT=0x1E: YUV422 8-bit
							04 10 7a 00 00 // pipe 2 DT=0x1E, pipe 3 DT=0x1E: YUV422 8-bit
							04 11 48 00 00 // pipe 1 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 12 20 00 00 // pipe 2 bpp=0x08, pipe 3 bpp=0x08: Datatypes = 0x2A, 0x10-12, 0x31-37
							04 15 c0 c0 00 // pipe 0/1 enable software overide
							04 18 c0 c0 00 // pipe 2/3 enable software overide
						];
					};
				};
			};
			/* VIDEO PIPE config end */

			/* MIPI TXPHY config start */
			mipi-txphys {
				status = "okay";

				phy-mode = <0>;
				phy-force-clock-out = <1>;
				phy-force-clk0-en = <0>;
				phy-force-clk3-en = <0>;

				// MIPI TXPHY A: phy-id = 0
				mipi-txphy-config-0 {
					status = "okay";
					phy-id = <0>; // MIPI TXPHY ID: 0/1/2/3

					phy-type = <0>;
					auto-deskew = <0x80>;
					data-lane-num = <4>;
					data-lane-map = <0x4>;
					vc-ext-en = <0>;
				};

				// MIPI TXPHY B: phy-id = 1
				mipi-txphy-config-1 {
					status = "okay";
					phy-id = <1>; // MIPI TXPHY ID: 0/1/2/3

					phy-type = <0>;
					auto-deskew = <0x80>;
					data-lane-num = <4>;
					data-lane-map = <0xe>;
					vc-ext-en = <0>;
				};
			};
			/* MIPI TXPHY config end */

			/* local device extra init sequence */
			extra-init-sequence {
				status = "disabled";

				seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
				reg-addr-len = <2>; // 1: 8bits, 2: 16bits
				reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

				// reg_addr reg_val val_mask delay
				init-sequence = [
					// common init sequence such as fsync / gpio and so on
				];
			};
		};
		/* serdes local device end */

		/* i2c-mux start */
		i2c-mux {
			#address-cells = <1>;
			#size-cells = <0>;

			i2c@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;

				// Note: Serializer node defined before camera node
				max96722_dphy3_ser0: max9295@45 {
					compatible = "maxim,ser,max9295";
					reg = <0x45>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							00 01 04 00 00 // RX_RATE: 187.5Mbps, TX_RATE: 3Gbps
							00 11 03 00 00 // Coax Drive
							02 D6 03 00 00 // MFP8: GPIO_OUT_DIS = 1, GPIO_TX_EN = 1
							03 F0 51 00 00 // RCLK: 27MHz/24MHz (ALT),Enable reference-generation PLL, Enable pre-defined clock setting for reference-generation PLL
							00 03 07 00 00 // RCLK: Enable RCLK output from altermative MFP pin, RCLKOUT clock select reference PLL
							00 06 b1 00 00 // RCLK: GMSL2, Enable RCLK output, i2c selected
							02 C1 10 00 00 // MFP1: GPIO_OUT pin output is driven to 1 when GPIO_RX_EN = 0
							02 C2 60 00 00 // MFP1: OUT_TYPE = 1: Push-pull, PULL_UPDN_SEL[1:0] = 0b01: Pullup
							00 07 07 00 00 // Enable Parallel video input, Parallel HS and VS Enable
							00 10 05 00 00 // AUTO_LINK = 0, LINK_CFG = 1: LinkA is selected, REG_ENABLE = 1: Regulator enabled
							00 12 14 00 00 // REG_MNL = 1: Enable LDO on/off state controlled by REG_ENABLE
							01 00 62 00 00 // Video X, Line CRC enabled, ENC_MODE = 2: HS, VS, DE encoding on, color bits sent only when DE is high
							01 01 50 00 00 // Video X, BPP = 0x10
							00 53 10 00 00 // Video X, TX_STR_SEL = 0: Stream ID = 0 for packets from this channel
							00 02 13 00 00 // Video transmit enable for Port X
						];
					};
				};

				max96722_dphy3_cam0: ov2311@35 {
					compatible = "maxim,ovti,ov2311";
					reg = <0x35>;

					cam-i2c-addr-def = <0x30>;

					cam-remote-ser = <&max96722_dphy3_ser0>; // remote serializer

					//poc-supply = <&max96722_dphy3_poc_regulator>;

					rockchip,camera-module-index = <0>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96722_dphy3_cam0_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf */
							//remote-endpoint = <&mipi_lvds_sditf_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};

			i2c@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;

				// Note: Serializer node defined before camera node
				max96722_dphy3_ser1: max9295@46 {
					compatible = "maxim,ser,max9295";
					reg = <0x46>;

					ser-i2c-addr-def = <0x40>;

					ser-init-sequence {
						seq-item-size = <5>; // reg-addr-len + reg-val-len * 2 + 1
						reg-addr-len = <2>; // 1: 8bits, 2: 16bits
						reg-val-len = <1>; // 1: 8bits, 2: 16bits, 3: 24bits

						// reg_addr reg_val val_mask delay
						init-sequence = [
							00 01 04 00 00 // RX_RATE: 187.5Mbps, TX_RATE: 3Gbps
							00 11 03 00 00 // Coax Drive
							02 D6 03 00 00 // MFP8: GPIO_OUT_DIS = 1, GPIO_TX_EN = 1
							03 F0 51 00 00 // RCLK: 27MHz/24MHz (ALT),Enable reference-generation PLL, Enable pre-defined clock setting for reference-generation PLL
							00 03 07 00 00 // RCLK: Enable RCLK output from altermative MFP pin, RCLKOUT clock select reference PLL
							00 06 b1 00 00 // RCLK: GMSL2, Enable RCLK output, i2c selected
							02 C1 10 00 00 // MFP1: GPIO_OUT pin output is driven to 1 when GPIO_RX_EN = 0
							02 C2 60 00 00 // MFP1: OUT_TYPE = 1: Push-pull, PULL_UPDN_SEL[1:0] = 0b01: Pullup
							00 07 07 00 00 // Enable Parallel video input, Parallel HS and VS Enable
							00 10 05 00 00 // AUTO_LINK = 0, LINK_CFG = 1: LinkA is selected, REG_ENABLE = 1: Regulator enabled
							00 12 14 00 00 // REG_MNL = 1: Enable LDO on/off state controlled by REG_ENABLE
							01 00 62 00 00 // Video X, Line CRC enabled, ENC_MODE = 2: HS, VS, DE encoding on, color bits sent only when DE is high
							01 01 50 00 00 // Video X, BPP = 0x10
							00 53 10 00 00 // Video X, TX_STR_SEL = 0: Stream ID = 0 for packets from this channel
							00 02 13 00 00 // Video transmit enable for Port X
						];
					};
				};

				max96722_dphy3_cam1: ov2312@36 {
					compatible = "maxim,ovti,ov2312";
					reg = <0x36>;

					cam-i2c-addr-def = <0x30>;

					cam-remote-ser = <&max96722_dphy3_ser1>; // remote serializer

					//poc-supply = <&max96722_dphy3_poc_regulator>;

					rockchip,camera-module-index = <1>;
					rockchip,camera-module-facing = "back";
					rockchip,camera-module-name = "default";
					rockchip,camera-module-lens-name = "default";

					/* port config start */
					port {
						max96722_dphy3_cam1_out: endpoint {
							/* remote endpoint: rkcif_mipi_lvds_sditf_vir1 */
							//remote-endpoint = <&mipi_lvds_sditf_vir1_in>;
							data-lanes = <1 2 3 4>;
						};
					};
					/* port config end */
				};
			};
		};
		/* i2c-mux end */

	};
};

&pinctrl {
	maxim-cameras {
		max96712_dphy0_pwdn: max96712-dphy0-pwdn {
			rockchip,pins = <1 RK_PC4 RK_FUNC_GPIO &pcfg_output_low>;
		};

		max96712_dphy0_errb: max96712-dphy0-errb {
			rockchip,pins = <1 RK_PD2 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};

		max96712_dphy0_lock: max96712-dphy0-lock {
			rockchip,pins = <1 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};

		max96722_dphy3_pwdn: max96722-dphy3-pwdn {
			rockchip,pins = <1 RK_PC7 RK_FUNC_GPIO &pcfg_output_low>;
		};

		max96722_dphy3_errb: max96722-dphy3-errb {
			rockchip,pins = <1 RK_PB1 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};

		max96722_dphy3_lock: max96722-dphy3-lock {
			rockchip,pins = <1 RK_PD5 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};
	};
};
