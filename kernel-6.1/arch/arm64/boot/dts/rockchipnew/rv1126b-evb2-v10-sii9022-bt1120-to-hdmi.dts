// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 */

/dts-v1/;

#include <dt-bindings/display/media-bus-format.h>
#include "rv1126b-evb2-v10.dts"

/ {
	model = "Rockchip RV1126B EVB2 V10 Board + RK EVB EXT DisplayBoard SII9022A BT1120toHDMI V10";
	compatible = "rockchip,rv1126b-evb2-v10-sii9022-bt1120-to-hdmi", "rockchip,rv1126b";
};

&i2c2 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&i2c2m1_pins>;
	status = "okay";

	sii9022: sii9022@39 {
		compatible = "sil,sii9022";
		reg = <0x39>;
		pinctrl-names = "default";
		pinctrl-0 = <&sii902x_hdmi>;
		interrupt-parent = <&gpio3>;
		interrupts = <RK_PB7 IRQ_TYPE_LEVEL_HIGH>;
		reset-gpio = <&gpio5 RK_PD6 GPIO_ACTIVE_LOW>;
		enable-gpio = <&gpio7 RK_PA7 GPIO_ACTIVE_HIGH>;
		/*
		 * MEDIA_BUS_FMT_YUYV8_1X16 for bt1120
		 * MEDIA_BUS_FMT_UYVY8_2X8  for bt656
		 */
		bus-format = <MEDIA_BUS_FMT_YUYV8_1X16>;
		status = "okay";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;

				sii9022_in_rgb: endpoint {
					remote-endpoint = <&rgb_out_sii9022>;
				};
			};
		};
	};
};

&pinctrl {
	sii902x {
		sii902x_hdmi: sii902x-hdmi {
			rockchip,pins = <3 RK_PB7 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&rgb {
	status = "okay";
	pinctrl-names = "default";
	/*
	 * bt1120_pins for bt1120
	 * bt656_m0_pins/bt656_m1_pins for bt656
	 */
	pinctrl-0 = <&bt1120_pins>;

	ports {
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			rgb_out_sii9022: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&sii9022_in_rgb>;
			};
		};
	};
};

&rgb_in_vop {
	status = "okay";
};

&route_rgb {
	status = "okay";
};
