// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

/dts-v1/;

#include "rk3588-vehicle-evb-v21.dtsi"
#include "rk3588-vehicle-evb-maxim-max96712-dphy3.dtsi"
#include "rk3588-vehicle-serdes-mfd-display-rohm.dtsi"
#include "rk3588-android.dtsi"

/ {
	model = "Rockchip RK3588 VEHICLE EVB V21 Board";
	compatible = "rockchip,rk3588-vehicle-evb-v21", "rockchip,rk3588";

	bt-sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "dsp_a";
		simple-audio-card,bitclock-inversion;
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,name = "rockchip,bt";
		simple-audio-card,cpu {
			sound-dai = <&i2s2_2ch>;
		};

		simple-audio-card,codec {
			sound-dai = <&bt_sco 1>;
		};
	};

	bt_sco: bt-sco {
		compatible = "delta,dfbmcs320";
		#sound-dai-cells = <1>;
		status = "okay";
	};

	vehicle_dummy: vehicle_dummy {
		status = "okay";
		compatible = "rockchip,vehicle-dummy-gpio";
		reverse-gpio = <&gpio0 RK_PA4 GPIO_ACTIVE_HIGH>;
		park-gpio = <&gpio0 RK_PB2 GPIO_ACTIVE_HIGH>;
	};
};

&i2c2 {
	himax@48 {
		himax,irq-gpio = <&gpio1 RK_PB0 IRQ_TYPE_EDGE_FALLING>;
	};
};

&i2c4 {
	himax@48 {
		himax,irq-gpio = <&gpio3 RK_PC5 IRQ_TYPE_EDGE_FALLING>;
	};
};

&i2c5 {
	ilitek@41 {
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PD4 IRQ_TYPE_LEVEL_LOW>;
		reset-gpio = <&gpio0 RK_PD1 GPIO_ACTIVE_LOW>;
	};
};

&i2c6 {
	himax@48 {
		himax,irq-gpio = <&gpio1 RK_PB7 IRQ_TYPE_EDGE_FALLING>;	//use rst as int
	};
};

&i2s2_2ch {
	pinctrl-0 = <&i2s2m1_lrck
		     &i2s2m1_sclk
		     &i2s2m1_sdi
		     &i2s2m1_sdo>;
	status = "okay";
};


&pinctrl {

	bl {
		bl0_enable_pin: bl0-enable-pin {
			rockchip,pins =
				<1 RK_PA7 RK_FUNC_GPIO &pcfg_pull_none>,
				<4 RK_PD2 RK_FUNC_GPIO &pcfg_pull_none>,
				<4 RK_PD3 RK_FUNC_GPIO &pcfg_pull_none>;

		};

		bl1_enable_pin: bl1-enable-pin {
			rockchip,pins = <1 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl2_enable_pin: bl2-enable-pin {
			rockchip,pins = <3 RK_PC4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl3_enable_pin: bl3-enable-pin {
			rockchip,pins = <3 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl4_enable_pin: bl4-enable-pin {
			rockchip,pins = <0 RK_PD5 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bl5_enable_pin: bl5-enable-pin {
			rockchip,pins = <1 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	serdes {
		//dsi0
		ser0_rst_pin: ser0-rst-pin {
			rockchip,pins = <1 RK_PA4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		//dsi1
		ser1_rst_pin: ser1-rst-pin {
			rockchip,pins = <1 RK_PA5 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	touch {
		//dsi0-i2c2
		touch_gpio_dsi0: touch-gpio-dsi0 {
			rockchip,pins =
				<1 RK_PB0 RK_FUNC_GPIO &pcfg_pull_up>;  //rst
		};
		//dsi1-i2c6
		touch_gpio_dsi1: touch-gpio-dsi1 {
			rockchip,pins =
				<1 RK_PB7 RK_FUNC_GPIO &pcfg_pull_up>,  //rst
				<1 RK_PB1 RK_FUNC_GPIO &pcfg_pull_up>;  //int
		};
		//dp0-i2c4
		touch_gpio_dp0: touch-gpio-dp0 {
			rockchip,pins =
				<3 RK_PC5 RK_FUNC_GPIO &pcfg_pull_up>,	//rst
				<0 RK_PC0 RK_FUNC_GPIO &pcfg_pull_up>;	//int
		};
		//edp0-i2c5
		touch_gpio_edp0: touch-gpio-edp0 {
			rockchip,pins =
				<0 RK_PD4 RK_FUNC_GPIO &pcfg_pull_up>,  //rst
				<0 RK_PD1 RK_FUNC_GPIO &pcfg_pull_up>;  //int
		};
	};
};

&rockchip_suspend {
	rockchip,sleep-mode-config = <
		(0
		| RKPM_SLP_ARMOFF_DDRPD
		| RKPM_SLP_PMU_PMUALIVE_32K
		| RKPM_SLP_PMU_DIS_OSC
		| RKPM_SLP_32K_EXT
		)
	>;
	rockchip,wakeup-config = <
		(0
		| RKPM_CPU0_WKUP_EN
		| RKPM_GPIO_WKUP_EN
		)
	>;
	status = "okay";
};

&vdd_log_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <800000>;
	};
};

&vcc_3v3_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <3300000>;
	};
};

&vcc_1v8_s0 {
	regulator-state-mem {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <1800000>;
	};
};

&vdd_1v8_pll_s0 {
		regulator-on-in-suspend;
		regulator-suspend-microvolt = <1800000>;
};
