// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 */

#include "dt-bindings/usb/pd.h"
#include "rk3588m.dtsi"
#include "rk3588-vehicle.dtsi"
#include "rk3588-rk806-single.dtsi"

/ {
	es8388_sound: es8388-sound {
		status = "disabled";
		compatible = "rockchip,multicodecs-card";
		rockchip,card-name = "rockchip-es8388";
		hp-det-gpio = <&gpio1 RK_PC4 GPIO_ACTIVE_LOW>;
		io-channels = <&saradc 3>;
		io-channel-names = "adc-detect";
		keyup-threshold-microvolt = <1800000>;
		poll-interval = <100>;
		spk-con-gpio = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
		hp-con-gpio = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
		rockchip,format = "i2s";
		rockchip,mclk-fs = <256>;
		rockchip,cpu = <&i2s0_8ch>;
		rockchip,codec = <&es8388>;
		rockchip,audio-routing =
			"Headphone", "LOUT1",
			"Headphone", "ROUT1",
			"Speaker", "LOUT2",
			"Speaker", "ROUT2",
			"Headphone", "Headphone Power",
			"Headphone", "Headphone Power",
			"Speaker", "Speaker Power",
			"Speaker", "Speaker Power",
			"LINPUT1", "Main Mic",
			"LINPUT2", "Main Mic",
			"RINPUT1", "Headset Mic",
			"RINPUT2", "Headset Mic";
		pinctrl-names = "default";
		pinctrl-0 = <&hp_det>;
		play-pause-key {
			label = "playpause";
			linux,code = <KEY_PLAYPAUSE>;
			press-threshold-microvolt = <2000>;
		};
	};

	fan: pwm-fan {
		compatible = "pwm-fan";
		#cooling-cells = <2>;
		pwms = <&pwm3 0 50000 0>;
		cooling-levels = <0 50 100 150 200 255>;
		rockchip,temp-trips = <
			50000	1
			55000	2
			60000	3
			65000	4
			70000	5
		>;
	};


	pcie20_avdd0v85: pcie20-avdd0v85 {
		compatible = "regulator-fixed";
		regulator-name = "pcie20_avdd0v85";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <850000>;
		vin-supply = <&vdd_0v85_s0>;
	};

	pcie20_avdd1v8: pcie20-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "pcie20_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	pcie30_avdd0v75: pcie30-avdd0v75 {
		compatible = "regulator-fixed";
		regulator-name = "pcie30_avdd0v75";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <750000>;
		regulator-max-microvolt = <750000>;
		vin-supply = <&avdd_0v75_s0>;
	};

	pcie30_avdd1v8: pcie30-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "pcie30_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;
		/*
		 * On the module itself this is one of these (depending
		 * on the actual card populated):
		 * - SDIO_RESET_L_WL_REG_ON
		 * - PDN (power down when low)
		 */
		post-power-on-delay-ms = <200>;
		reset-gpios = <&gpio2 RK_PC5 GPIO_ACTIVE_LOW>;
		status = "disabled";
	};

	rk_headset: rk-headset {
		status = "disabled";
		compatible = "rockchip_headset";
		headset_gpio = <&gpio1 RK_PD5 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&hp_det>;
		io-channels = <&saradc 3>;
	};


	vcc_1v1_nldo_s3: vcc-1v1-nldo-s3 {
		compatible = "regulator-fixed";
		regulator-name = "vcc_1v1_nldo_s3";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <1100000>;
		regulator-max-microvolt = <1100000>;
		vin-supply = <&vcc5v0_sys>;
	};

	vcc3v3_lcd_n: vcc3v3-lcd0-n {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_lcd0_n";
		regulator-boot-on;
		enable-active-high;
		gpio = <&gpio2 RK_PC1 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc_1v8_s0>;
	};

	vcc5v0_otg: vcc5v0-otg {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_otg";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio4 RK_PA0 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_otg_en>;
	};

	vcc5v0_host: vcc5v0-host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio4 RK_PA3 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_host_en>;
	};

	wireless_bluetooth: wireless-bluetooth {
		compatible = "bluetooth-platdata";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		uart_rts_gpios = <&gpio4 RK_PC4 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart9m0_rtsn>, <&bt_reset_gpio>, <&bt_wake_gpio>, <&bt_irq_gpio>;
		pinctrl-1 = <&uart9_gpios>;
		BT,reset_gpio    = <&gpio4 RK_PC2 GPIO_ACTIVE_HIGH>;
		BT,wake_gpio     = <&gpio4 RK_PC6 GPIO_ACTIVE_HIGH>;
		BT,wake_host_irq = <&gpio2 RK_PB4 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless_wlan: wireless-wlan {
		compatible = "wlan-platdata";
		wifi_chip_type = "ap6398s";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_host_wake_irq>;
		WIFI,host_wake_irq = <&gpio2 RK_PB6 GPIO_ACTIVE_HIGH>;
		WIFI,poweren_gpio = <&gpio2 RK_PC5 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	dummy_codec: dummy-codec {
		status = "okay";
		compatible = "rockchip,dummy-codec";
		#sound-dai-cells = <0>;
		pinctrl-names = "default";
		pinctrl-0 = <&rk3308_reset>;
	};

	car_rk3308_sound: car-rk3308-sound {
		status = "okay";
		compatible = "simple-audio-card";
		simple-audio-card,name = "rockchip,car-rk3308-sound";
		simple-audio-card,format = "i2s";
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,bitclock-master = <&codec_master>;
		simple-audio-card,frame-master = <&codec_master>;
		simple-audio-card,cpu {
			sound-dai = <&i2s0_8ch>;
			dai-tdm-slot-num = <8>;
			dai-tdm-slot-width = <32>;
		};
		codec_master: simple-audio-card,codec {
			sound-dai = <&dummy_codec>;
		};
	};
};

&backlight {
	pwms = <&pwm1 0 25000 0>;
	status = "okay";
};

&combphy0_ps {
	status = "okay";
};

&combphy1_ps {
	status = "okay";
};

&combphy2_psu {
	status = "okay";
};

&gmac1 {
	/* Use rgmii-rxid mode to disable rx delay inside Soc */
	phy-mode = "rgmii-rxid";
	clock_in_out = "output";

	snps,reset-gpio = <&gpio3 RK_PB7 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	/* Reset time is 20ms, 100ms for rtl8211f */
	snps,reset-delays-us = <0 20000 100000>;

	pinctrl-names = "default";
	pinctrl-0 = <&gmac1_miim
		     &gmac1_tx_bus2
		     &gmac1_rx_bus2
		     &gmac1_rgmii_clk
		     &gmac1_rgmii_bus>;

	tx_delay = <0x43>;
	/* rx_delay = <0x3f>; */

	phy-handle = <&rgmii_phy>;
	status = "disabled";
};

&hdmi0 {
	enable-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&hdmi0_in_vp0 {
	status = "okay";
};

&hdmi0_sound {
	status = "okay";
};

&hdmi1 {
	enable-gpios = <&gpio4 RK_PB2 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&hdmi1_in_vp0 {
	status = "okay";
};

&hdmi1_sound {
	status = "okay";
};


&hdptxphy_hdmi0 {
	status = "okay";
};

&hdptxphy_hdmi1 {
	status = "okay";
};

&i2c0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c0m2_xfer>;

	vdd_cpu_big0_s0: vdd_cpu_big0_mem_s0: rk8602@42 {
		compatible = "rockchip,rk8602";
		reg = <0x42>;
		vin-supply = <&vcc5v0_sys>;
		regulator-compatible = "rk860x-reg";
		regulator-name = "vdd_cpu_big0_s0";
		regulator-min-microvolt = <550000>;
		regulator-max-microvolt = <1050000>;
		regulator-ramp-delay = <2300>;
		rockchip,suspend-voltage-selector = <1>;
		regulator-boot-on;
		regulator-always-on;
		regulator-state-mem {
			regulator-off-in-suspend;
		};
	};

	vdd_cpu_big1_s0: vdd_cpu_big1_mem_s0: rk8603@43 {
		compatible = "rockchip,rk8603";
		reg = <0x43>;
		vin-supply = <&vcc5v0_sys>;
		regulator-compatible = "rk860x-reg";
		regulator-name = "vdd_cpu_big1_s0";
		regulator-min-microvolt = <550000>;
		regulator-max-microvolt = <1050000>;
		regulator-ramp-delay = <2300>;
		rockchip,suspend-voltage-selector = <1>;
		regulator-boot-on;
		regulator-always-on;
		regulator-state-mem {
			regulator-off-in-suspend;
		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c1m2_xfer>;

	vdd_npu_s0: vdd_npu_mem_s0: rk8602@42 {
		compatible = "rockchip,rk8602";
		reg = <0x42>;
		vin-supply = <&vcc5v0_sys>;
		regulator-compatible = "rk860x-reg";
		regulator-name = "vdd_npu_s0";
		regulator-min-microvolt = <550000>;
		regulator-max-microvolt = <950000>;
		regulator-ramp-delay = <2300>;
		rockchip,suspend-voltage-selector = <1>;
		regulator-boot-on;
		regulator-always-on;
		regulator-state-mem {
			regulator-off-in-suspend;
		};
	};
};

&i2c4 {
	status = "okay";
	pinctrl-0 = <&i2c4m2_xfer>;
	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "hym8563";
		pinctrl-names = "default";
		pinctrl-0 = <&hym8563_int>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PB0 IRQ_TYPE_LEVEL_LOW>;
		wakeup-source;
	};

};

&i2c5 {
	status = "okay";

};

&i2c6 {
	status = "okay";

};

&i2c7 {
	status = "okay";
	es8388: es8388@11 {
		status = "okay";
		#sound-dai-cells = <0>;
		compatible = "everest,es8388", "everest,es8323";
		reg = <0x11>;
		clocks = <&mclkout_i2s0>;
		clock-names = "mclk";
		assigned-clocks = <&mclkout_i2s0>;
		assigned-clock-rates = <12288000>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s0_mclk>;
	};
};

&i2s0_8ch {
	status = "okay";
	rockchip,tdm-fsync-half-frame;
	pinctrl-names = "default";
	pinctrl-0 = <&i2s0_lrck
		     &i2s0_sclk
		     &i2s0_sdi0
		     &i2s0_sdi1
		     &i2s0_sdo0
		     &i2s0_sdo1
		     &i2s0_sdo2
		     &i2s0_sdo3>;
};

&i2s5_8ch {
	status = "okay";
};

&i2s6_8ch {
	status = "okay";
};

&i2s7_8ch {
	status = "okay";
};

&mdio1 {
	rgmii_phy: phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0x1>;
	};
};

&pcie2x1l0 {
	reset-gpios = <&gpio1 RK_PB4 GPIO_ACTIVE_HIGH>;
	status = "disabled";
};

&pcie2x1l2 {
	reset-gpios = <&gpio3 RK_PD1 GPIO_ACTIVE_HIGH>;
	rockchip,skip-scan-in-resume;
	pinctrl-names = "default";
	pinctrl-0 = <&wifi_enable_h>;
	status = "okay";
};

&pcie30phy {
	rockchip,pcie30-phymode = <PHY_MODE_PCIE_AGGREGATION>;
	status = "disabled";
};

&pcie3x4 {
	reset-gpios = <&gpio4 RK_PB3 GPIO_ACTIVE_HIGH>;
	status = "disabled";
};

&pinctrl {
	cam {
		mipicsi0_pwr: mipicsi0-pwr {
			rockchip,pins =
				/* camera power en */
				<1 RK_PD2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
		mipicsi1_pwr: mipicsi1-pwr {
			rockchip,pins =
				/* camera power en */
				<1 RK_PD3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
		mipidcphy0_pwr: mipidcphy0-pwr {
			rockchip,pins =
				/* camera power en */
				<2 RK_PC4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	headphone {
		hp_det: hp-det {
			rockchip,pins = <1 RK_PC4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hym8563 {
		hym8563_int: hym8563-int {
			rockchip,pins = <0 RK_PB0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	lcd {
		lcd_rst_gpio: lcd-rst-gpio {
			rockchip,pins = <0 RK_PD3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sdio-pwrseq {
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <2 RK_PC5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	touch {
		touch_gpio: touch-gpio {
			rockchip,pins =
				<3 RK_PC1 RK_FUNC_GPIO &pcfg_pull_down>,
				<3 RK_PC0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb {
		vcc5v0_otg_en: vcc5v0-otg-en {
			rockchip,pins = <4 RK_PA0 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		vcc5v0_host_en: vcc5v0-host-en {
			rockchip,pins = <4 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};


	wireless-bluetooth {
		uart9_gpios: uart9-gpios {
			rockchip,pins = <4 RK_PC4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_reset_gpio: bt-reset-gpio {
			rockchip,pins = <4 RK_PC2 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_wake_gpio: bt-wake-gpio {
			rockchip,pins = <4 RK_PC6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_irq_gpio: bt-irq-gpio {
			rockchip,pins = <2 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-wlan {
		wifi_host_wake_irq: wifi-host-wake-irq {
			rockchip,pins = <2 RK_PB6 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};

	rk3308 {
		rk3308_reset: rk3308-reset {
			rockchip,pins = <2 RK_PC1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&pwm1 {
	status = "okay";
};

&pwm3 {
	pinctrl-0 = <&pwm3m1_pins>;
	status = "okay";
};

&route_dsi0 {
	status = "okay";
	connect = <&vp3_out_dsi0>;
};

&route_dsi1 {
	status = "disabled";
	connect = <&vp3_out_dsi1>;
};

&route_hdmi0 {
	status = "okay";
};

&route_hdmi1 {
	status = "okay";
};

&sata0 {
	status = "okay";
};

&sdio {
	max-frequency = <150000000>;
	no-sd;
	no-mmc;
	bus-width = <4>;
	disable-wp;
	cap-sd-highspeed;
	cap-sdio-irq;
	keep-power-in-suspend;
	mmc-pwrseq = <&sdio_pwrseq>;
	non-removable;
	pinctrl-names = "default";
	pinctrl-0 = <&sdiom0_pins>;
	sd-uhs-sdr104;
	status = "disabled";
};

&sdmmc {
	status = "disabled";
};

&uart9 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart9m0_xfer &uart9m0_ctsn>;
};

&u2phy0_otg {
	status = "okay";
};

&u2phy1_otg {
	phy-supply = <&vcc5v0_otg>;
};

&u2phy2_host {
	phy-supply = <&vcc5v0_otg>;
};

&u2phy3_host {
	phy-supply = <&vcc5v0_host>;
};

&usbdp_phy0 {
	rockchip,dp-lane-mux = <2 3>;
	status = "okay";
};

&usbdp_phy0_dp {
	status = "okay";
};

&usbdp_phy0_u3 {
	status = "okay";
};

&usbdp_phy1 {
	maximum-speed = "high-speed";
	rockchip,dp-lane-mux = <3 2 1 0>;
	status = "okay";
};

&usbdp_phy1_dp {
	status = "okay";
};

&usbdp_phy1_u3 {
	status = "okay";
};

&usbdrd_dwc3_0 {
	dr_mode = "peripheral";
	maximum-speed = "high-speed";
	extcon = <&u2phy0>;
	status = "okay";
};

&usbdrd_dwc3_1 {
	dr_mode = "host";
	maximum-speed = "high-speed";
	snps,dis_u2_susphy_quirk;
	status = "okay";
};
