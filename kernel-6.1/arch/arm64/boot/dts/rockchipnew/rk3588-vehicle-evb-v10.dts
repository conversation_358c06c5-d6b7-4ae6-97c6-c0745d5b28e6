// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

/dts-v1/;

#include "rk3588-vehicle-evb.dtsi"
#include "rk3588-vehicle-evb-mipi-nvp6188.dtsi"
#include "rk3588-vehicle-evb-thine_thcv244.dtsi"
#include "rk3588-vehicle-evb-image-reverse.dtsi"
#include "rk3588-vehicle-serdes-display.dtsi"
#include "rk3588-android.dtsi"

/ {
	model = "Rockchip RK3588 VEHICLE EVB V10 Board";
	compatible = "rockchip,rk3588-vehicle-evb-v10", "rockchip,rk3588";

	bt-sound {
		compatible = "simple-audio-card";
		simple-audio-card,format = "dsp_a";
		simple-audio-card,bitclock-inversion;
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,name = "rockchip,bt";
		simple-audio-card,cpu {
			sound-dai = <&i2s2_2ch>;
		};

		simple-audio-card,codec {
			sound-dai = <&bt_sco 1>;
		};
	};

	bt_sco: bt-sco {
		compatible = "delta,dfbmcs320";
		#sound-dai-cells = <1>;
		status = "okay";
	};
};

&i2s2_2ch{
	pinctrl-0 = <&i2s2m0_lrck
		     &i2s2m0_sclk
		     &i2s2m0_sdi
		     &i2s2m0_sdo>;
	status = "okay";
};
