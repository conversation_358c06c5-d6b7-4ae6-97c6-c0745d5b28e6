// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

#include "rk3588m.dtsi"
#include "rk3588-vehicle-s66.dtsi"
#include "rk3588-rk806-dual.dtsi"
/ {
	pcie20_avdd0v85: pcie20-avdd0v85 {
		compatible = "regulator-fixed";
		regulator-name = "pcie20_avdd0v85";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <850000>;
		vin-supply = <&vdd_0v85_s0>;
	};

	pcie20_avdd1v8: pcie20-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "pcie20_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	pcie30_avdd0v75: pcie30-avdd0v75 {
		compatible = "regulator-fixed";
		regulator-name = "pcie30_avdd0v75";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <750000>;
		regulator-max-microvolt = <750000>;
		vin-supply = <&avdd_0v75_s0>;
	};

	pcie30_avdd1v8: pcie30-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "pcie30_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	vcc3v3_pcie_wifi: vcc3v3-pcie-wifi {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_pcie_wifi";
		regulator-always-on;
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		enable-active-high;
		gpios = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
		startup-delay-us = <5000>;
		vin-supply = <&vcc_3v3_s0>;
	};

	vcc5v0_host: vcc5v0-host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		//gpio = <&gpio4 RK_PA3 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		//pinctrl-names = "default";
		//pinctrl-0 = <&vcc5v0_host_en>;
		//TODO: should powered by MCU
	};

	wireless_bluetooth: wireless-bluetooth {
		compatible = "bluetooth-platdata";
		BT,reset_gpio = <&gpio4 RK_PB6 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless_wlan: wireless-wlan {
		compatible = "wlan-platdata";
		wifi_chip_type = "ap6398s";
		WIFI,poweren_gpio = <&gpio4 RK_PA7 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};
};

&combphy0_ps {
	status = "okay";
};

&combphy1_ps {
	status = "okay";
};

&combphy2_psu {
	status = "okay";
};

&gmac0 {
	/* Use rgmii-rxid mode to disable rx delay inside Soc */
	phy-mode = "rgmii-rxid";
	clock_in_out = "output";
	snps,reset-gpio = <&gpio2 RK_PC5 GPIO_ACTIVE_LOW>;
	snps,reset-active-low;
	/* Reset time is 20ms, 100ms for rtl8211f */
	snps,reset-delays-us = <0 20000 100000>;
	pinctrl-0 = <&gmac0_miim
		     &gmac0_tx_bus2
		     &gmac0_rx_bus2
		     &gmac0_rgmii_clk
		     &gmac0_rgmii_bus>;
	tx_delay = <0x43>;
	//rx_delay = <0x3f>;
	phy-handle = <&rgmii_phy>;
	status = "okay";
};

&i2c3 {
	status = "okay";

	iam20680_acc: acc@69 {
		compatible = "iam20680_acc";
		reg = <0x69>;
		irq-gpio = <&gpio1 RK_PC2 IRQ_TYPE_LEVEL_LOW>;
		irq_enable = <1>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_ACCEL>;
		layout = <1>;
	};

	iam20680_gyro: gyro@69 {
		compatible = "iam20680_gyro";
		reg = <0x69>;
		irq_enable = <0>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_GYROSCOPE>;
		layout = <1>;
	};

	//todo, add mfi
};

&i2c4 {
	status = "okay";
	pinctrl-0 = <&i2c4m0_xfer>;
	//todo, add LT9211
};

&mdio0 {
	rgmii_phy: phy@1 {
		compatible = "ethernet-phy-ieee802.3-c22";
		reg = <0x1>;
	};
};

&pcie2x1l0 {
	status = "disabled";
};

&pcie2x1l1 {
	status = "disabled";
};

&pcie2x1l2 {
	reset-gpios = <&gpio4 RK_PC1 GPIO_ACTIVE_HIGH>;
	rockchip,skip-scan-in-resume;
	rockchip,perst-inactive-ms = <500>;
	vpcie3v3-supply = <&vcc3v3_pcie_wifi>;
	status = "okay";
};

&pcie30phy {
	rockchip,pcie30-phymode = <PHY_MODE_PCIE_NABIBI>;
	status = "disabled";
};

&pcie3x4 {
	num-lanes = <1>;
	status = "disabled";
};

&sata0 {
	status = "disabled";
};

&sdmmc {
	status = "disabled";
};

&uart1 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart1m1_xfer &uart1m1_ctsn &uart1m1_rtsn>;
};

&u2phy1_otg {
	phy-supply = <&vcc5v0_host>;
};

&u2phy2_host {
	phy-supply = <&vcc5v0_host>;
};

&u2phy3_host {
	phy-supply = <&vcc5v0_host>;
};

&usbdp_phy0 {
	rockchip,dp-lane-mux = <2 3>;
	status = "okay";
};

&usbdp_phy0_dp {
	status = "okay";
};

&usbdp_phy0_u3 {
	status = "okay";
};

&usbdp_phy1 {
	maximum-speed = "high-speed";
	rockchip,dp-lane-mux = <3 2 1 0>;
	status = "disabled";
};

&usbdp_phy1_dp {
	status = "disabled";
};

&usbdp_phy1_u3 {
	status = "okay";
};

&usbdrd_dwc3_0 {
	dr_mode = "peripheral";
	maximum-speed = "high-speed";
	extcon = <&u2phy0>;
	status = "okay";
};

&usbdrd_dwc3_1 {
	dr_mode = "host";
	maximum-speed = "high-speed";
	snps,dis_u2_susphy_quirk;
	status = "okay";
};
