// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 */

#include <dt-bindings/phy/phy-snps-pcie3.h>
#include "rk3588s.dtsi"
#include "rk3588-vccio3-pinctrl.dtsi"

/ {
	aliases {
		dp0 = &dp0;
		dp1 = &dp1;
		edp0 = &edp0;
		edp1 = &edp1;
		ethernet0 = &gmac0;
		hdptx0 = &hdptxphy0;
		hdptx1 = &hdptxphy1;
		hdptxhdmi0 = &hdptxphy_hdmi0;
		hdptxhdmi1 = &hdptxphy_hdmi1;
		hdmi0 = &hdmi0;
		hdmi1 = &hdmi1;
		hdmirx0 = &hdmirx_ctrler;
		usbdp0 = &usbdp_phy0;
		usbdp1 = &usbdp_phy1;
	};

	usbdrd3_1: usbdrd3_1 {
		compatible = "rockchip,rk3588-dwc3", "rockchip,rk3399-dwc3";
		clocks = <&cru REF_CLK_USB3OTG1>, <&cru SUSPEND_CLK_USB3OTG1>,
			 <&cru ACLK_USB3OTG1>;
		clock-names = "ref", "suspend", "bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		status = "disabled";

		usbdrd_dwc3_1: usb@fc400000 {
			compatible = "snps,dwc3";
			reg = <0x0 0xfc400000 0x0 0x400000>;
			interrupts = <GIC_SPI 221 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&power RK3588_PD_USB>;
			resets = <&cru SRST_A_USB3OTG1>;
			reset-names = "usb3-otg";
			dr_mode = "host";
			phys = <&u2phy1_otg>, <&usbdp_phy1_u3>;
			phy-names = "usb2-phy", "usb3-phy";
			phy_type = "utmi_wide";
			snps,dis_enblslpm_quirk;
			snps,dis-u1-entry-quirk;
			snps,dis-u2-entry-quirk;
			snps,dis-u2-freeclk-exists-quirk;
			snps,dis-del-phy-power-chg-quirk;
			snps,dis-tx-ipgap-linecheck-quirk;
			snps,parkmode-disable-hs-quirk;
			snps,parkmode-disable-ss-quirk;
			status = "disabled";
		};
	};

	pcie30_phy_grf: syscon@fd5b8000 {
		compatible = "rockchip,pcie30-phy-grf", "syscon";
		reg = <0x0 0xfd5b8000 0x0 0x10000>;
	};

	pipe_phy1_grf: syscon@fd5c0000 {
		compatible = "rockchip,pipe-phy-grf", "syscon";
		reg = <0x0 0xfd5c0000 0x0 0x100>;
	};

	usbdpphy1_grf: syscon@fd5cc000 {
		compatible = "rockchip,rk3588-usbdpphy-grf", "syscon";
		reg = <0x0 0xfd5cc000 0x0 0x4000>;
	};

	usb2phy1_grf: syscon@fd5d4000 {
		compatible = "rockchip,rk3588-usb2phy-grf", "syscon",
			     "simple-mfd";
		reg = <0x0 0xfd5d4000 0x0 0x4000>;
		#address-cells = <1>;
		#size-cells = <1>;

		u2phy1: usb2-phy@4000 {
			compatible = "rockchip,rk3588-usb2phy";
			reg = <0x4000 0x10>;
			interrupts = <GIC_SPI 394 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&cru SRST_OTGPHY_U3_1>, <&cru SRST_P_USB2PHY_U3_1_GRF0>;
			reset-names = "phy", "apb";
			clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>;
			clock-names = "phyclk";
			clock-output-names = "usb480m_phy1";
			#clock-cells = <0>;
			rockchip,usbctrl-grf = <&usb_grf>;
			status = "disabled";

			u2phy1_otg: otg-port {
				#phy-cells = <0>;
				status = "disabled";
			};
		};
	};

	hdptxphy1_grf: syscon@fd5e4000 {
		compatible = "rockchip,rk3588-hdptxphy-grf", "syscon";
		reg = <0x0 0xfd5e4000 0x0 0x100>;
	};

	spdif_tx5: spdif-tx@fddb8000 {
		compatible = "rockchip,rk3588-spdif", "rockchip,rk3568-spdif";
		reg = <0x0 0xfddb8000 0x0 0x1000>;
		interrupts = <GIC_SPI 198 IRQ_TYPE_LEVEL_HIGH>;
		dmas = <&dmac1 22>;
		dma-names = "tx";
		clock-names = "mclk", "hclk";
		clocks = <&cru MCLK_SPDIF5>, <&cru HCLK_SPDIF5_DP1>;
		assigned-clocks = <&cru CLK_SPDIF5_DP1_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		power-domains = <&power RK3588_PD_VO0>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s8_8ch: i2s@fddc8000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfddc8000 0x0 0x1000>;
		interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S8_8CH_TX>, <&cru HCLK_I2S8_8CH>;
		clock-names = "mclk_tx", "hclk";
		assigned-clocks = <&cru CLK_I2S8_8CH_TX_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac2 22>;
		dma-names = "tx";
		power-domains = <&power RK3588_PD_VO0>;
		resets = <&cru SRST_M_I2S8_8CH_TX>;
		reset-names = "tx-m";
		rockchip,playback-only;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif_tx4: spdif-tx@fdde8000 {
		compatible = "rockchip,rk3588-spdif", "rockchip,rk3568-spdif";
		reg = <0x0 0xfdde8000 0x0 0x1000>;
		interrupts = <GIC_SPI 197 IRQ_TYPE_LEVEL_HIGH>;
		dmas = <&dmac1 8>;
		dma-names = "tx";
		clock-names = "mclk", "hclk";
		clocks = <&cru MCLK_SPDIF4>, <&cru HCLK_SPDIF4>;
		assigned-clocks = <&cru CLK_SPDIF4_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		power-domains = <&power RK3588_PD_VO1>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s6_8ch: i2s@fddf4000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfddf4000 0x0 0x1000>;
		interrupts = <GIC_SPI 186 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S6_8CH_TX>, <&cru MCLK_I2S6_8CH_TX>, <&cru HCLK_I2S6_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		assigned-clocks = <&cru CLK_I2S6_8CH_TX_SRC>;
		assigned-clock-parents = <&cru PLL_GPLL>;
		dmas = <&dmac2 4>;
		dma-names = "tx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_I2S6_8CH_TX>;
		reset-names = "tx-m";
		rockchip,always-on;
		rockchip,hdmi-path;
		rockchip,playback-only;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s7_8ch: i2s@fddf8000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfddf8000 0x0 0x1000>;
		interrupts = <GIC_SPI 187 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S7_8CH_RX>, <&cru MCLK_I2S7_8CH_RX>, <&cru HCLK_I2S7_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		assigned-clocks = <&cru CLK_I2S7_8CH_RX_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac2 21>;
		dma-names = "rx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_I2S7_8CH_RX>;
		reset-names = "rx-m";
		rockchip,capture-only;
		rockchip,i2s-rx-wait-time-ms = <50>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s10_8ch: i2s@fde00000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfde00000 0x0 0x1000>;
		interrupts = <GIC_SPI 190 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S10_8CH_RX>, <&cru MCLK_I2S10_8CH_RX>, <&cru HCLK_I2S10_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		assigned-clocks = <&cru CLK_I2S10_8CH_RX_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac2 24>;
		dma-names = "rx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_I2S10_8CH_RX>;
		reset-names = "rx-m";
		rockchip,capture-only;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif_rx1: spdif-rx@fde10000 {
		compatible = "rockchip,rk3588-spdifrx", "rockchip,rk3308-spdifrx";
		reg = <0x0 0xfde10000 0x0 0x1000>;
		interrupts = <GIC_SPI 200 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_SPDIFRX1>, <&cru HCLK_SPDIFRX1>;
		clock-names = "mclk", "hclk";
		assigned-clocks = <&cru MCLK_SPDIFRX1>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac0 22>;
		dma-names = "rx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_SPDIFRX1>;
		reset-names = "spdifrx-m";
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif_rx2: spdif-rx@fde18000 {
		compatible = "rockchip,rk3588-spdifrx", "rockchip,rk3308-spdifrx";
		reg = <0x0 0xfde18000 0x0 0x1000>;
		interrupts = <GIC_SPI 201 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_SPDIFRX2>, <&cru HCLK_SPDIFRX2>;
		clock-names = "mclk", "hclk";
		assigned-clocks = <&cru MCLK_SPDIFRX2>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac0 23>;
		dma-names = "rx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_SPDIFRX2>;
		reset-names = "spdifrx-m";
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	dp1: dp@fde60000 {
		compatible = "rockchip,rk3588-dp";
		reg = <0x0 0xfde60000 0x0 0x4000>;
		interrupts = <GIC_SPI 162 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_DP1>, <&cru CLK_AUX16M_1>,
			 <&cru MCLK_I2S8_8CH_TX>, <&cru MCLK_SPDIF5_DP1>,
			 <&hclk_vo0>, <&cru CLK_DP1>;
		clock-names = "apb", "aux", "i2s", "spdif", "hclk", "hdcp";
		assigned-clocks = <&cru CLK_AUX16M_1>;
		assigned-clock-rates = <16000000>;
		resets = <&cru SRST_DP1>;
		phys = <&usbdp_phy1_dp>;
		power-domains = <&power RK3588_PD_VO0>;
		#sound-dai-cells = <1>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				dp1_in_vp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp0_out_dp1>;
					status = "disabled";
				};

				dp1_in_vp1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp1_out_dp1>;
					status = "disabled";
				};

				dp1_in_vp2: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&vp2_out_dp1>;
					status = "disabled";
				};
			};

			port@1 {
				reg = <1>;

				dp1_out: endpoint { };
			};
		};
	};

	hdmi1: hdmi@fdea0000 {
		compatible = "rockchip,rk3588-dw-hdmi";
		reg = <0x0 0xfdea0000 0x0 0x10000>, <0x0 0xfdeb0000 0x0 0x10000>;
		interrupts = <GIC_SPI 173 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 176 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 361 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_HDMITX1>,
			 <&cru CLK_HDMIHDP1>,
			 <&cru CLK_HDMITX1_EARC>,
			 <&cru CLK_HDMITX1_REF>,
			 <&cru MCLK_I2S6_8CH_TX>,
			 <&cru DCLK_VOP0>,
			 <&cru DCLK_VOP1>,
			 <&cru DCLK_VOP2>,
			 <&cru DCLK_VOP3>,
			 <&hclk_vo1>,
			 <&hdptxphy_hdmi1>;
		clock-names = "pclk",
			      "hpd",
			      "earc",
			      "hdmitx_ref",
			      "aud",
			      "dclk_vp0",
			      "dclk_vp1",
			      "dclk_vp2",
			      "dclk_vp3",
			      "hclk_vo1",
			      "link_clk";
		resets = <&cru SRST_HDMITX1_REF>, <&cru SRST_HDMIHDP1>;
		reset-names = "ref", "hdp";
		power-domains = <&power RK3588_PD_VO1>;
		pinctrl-names = "default";
		pinctrl-0 = <&hdmim2_tx1_cec &hdmim0_tx1_hpd &hdmim1_tx1_scl &hdmim1_tx1_sda>;
		reg-io-width = <4>;
		rockchip,grf = <&sys_grf>;
		rockchip,vo1_grf = <&vo1_grf>;
		phys = <&hdptxphy_hdmi1>;
		phy-names = "hdmi";
		#sound-dai-cells = <0>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			hdmi1_in: port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi1_in_vp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp0_out_hdmi1>;
					status = "disabled";
				};

				hdmi1_in_vp1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp1_out_hdmi1>;
					status = "disabled";
				};

				hdmi1_in_vp2: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&vp2_out_hdmi1>;
					status = "disabled";
				};
			};
		};
	};

	edp1: edp@fded0000 {
		compatible = "rockchip,rk3588-edp";
		reg = <0x0 0xfded0000 0x0 0x1000>;
		interrupts = <GIC_SPI 164 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru CLK_EDP1_24M>, <&cru PCLK_EDP1>,
			 <&cru CLK_EDP1_200M>, <&hclk_vo1>;
		clock-names = "dp", "pclk", "spdif", "hclk";
		resets = <&cru SRST_EDP1_24M>, <&cru SRST_P_EDP1>;
		reset-names = "dp", "apb";
		phys = <&hdptxphy1>;
		phy-names = "dp";
		power-domains = <&power RK3588_PD_VO1>;
		rockchip,grf = <&vo1_grf>;
		#sound-dai-cells = <1>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				edp1_in_vp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp0_out_edp1>;
					status = "disabled";
				};

				edp1_in_vp1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp1_out_edp1>;
					status = "disabled";
				};

				edp1_in_vp2: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&vp2_out_edp1>;
					status = "disabled";
				};
			};

			port@1 {
				reg = <1>;

				edp1_out: endpoint { };
			};
		};
	};

	hdmirx_ctrler: hdmirx-controller@fdee0000 {
		compatible = "rockchip,rk3588-hdmirx-ctrler", "rockchip,hdmirx-ctrler";
		reg = <0x0 0xfdee0000 0x0 0x6000>;
		reg-names = "hdmirx_regs";
		power-domains = <&power RK3588_PD_VO1>;
		rockchip,grf = <&sys_grf>;
		rockchip,vo1_grf = <&vo1_grf>;
		interrupts = <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 436 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 179 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "cec", "hdmi", "dma";
		clocks = <&cru ACLK_HDMIRX>,
			 <&cru CLK_HDMIRX_AUD>,
			 <&cru CLK_CR_PARA>,
			 <&cru PCLK_HDMIRX>,
			 <&cru CLK_HDMIRX_REF>,
			 <&cru PCLK_S_HDMIRX>,
			 <&hclk_vo1>;
		clock-names = "aclk",
			      "audio",
			      "cr_para",
			      "pclk",
			      "ref",
			      "hclk_s_hdmirx",
			      "hclk_vo1";
		resets = <&cru SRST_A_HDMIRX>, <&cru SRST_P_HDMIRX>,
			 <&cru SRST_HDMIRX_REF>, <&cru SRST_A_HDMIRX_BIU>;
		reset-names = "rst_a", "rst_p", "rst_ref", "rst_biu";
		pinctrl-0 = <&hdmim1_rx>;
		pinctrl-names = "default";
		status = "disabled";
	};

	pcie3x4: pcie@fe150000 {
		compatible = "rockchip,rk3588-pcie", "snps,dw-pcie";
		#address-cells = <3>;
		#size-cells = <2>;
		bus-range = <0x00 0x0f>;
		clocks = <&cru ACLK_PCIE_4L_MSTR>, <&cru ACLK_PCIE_4L_SLV>,
			 <&cru ACLK_PCIE_4L_DBI>, <&cru PCLK_PCIE_4L>,
			 <&cru CLK_PCIE_AUX0>, <&cru CLK_PCIE4L_PIPE>;
		clock-names = "aclk_mst", "aclk_slv",
			      "aclk_dbi", "pclk",
			      "aux", "pipe";
		device_type = "pci";
		interrupts = <GIC_SPI 263 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 262 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 261 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 260 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 259 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "sys", "pmc", "msg", "legacy", "err";
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pcie3x4_intc 0>,
				<0 0 0 2 &pcie3x4_intc 1>,
				<0 0 0 3 &pcie3x4_intc 2>,
				<0 0 0 4 &pcie3x4_intc 3>;
		linux,pci-domain = <0>;
		num-ib-windows = <16>;
		num-ob-windows = <16>;
		num-viewport = <8>;
		max-link-speed = <3>;
		msi-map = <0x0000 &its1 0x0000 0x1000>;
		num-lanes = <4>;
		phys = <&pcie30phy>;
		phy-names = "pcie-phy";
		power-domains = <&power RK3588_PD_PCIE>;
		ranges = <0x81000000 0x0 0xf0100000 0x0 0xf0100000 0x0 0x100000
			  0x82000000 0x0 0xf0200000 0x0 0xf0200000 0x0 0xe00000
			  0xc3000000 0x9 0x00000000 0x9 0x00000000 0x0 0x40000000>;
		reg = <0x0 0xfe150000 0x0 0x10000>,
		      <0xa 0x40000000 0x0 0x400000>,
		      <0x0 0xf0000000 0x0 0x100000>;
		reg-names = "pcie-apb", "pcie-dbi", "config";
		resets = <&cru SRST_PCIE0_POWER_UP>, <&cru SRST_P_PCIE0>;
		reset-names = "pcie", "periph";
		rockchip,pipe-grf = <&php_grf>;
		status = "disabled";

		pcie3x4_intc: legacy-interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
			interrupt-parent = <&gic>;
			interrupts = <GIC_SPI 260 IRQ_TYPE_EDGE_RISING>;
		};
	};

	pcie3x2: pcie@fe160000 {
		compatible = "rockchip,rk3588-pcie", "snps,dw-pcie";
		#address-cells = <3>;
		#size-cells = <2>;
		bus-range = <0x10 0x1f>;
		clocks = <&cru ACLK_PCIE_2L_MSTR>, <&cru ACLK_PCIE_2L_SLV>,
			 <&cru ACLK_PCIE_2L_DBI>, <&cru PCLK_PCIE_2L>,
			 <&cru CLK_PCIE_AUX1>, <&cru CLK_PCIE2L_PIPE>;
		clock-names = "aclk_mst", "aclk_slv",
			      "aclk_dbi", "pclk",
			      "aux", "pipe";
		device_type = "pci";
		interrupts = <GIC_SPI 258 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 257 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 256 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 255 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 254 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "sys", "pmc", "msg", "legacy", "err";
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pcie3x2_intc 0>,
				<0 0 0 2 &pcie3x2_intc 1>,
				<0 0 0 3 &pcie3x2_intc 2>,
				<0 0 0 4 &pcie3x2_intc 3>;
		linux,pci-domain = <1>;
		num-ib-windows = <16>;
		num-ob-windows = <16>;
		num-viewport = <8>;
		max-link-speed = <3>;
		msi-map = <0x1000 &its1 0x1000 0x1000>;
		num-lanes = <2>;
		phys = <&pcie30phy>;
		phy-names = "pcie-phy";
		power-domains = <&power RK3588_PD_PCIE>;
		ranges = <0x81000000 0x0 0xf1100000 0x0 0xf1100000 0x0 0x100000
			  0x82000000 0x0 0xf1200000 0x0 0xf1200000 0x0 0xe00000
			  0xc3000000 0x9 0x40000000 0x9 0x40000000 0x0 0x40000000>;
		reg = <0x0 0xfe160000 0x0 0x10000>,
		      <0xa 0x40400000 0x0 0x400000>,
		      <0x0 0xf1000000 0x0 0x100000>;
		reg-names = "pcie-apb", "pcie-dbi", "config";
		resets = <&cru SRST_PCIE1_POWER_UP>, <&cru SRST_P_PCIE1>;
		reset-names = "pcie", "periph";
		rockchip,pipe-grf = <&php_grf>;
		status = "disabled";

		pcie3x2_intc: legacy-interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
			interrupt-parent = <&gic>;
			interrupts = <GIC_SPI 255 IRQ_TYPE_EDGE_RISING>;
		};
	};

	pcie2x1l0: pcie@fe170000 {
		compatible = "rockchip,rk3588-pcie", "snps,dw-pcie";
		#address-cells = <3>;
		#size-cells = <2>;
		bus-range = <0x20 0x2f>;
		clocks = <&cru ACLK_PCIE_1L0_MSTR>, <&cru ACLK_PCIE_1L0_SLV>,
			 <&cru ACLK_PCIE_1L0_DBI>, <&cru PCLK_PCIE_1L0>,
			 <&cru CLK_PCIE_AUX2>, <&cru CLK_PCIE1L0_PIPE>;
		clock-names = "aclk_mst", "aclk_slv",
			      "aclk_dbi", "pclk",
			      "aux", "pipe";
		device_type = "pci";
		interrupts = <GIC_SPI 243 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 242 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 241 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 240 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 239 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "sys", "pmc", "msg", "legacy", "err";
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pcie2x1l0_intc 0>,
				<0 0 0 2 &pcie2x1l0_intc 1>,
				<0 0 0 3 &pcie2x1l0_intc 2>,
				<0 0 0 4 &pcie2x1l0_intc 3>;
		linux,pci-domain = <2>;
		num-ib-windows = <8>;
		num-ob-windows = <8>;
		num-viewport = <4>;
		max-link-speed = <2>;
		msi-map = <0x2000 &its0 0x2000 0x1000>;
		num-lanes = <1>;
		phys = <&combphy1_ps PHY_TYPE_PCIE>;
		phy-names = "pcie-phy";
		ranges = <0x81000000 0x0 0xf2100000 0x0 0xf2100000 0x0 0x100000
			  0x82000000 0x0 0xf2200000 0x0 0xf2200000 0x0 0xe00000
			  0xc3000000 0x9 0x80000000 0x9 0x80000000 0x0 0x40000000>;
		reg = <0x0 0xfe170000 0x0 0x10000>,
		      <0xa 0x40800000 0x0 0x400000>,
		      <0x0 0xf2000000 0x0 0x100000>;
		reg-names = "pcie-apb", "pcie-dbi", "config";
		resets = <&cru SRST_PCIE2_POWER_UP>, <&cru SRST_P_PCIE2>;
		reset-names = "pcie", "periph";
		rockchip,pipe-grf = <&php_grf>;
		status = "disabled";

		pcie2x1l0_intc: legacy-interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
			interrupt-parent = <&gic>;
			interrupts = <GIC_SPI 240 IRQ_TYPE_EDGE_RISING>;
		};
	};

	gmac_uio0: uio@fe1b0000 {
		compatible = "rockchip,uio-gmac";
		reg = <0x0 0xfe1b0000 0x0 0x10000>;
		rockchip,ethernet = <&gmac0>;
		status = "disabled";
	};

	gmac0: ethernet@fe1b0000 {
		compatible = "rockchip,rk3588-gmac", "snps,dwmac-4.20a";
		reg = <0x0 0xfe1b0000 0x0 0x10000>;
		interrupts = <GIC_SPI 227 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 226 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "macirq", "eth_wake_irq";
		rockchip,grf = <&sys_grf>;
		rockchip,php_grf = <&php_grf>;
		clocks = <&cru CLK_GMAC_125M>, <&cru CLK_GMAC_50M>,
			 <&cru PCLK_GMAC0>, <&cru ACLK_GMAC0>,
			 <&cru CLK_GMAC0_PTP_REF>;
		clock-names = "stmmaceth", "clk_mac_ref",
			      "pclk_mac", "aclk_mac",
			      "ptp_ref";
		resets = <&cru SRST_A_GMAC0>;
		reset-names = "stmmaceth";
		power-domains = <&power RK3588_PD_GMAC>;

		snps,mixed-burst;
		snps,tso;

		snps,axi-config = <&gmac0_stmmac_axi_setup>;
		snps,mtl-rx-config = <&gmac0_mtl_rx_setup>;
		snps,mtl-tx-config = <&gmac0_mtl_tx_setup>;
		status = "disabled";

		mdio0: mdio {
			compatible = "snps,dwmac-mdio";
			#address-cells = <0x1>;
			#size-cells = <0x0>;
		};

		gmac0_stmmac_axi_setup: stmmac-axi-config {
			snps,wr_osr_lmt = <4>;
			snps,rd_osr_lmt = <8>;
			snps,blen = <0 0 0 0 16 8 4>;
		};

		gmac0_mtl_rx_setup: rx-queues-config {
			snps,rx-queues-to-use = <1>;
			queue0 {};
		};

		gmac0_mtl_tx_setup: tx-queues-config {
			snps,tx-queues-to-use = <1>;
			queue0 {};
		};
	};

	sata1: sata@fe220000 {
		compatible = "rockchip,rk-ahci", "snps,dwc-ahci";
		reg = <0 0xfe220000 0 0x1000>;
		clocks = <&cru ACLK_SATA1>, <&cru CLK_PMALIVE1>,
			 <&cru CLK_RXOOB1>, <&cru CLK_PIPEPHY1_REF>,
			 <&cru CLK_PIPEPHY1_PIPE_ASIC_G>;
		clock-names = "sata", "pmalive", "rxoob", "ref", "asic";
		interrupts = <GIC_SPI 274 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "hostc";
		phys = <&combphy1_ps PHY_TYPE_SATA>;
		phy-names = "sata-phy";
		ports-implemented = <0x1>;
		status = "disabled";
	};

	hdptxphy1: phy@fed70000 {
		compatible = "rockchip,rk3588-hdptx-phy";
		reg = <0x0 0xfed70000 0x0 0x2000>;
		clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>, <&cru PCLK_HDPTX1>;
		clock-names = "ref", "apb";
		resets = <&cru SRST_P_HDPTX1>, <&cru SRST_HDPTX1_INIT>,
			 <&cru SRST_HDPTX1_CMN>, <&cru SRST_HDPTX1_LANE>;
		reset-names = "apb", "init", "cmn", "lane";
		rockchip,grf = <&hdptxphy1_grf>;
		#phy-cells = <0>;
		status = "disabled";
	};

	hdptxphy_hdmi1: hdmiphy@fed70000 {
		compatible = "rockchip,rk3588-hdptx-phy-hdmi";
		reg = <0x0 0xfed70000 0x0 0x2000>;
		clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>, <&cru PCLK_HDPTX1>;
		clock-names = "ref", "apb";
		clock-output-names = "clk_hdmiphy_pixel1";
		#clock-cells = <0>;
		resets = <&cru SRST_HDPTX1>, <&cru SRST_P_HDPTX1>,
			 <&cru SRST_HDPTX1_INIT>, <&cru SRST_HDPTX1_CMN>,
			 <&cru SRST_HDPTX1_LANE>, <&cru SRST_HDPTX1_ROPLL>,
			 <&cru SRST_HDPTX1_LCPLL>;
		reset-names = "phy", "apb", "init", "cmn", "lane", "ropll",
			      "lcpll";
		rockchip,grf = <&hdptxphy1_grf>;
		#phy-cells = <0>;
		status = "disabled";
	};


	usbdp_phy1: phy@fed90000 {
		compatible = "rockchip,rk3588-usbdp-phy";
		reg = <0x0 0xfed90000 0x0 0x10000>;
		rockchip,u2phy-grf = <&usb2phy1_grf>;
		rockchip,usb-grf = <&usb_grf>;
		rockchip,usbdpphy-grf = <&usbdpphy1_grf>;
		rockchip,vo-grf = <&vo0_grf>;
		clocks = <&cru CLK_USBDPPHY_MIPIDCPPHY_REF>,
			 <&cru CLK_USBDP_PHY1_IMMORTAL>,
			 <&cru PCLK_USBDPPHY1>,
			 <&u2phy1>;
		clock-names = "refclk", "immortal", "pclk", "utmi";
		resets = <&cru SRST_USBDP_COMBO_PHY1_INIT>,
			 <&cru SRST_USBDP_COMBO_PHY1_CMN>,
			 <&cru SRST_USBDP_COMBO_PHY1_LANE>,
			 <&cru SRST_USBDP_COMBO_PHY1_PCS>,
			 <&cru SRST_P_USBDPPHY1>;
		reset-names = "init", "cmn", "lane", "pcs_apb", "pma_apb";
		status = "disabled";

		usbdp_phy1_dp: dp-port {
			#phy-cells = <0>;
			status = "disabled";
		};

		usbdp_phy1_u3: u3-port {
			#phy-cells = <0>;
			status = "disabled";
		};
	};

	combphy1_ps: phy@fee10000 {
		compatible = "rockchip,rk3588-naneng-combphy";
		reg = <0x0 0xfee10000 0x0 0x100>;
		#phy-cells = <1>;
		clocks = <&cru CLK_REF_PIPE_PHY1>, <&cru PCLK_PCIE_COMBO_PIPE_PHY1>,
			 <&cru PCLK_PHP_ROOT>;
		clock-names = "refclk", "apbclk", "phpclk";
		assigned-clocks = <&cru CLK_REF_PIPE_PHY1>;
		assigned-clock-rates = <100000000>;
		resets = <&cru SRST_P_PCIE2_PHY1>, <&cru SRST_REF_PIPE_PHY1>;
		reset-names = "combphy-apb", "combphy";
		rockchip,pipe-grf = <&php_grf>;
		rockchip,pipe-phy-grf = <&pipe_phy1_grf>;
		rockchip,pcie1ln-sel-bits = <0x100 0 0 0>;
		status = "disabled";
	};

	pcie30phy: phy@fee80000 {
		compatible = "rockchip,rk3588-pcie3-phy";
		reg = <0x0 0xfee80000 0x0 0x20000>;
		#phy-cells = <0>;
		clocks = <&cru PCLK_PCIE_COMBO_PIPE_PHY>;
		clock-names = "pclk";
		resets = <&cru SRST_PCIE30_PHY>;
		reset-names = "phy";
		rockchip,pipe-grf = <&php_grf>;
		rockchip,phy-grf = <&pcie30_phy_grf>;
		status = "disabled";
	};

};

&display_subsystem {
	route {
		route_dp1: route-dp1 {
			status = "disabled";
			logo,uboot = "logo.bmp";
			logo,kernel = "logo_kernel.bmp";
			logo,mode = "center";
			charge_logo,mode = "center";
			connect = <&vp1_out_dp1>;
		};

		route_hdmi1: route-hdmi1 {
			status = "disabled";
			logo,uboot = "logo.bmp";
			logo,kernel = "logo_kernel.bmp";
			logo,mode = "center";
			charge_logo,mode = "center";
			connect = <&vp1_out_hdmi1>;
		};
	};
};

&vp0 {
	vp0_out_dp1: endpoint@3 {
		reg = <3>;
		remote-endpoint = <&dp1_in_vp0>;
	};

	vp0_out_edp1: endpoint@4 {
		reg = <4>;
		remote-endpoint = <&edp1_in_vp0>;
	};

	vp0_out_hdmi1: endpoint@5 {
		reg = <5>;
		remote-endpoint = <&hdmi1_in_vp0>;
	};
};

&vp1 {
	vp1_out_dp1: endpoint@3 {
		reg = <3>;
		remote-endpoint = <&dp1_in_vp1>;
	};

	vp1_out_edp1: endpoint@4 {
		reg = <4>;
		remote-endpoint = <&edp1_in_vp1>;
	};

	vp1_out_hdmi1: endpoint@5 {
		reg = <5>;
		remote-endpoint = <&hdmi1_in_vp1>;
	};
};

&vp2 {
	vp2_out_dp1: endpoint@5 {
		reg = <5>;
		remote-endpoint = <&dp1_in_vp2>;
	};

	vp2_out_edp1: endpoint@6 {
		reg = <6>;
		remote-endpoint = <&edp1_in_vp2>;
	};

	vp2_out_hdmi1: endpoint@7 {
		reg = <7>;
		remote-endpoint = <&hdmi1_in_vp2>;
	};
};
