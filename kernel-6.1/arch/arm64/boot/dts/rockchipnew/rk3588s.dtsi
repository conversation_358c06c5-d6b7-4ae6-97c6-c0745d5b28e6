// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 */

#include <dt-bindings/clock/rk3588-cru.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/phy/phy.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/power/rk3588-power.h>
#include <dt-bindings/soc/rockchip,boot-mode.h>
#include <dt-bindings/soc/rockchip-system-status.h>
#include <dt-bindings/suspend/rockchip-rk3588.h>
#include <dt-bindings/thermal/thermal.h>

/ {
	compatible = "rockchip,rk3588";

	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		csi2dcphy0 = &csi2_dcphy0;
		csi2dcphy1 = &csi2_dcphy1;
		csi2dphy0 = &csi2_dphy0;
		csi2dphy1 = &csi2_dphy1;
		csi2dphy2 = &csi2_dphy2;
		csi2dphy3 = &csi2_dphy3;
		csi2dphy4 = &csi2_dphy4;
		csi2dphy5 = &csi2_dphy5;
		dsi0 = &dsi0;
		dsi1 = &dsi1;
		ethernet1 = &gmac1;
		gpio0 = &gpio0;
		gpio1 = &gpio1;
		gpio2 = &gpio2;
		gpio3 = &gpio3;
		gpio4 = &gpio4;
		i2c0 = &i2c0;
		i2c1 = &i2c1;
		i2c2 = &i2c2;
		i2c3 = &i2c3;
		i2c4 = &i2c4;
		i2c5 = &i2c5;
		i2c6 = &i2c6;
		i2c7 = &i2c7;
		i2c8 = &i2c8;
		rkcif_mipi_lvds0= &rkcif_mipi_lvds;
		rkcif_mipi_lvds1= &rkcif_mipi_lvds1;
		rkcif_mipi_lvds2= &rkcif_mipi_lvds2;
		rkcif_mipi_lvds3= &rkcif_mipi_lvds3;
		rkcif_mipi_lvds4= &rkcif_mipi_lvds4;
		rkcif_mipi_lvds5= &rkcif_mipi_lvds5;
		rkvdec0 = &rkvdec0;
		rkvdec1 = &rkvdec1;
		rkvenc0 = &rkvenc0;
		rkvenc1 = &rkvenc1;
		jpege0 = &jpege0;
		jpege1 = &jpege1;
		jpege2 = &jpege2;
		jpege3 = &jpege3;
		serial0 = &uart0;
		serial1 = &uart1;
		serial2 = &uart2;
		serial3 = &uart3;
		serial4 = &uart4;
		serial5 = &uart5;
		serial6 = &uart6;
		serial7 = &uart7;
		serial8 = &uart8;
		serial9 = &uart9;
		spi0 = &spi0;
		spi1 = &spi1;
		spi2 = &spi2;
		spi3 = &spi3;
		spi4 = &spi4;
		spi5 = &sfc;
		hdcp0 = &hdcp0;
		hdcp1 = &hdcp1;
	};

	clocks {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		spll: spll {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <702000000>;
			clock-output-names = "spll";
		};

		xin32k: xin32k {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <32768>;
			clock-output-names = "xin32k";
		};

		xin24m: xin24m {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <24000000>;
			clock-output-names = "xin24m";
		};

		hclk_vo1: hclk_vo1@fd7c08ec {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08ec 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_VO1USB_TOP_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_vdpu_low_pre: aclk_vdpu_low_pre@fd7c08b0 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08b0 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_vo0: hclk_vo0@fd7c08dc {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08dc 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_VOP_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_usb: hclk_usb@fd7c08a8 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08a8 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_VO1USB_TOP_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_nvm: hclk_nvm@fd7c087c {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c087c 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_NVM_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_usb: aclk_usb@fd7c08a8 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08a8 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VO1USB_TOP_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_isp1_pre: hclk_isp1_pre@fd7c0868 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c0868 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_VI_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_isp1_pre: aclk_isp1_pre@fd7c0868 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c0868 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VI_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_rkvdec0_pre: aclk_rkvdec0_pre@fd7c08a0 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08a0 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_rkvdec0_pre: hclk_rkvdec0_pre@fd7c08a0 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08a0 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_rkvdec1_pre: aclk_rkvdec1_pre@fd7c08a4 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08a4 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_rkvdec1_pre: hclk_rkvdec1_pre@fd7c08a4 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08a4 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_jpeg_decoder_pre: aclk_jpeg_decoder_pre@fd7c08b0 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08b0 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_rkvenc1_pre: aclk_rkvenc1_pre@fd7c08c0 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08c0 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_RKVENC0>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_rkvenc1_pre: hclk_rkvenc1_pre@fd7c08c0 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08c0 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_RKVENC0>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_hdcp0_pre: aclk_hdcp0_pre@fd7c08dc {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08dc 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VOP_LOW_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_hdcp1_pre: aclk_hdcp1_pre@fd7c08ec {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c08ec 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VO1USB_TOP_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		pclk_av1_pre: pclk_av1_pre@fd7c0910 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c0910 0 0x10>;
			clock-names = "link";
			clocks = <&cru HCLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		aclk_av1_pre: aclk_av1_pre@fd7c0910 {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c0910 0 0x10>;
			clock-names = "link";
			clocks = <&cru ACLK_VDPU_ROOT>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		hclk_sdio_pre: hclk_sdio_pre@fd7c092c {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0 0xfd7c092c 0 0x10>;
			clock-names = "link";
			clocks = <&hclk_nvm>;
			#power-domain-cells = <1>;
			#clock-cells = <0>;
		};

		pclk_vo0_grf: pclk_vo0_grf@fd7c08dc {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0x0 0xfd7c08dc 0x0 0x4>;
			clocks = <&hclk_vo0>;
			clock-names = "link";
			#clock-cells = <0>;
		};

		pclk_vo1_grf: pclk_vo1_grf@fd7c08ec {
			compatible = "rockchip,rk3588-clock-gate-link";
			reg = <0x0 0xfd7c08ec 0x0 0x4>;
			clocks = <&hclk_vo1>;
			clock-names = "link";
			#clock-cells = <0>;
		};

		mclkin_i2s0: mclkin-i2s0 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
			clock-output-names = "i2s0_mclkin";
		};

		mclkin_i2s1: mclkin-i2s1 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
			clock-output-names = "i2s1_mclkin";
		};

		mclkin_i2s2: mclkin-i2s2 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
			clock-output-names = "i2s2_mclkin";
		};

		mclkin_i2s3: mclkin-i2s3 {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <0>;
			clock-output-names = "i2s3_mclkin";
		};

		mclkout_i2s0: mclkout-i2s0@fd58c318 {
			compatible = "rockchip,clk-out";
			reg = <0 0xfd58c318 0 0x4>;
			clocks = <&cru I2S0_8CH_MCLKOUT>;
			#clock-cells = <0>;
			clock-output-names = "i2s0_mclkout_to_io";
			power-domains = <&power RK3588_PD_AUDIO>;
			rockchip,bit-shift = <0>;
			rockchip,bit-set-to-disable;
		};

		mclkout_i2s1: mclkout-i2s1@fd58c318 {
			compatible = "rockchip,clk-out";
			reg = <0 0xfd58c318 0 0x4>;
			clocks = <&cru I2S1_8CH_MCLKOUT>;
			#clock-cells = <0>;
			clock-output-names = "i2s1_mclkout_to_io";
			rockchip,bit-shift = <1>;
			rockchip,bit-set-to-disable;
		};

		mclkout_i2s1m1: mclkout-i2s1@fd58a000 {
			compatible = "rockchip,clk-out";
			reg = <0 0xfd58a000 0 0x4>;
			clocks = <&cru I2S1_8CH_MCLKOUT>;
			#clock-cells = <0>;
			clock-output-names = "i2s1m1_mclkout_to_io";
			rockchip,bit-shift = <6>;
		};

		mclkout_i2s2: mclkout-i2s2@fd58c318 {
			compatible = "rockchip,clk-out";
			reg = <0 0xfd58c318 0 0x4>;
			clocks = <&cru I2S2_2CH_MCLKOUT>;
			#clock-cells = <0>;
			clock-output-names = "i2s2_mclkout_to_io";
			power-domains = <&power RK3588_PD_AUDIO>;
			rockchip,bit-shift = <2>;
			rockchip,bit-set-to-disable;
		};

		mclkout_i2s3: mclkout-i2s3@fd58c318 {
			compatible = "rockchip,clk-out";
			reg = <0 0xfd58c318 0 0x4>;
			clocks = <&cru I2S3_2CH_MCLKOUT>;
			#clock-cells = <0>;
			clock-output-names = "i2s3_mclkout_to_io";
			power-domains = <&power RK3588_PD_AUDIO>;
			rockchip,bit-shift = <7>;
			rockchip,bit-set-to-disable;
		};
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&cpu_l0>;
				};
				core1 {
					cpu = <&cpu_l1>;
				};
				core2 {
					cpu = <&cpu_l2>;
				};
				core3 {
					cpu = <&cpu_l3>;
				};
			};
			cluster1 {
				core0 {
					cpu = <&cpu_b0>;
				};
				core1 {
					cpu = <&cpu_b1>;
				};
			};
			cluster2 {
				core0 {
					cpu = <&cpu_b2>;
				};
				core1 {
					cpu = <&cpu_b3>;
				};
			};
		};

		cpu_l0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x0>;
			enable-method = "psci";
			capacity-dmips-mhz = <530>;
			clocks = <&scmi_clk SCMI_CLK_CPUL>;
			operating-points-v2 = <&cluster0_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l0>;
			#cooling-cells = <2>;
			dynamic-power-coefficient = <100>;
		};

		cpu_l1: cpu@100 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x100>;
			enable-method = "psci";
			capacity-dmips-mhz = <530>;
			clocks = <&scmi_clk SCMI_CLK_CPUL>;
			operating-points-v2 = <&cluster0_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l1>;
		};

		cpu_l2: cpu@200 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x200>;
			enable-method = "psci";
			capacity-dmips-mhz = <530>;
			clocks = <&scmi_clk SCMI_CLK_CPUL>;
			operating-points-v2 = <&cluster0_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l2>;
		};

		cpu_l3: cpu@300 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x300>;
			enable-method = "psci";
			capacity-dmips-mhz = <530>;
			clocks = <&scmi_clk SCMI_CLK_CPUL>;
			operating-points-v2 = <&cluster0_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l3>;
		};

		cpu_b0: cpu@400 {
			device_type = "cpu";
			compatible = "arm,cortex-a76";
			reg = <0x400>;
			enable-method = "psci";
			capacity-dmips-mhz = <1024>;
			clocks = <&scmi_clk SCMI_CLK_CPUB01>;
			operating-points-v2 = <&cluster1_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <65536>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <65536>;
			d-cache-line-size = <64>;
			d-cache-sets = <256>;
			next-level-cache = <&l2_cache_b0>;
			#cooling-cells = <2>;
			dynamic-power-coefficient = <300>;
		};

		cpu_b1: cpu@500 {
			device_type = "cpu";
			compatible = "arm,cortex-a76";
			reg = <0x500>;
			enable-method = "psci";
			capacity-dmips-mhz = <1024>;
			clocks = <&scmi_clk SCMI_CLK_CPUB01>;
			operating-points-v2 = <&cluster1_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <65536>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <65536>;
			d-cache-line-size = <64>;
			d-cache-sets = <256>;
			next-level-cache = <&l2_cache_b1>;
		};

		cpu_b2: cpu@600 {
			device_type = "cpu";
			compatible = "arm,cortex-a76";
			reg = <0x600>;
			enable-method = "psci";
			capacity-dmips-mhz = <1024>;
			clocks = <&scmi_clk SCMI_CLK_CPUB23>;
			operating-points-v2 = <&cluster2_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <65536>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <65536>;
			d-cache-line-size = <64>;
			d-cache-sets = <256>;
			next-level-cache = <&l2_cache_b2>;
			#cooling-cells = <2>;
			dynamic-power-coefficient = <300>;
		};

		cpu_b3: cpu@700 {
			device_type = "cpu";
			compatible = "arm,cortex-a76";
			reg = <0x700>;
			enable-method = "psci";
			capacity-dmips-mhz = <1024>;
			clocks = <&scmi_clk SCMI_CLK_CPUB23>;
			operating-points-v2 = <&cluster2_opp_table>;
			cpu-idle-states = <&CPU_SLEEP>;
			i-cache-size = <65536>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <65536>;
			d-cache-line-size = <64>;
			d-cache-sets = <256>;
			next-level-cache = <&l2_cache_b3>;
		};

		idle-states {
			entry-method = "psci";
			CPU_SLEEP: cpu-sleep {
				compatible = "arm,idle-state";
				local-timer-stop;
				arm,psci-suspend-param = <0x0010000>;
				entry-latency-us = <100>;
				exit-latency-us = <120>;
				min-residency-us = <1000>;
			};
		};

		l2_cache_l0: l2-cache-l0 {
			compatible = "cache";
			cache-size = <131072>;
			cache-line-size = <64>;
			cache-sets = <512>;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l1: l2-cache-l1 {
			compatible = "cache";
			cache-size = <131072>;
			cache-line-size = <64>;
			cache-sets = <512>;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l2: l2-cache-l2 {
			compatible = "cache";
			cache-size = <131072>;
			cache-line-size = <64>;
			cache-sets = <512>;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l3: l2-cache-l3 {
			compatible = "cache";
			cache-size = <131072>;
			cache-line-size = <64>;
			cache-sets = <512>;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_b0: l2-cache-b0 {
			compatible = "cache";
			cache-size = <524288>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_b1: l2-cache-b1 {
			compatible = "cache";
			cache-size = <524288>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_b2: l2-cache-b2 {
			compatible = "cache";
			cache-size = <524288>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_b3: l2-cache-b3 {
			compatible = "cache";
			cache-size = <524288>;
			cache-line-size = <64>;
			cache-sets = <1024>;
			next-level-cache = <&l3_cache>;
		};

		l3_cache: l3-cache {
			compatible = "cache";
			cache-size = <3145728>;
			cache-line-size = <64>;
			cache-sets = <4096>;
		};
	};

	cluster0_opp_table: cluster0-opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		nvmem-cells = <&cpul_leakage>, <&cpul_opp_info>,
			      <&specification_serial_number>, <&customer_demand>;
		nvmem-cell-names = "leakage", "opp-info",
				   "specification_serial_number", "customer_demand";
		rockchip,supported-hw;
		rockchip,opp-shared-dsu;

		rockchip,pvtm-hw = <0x06>;
		rockchip,pvtm-voltage-sel-hw = <
			0	1365	0
			1366	1387	1
			1388	1409	2
			1410	1431	3
			1432	1453	4
			1454	1475	5
			1476	9999	6
		>;
		rockchip,pvtm-voltage-sel-B4 = <
			0	1330	0
			1331	1365	1
			1366	1390	2
			1391	1410	3
			1411	1434	4
			1435	1458	5
			1459	1482	6
			1483	1506	7
			1507	1530	8
			1531	9999	9
		>;
		rockchip,pvtm-voltage-sel = <
			0	1410	0
			1411	1434	1
			1435	1458	2
			1459	1482	3
			1483	1506	4
			1507	1530	5
			1531	9999	6
		>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x64>;
		rockchip,pvtm-sample-time = <1100>;
		rockchip,pvtm-freq = <1416000>;
		rockchip,pvtm-volt = <750000>;
		rockchip,pvtm-ref-temp = <25>;
		rockchip,pvtm-temp-prop = <244 244>;
		rockchip,pvtm-thermal-zone = "soc-thermal";

		rockchip,grf = <&litcore_grf>;
		rockchip,dsu-grf = <&dsu_grf>;
		volt-mem-read-margin = <
			855000	1
			765000	2
			675000	3
			495000	4
		>;
		low-volt-mem-read-margin = <4>;
		intermediate-threshold-freq = <1008000>;	/* KHz */
		rockchip,reboot-freq = <1416000>;		/* KHz */

		rockchip,temp-hysteresis = <5000>;
		rockchip,low-temp = <15000>;
		rockchip,low-temp-min-volt = <800000>;
		rockchip,high-temp = <85000>;
		rockchip,high-temp-max-freq = <1608000>;

		/* RK3588 cluster0 OPPs */
		opp-408000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-600000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-816000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-1008000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-1200000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <712500 712500 950000>,
					<712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L3 = <687500 687500 950000>,
					   <687500 687500 950000>;
			opp-microvolt-L4 = <675000 675000 950000>,
					   <675000 675000 950000>;
			opp-microvolt-L5 = <675000 675000 950000>,
					   <675000 675000 950000>;
			opp-microvolt-L6 = <675000 675000 950000>,
					   <675000 675000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-1416000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <762500 762500 950000>,
					<762500 762500 950000>;
			opp-microvolt-L1 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L2 = <737500 737500 950000>,
					   <737500 737500 950000>;
			opp-microvolt-L3 = <725000 725000 950000>,
					   <725000 725000 950000>;
			opp-microvolt-L4 = <725000 725000 950000>,
					   <725000 725000 950000>;
			opp-microvolt-L5 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L6 = <712500 712500 950000>,
					   <712500 712500 950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-1608000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <850000 850000 950000>,
					<850000 850000 950000>;
			opp-microvolt-L1 = <837500 837500 950000>,
					   <837500 837500 950000>;
			opp-microvolt-L2 = <825000 825000 950000>,
					   <825000 825000 950000>;
			opp-microvolt-L3 = <812500 812500 950000>,
					   <812500 812500 950000>;
			opp-microvolt-L4 = <800000 800000 950000>,
					   <800000 800000 950000>;
			opp-microvolt-L5 = <800000 800000 950000>,
					   <800000 800000 950000>;
			opp-microvolt-L6 = <787500 787500 950000>,
					   <787500 787500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-1800000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <950000 950000 950000>,
					<950000 950000 950000>;
			opp-microvolt-L1 = <937500 937500 950000>,
					   <937500 937500 950000>;
			opp-microvolt-L2 = <925000 925000 950000>,
					   <925000 925000 950000>;
			opp-microvolt-L3 = <912500 912500 950000>,
					   <912500 912500 950000>;
			opp-microvolt-L4 = <900000 900000 950000>,
					   <900000 900000 950000>;
			opp-microvolt-L5 = <887500 887500 950000>,
					   <887500 887500 950000>;
			opp-microvolt-L6 = <875000 875000 950000>,
					   <875000 875000 950000>;
			clock-latency-ns = <40000>;
		};

		opp-b-408000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-600000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-816000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1008000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1200000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			opp-microvolt-L1 = <737500 737500 950000>,
					   <737500 737500 950000>;
			opp-microvolt-L2 = <725000 725000 950000>,
					   <725000 725000 950000>;
			opp-microvolt-L3 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L4 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L5 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L6 = <687500 687500 950000>,
					   <687500 687500 950000>;
			opp-microvolt-L7 = <675000 675000 950000>,
					   <675000 675000 950000>;
			opp-microvolt-L8 = <675000 675000 950000>,
					   <675000 675000 950000>;
			opp-microvolt-L9 = <675000 675000 950000>,
					   <675000 675000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1416000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <800000 800000 950000>,
					<800000 800000 950000>;
			opp-microvolt-L1 = <787500 787500 950000>,
					   <787500 787500 950000>;
			opp-microvolt-L2 = <775000 775000 950000>,
					   <775000 775000 950000>;
			opp-microvolt-L3 = <762500 762500 950000>,
					   <762500 762500 950000>;
			opp-microvolt-L4 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L5 = <737500 737500 950000>,
					   <737500 737500 950000>;
			opp-microvolt-L6 = <725000 725000 950000>,
					   <725000 725000 950000>;
			opp-microvolt-L7 = <725000 725000 950000>,
					   <725000 725000 950000>;
			opp-microvolt-L8 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L9 = <712500 712500 950000>,
					   <712500 712500 950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-b-1608000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <887500 887500 950000>,
					<887500 887500 950000>;
			opp-microvolt-L1 = <875000 875000 950000>,
					   <875000 875000 950000>;
			opp-microvolt-L2 = <862500 862500 950000>,
					   <862500 862500 950000>;
			opp-microvolt-L3 = <850000 850000 950000>,
					   <850000 850000 950000>;
			opp-microvolt-L4 = <837500 837500 950000>,
					   <837500 837500 950000>;
			opp-microvolt-L5 = <825000 825000 950000>,
					   <825000 825000 950000>;
			opp-microvolt-L6 = <812500 812500 950000>,
					   <812500 812500 950000>;
			opp-microvolt-L7 = <800000 800000 950000>,
					   <800000 800000 950000>;
			opp-microvolt-L8 = <800000 800000 950000>,
					   <800000 800000 950000>;
			opp-microvolt-L9 = <787500 787500 950000>,
					   <787500 787500 950000>;
			clock-latency-ns = <40000>;
		};

		/* RK3588J/M cluster0 OPPs */
		opp-j-m-408000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-600000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-816000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1008000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1200000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-1296000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = /bits/ 64 <1296000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			opp-microvolt-L0 = <775000 775000 950000>,
					   <775000 775000 950000>;
			opp-microvolt-L1 = <762500 762500 950000>,
					   <762500 762500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1416000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			opp-microvolt-L0 = <787500 787500 950000>,
					   <787500 787500 950000>;
			opp-microvolt-L1 = <775000 775000 950000>,
					   <775000 775000 950000>;
			opp-microvolt-L2 = <762500 762500 950000>,
					   <762500 762500 950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-j-m-1608000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <887500 887500 950000>,
					<887500 887500 950000>;
			opp-microvolt-L1 = <875000 875000 950000>,
					   <875000 875000 950000>;
			opp-microvolt-L2 = <862500 862500 950000>,
					   <862500 862500 950000>;
			opp-microvolt-L3 = <850000 850000 950000>,
					   <850000 850000 950000>;
			opp-microvolt-L4 = <837500 837500 950000>,
					   <837500 837500 950000>;
			opp-microvolt-L5 = <825000 825000 950000>,
					   <825000 825000 950000>;
			opp-microvolt-L6 = <812500 812500 950000>,
					   <812500 812500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1704000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1704000000>;
			opp-microvolt = <937500 937500 950000>,
					<937500 937500 950000>;
			opp-microvolt-L1 = <925000 925000 950000>,
					   <925000 925000 950000>;
			opp-microvolt-L2 = <912500 912500 950000>,
					   <912500 912500 950000>;
			opp-microvolt-L3 = <900000 900000 950000>,
					   <900000 900000 950000>;
			opp-microvolt-L4 = <887500 887500 950000>,
					   <887500 887500 950000>;
			opp-microvolt-L5 = <875000 875000 950000>,
					   <875000 875000 950000>;
			opp-microvolt-L6 = <862500 862500 950000>,
					   <862500 862500 950000>;
			clock-latency-ns = <40000>;
		};
	};

	cluster1_opp_table: cluster1-opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		nvmem-cells = <&cpub0_leakage>, <&cpub01_opp_info>,
			      <&specification_serial_number>, <&customer_demand>;
		nvmem-cell-names = "leakage", "opp-info",
				   "specification_serial_number", "customer_demand";
		rockchip,supported-hw;

		rockchip,pvtm-hw = <0x06>;
		rockchip,pvtm-voltage-sel-hw = <
			0	1539	0
			1540	1564	1
			1565	1589	2
			1590	1614	3
			1615	1644	4
			1645	1674	5
			1675	1704	6
			1705	9999	7
		>;
		rockchip,pvtm-voltage-sel-B4 = <
			0	1525	0
			1526	1545	1
			1546	1565	2
			1566	1595	3
			1596	1615	4
			1616	1640	5
			1641	1675	6
			1676	1710	7
			1711	1743	8
			1744	1776	9
			1777	9999	10
		>;
		rockchip,pvtm-voltage-sel = <
			0	1595	0
			1596	1615	1
			1616	1640	2
			1641	1675	3
			1676	1710	4
			1711	1743	5
			1744	1776	6
			1777	9999	7
		>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x18>;
		rockchip,pvtm-sample-time = <1100>;
		rockchip,pvtm-freq = <1608000>;
		rockchip,pvtm-volt = <750000>;
		rockchip,pvtm-ref-temp = <25>;
		rockchip,pvtm-temp-prop = <270 270>;
		rockchip,pvtm-thermal-zone = "soc-thermal";
		rockchip,pvtm-low-len-sel = <3>;

		rockchip,grf = <&bigcore0_grf>;
		volt-mem-read-margin = <
			855000	1
			765000	2
			675000	3
			495000	4
		>;
		low-volt-mem-read-margin = <4>;
		intermediate-threshold-freq = <1008000>;	/* KHz */
		rockchip,idle-threshold-freq = <2208000>;	/* KHz */
		rockchip,reboot-freq = <1800000>;		/* KHz */

		rockchip,temp-hysteresis = <5000>;
		rockchip,low-temp = <15000>;
		rockchip,low-temp-min-volt = <800000>;
		rockchip,high-temp = <85000>;
		rockchip,high-temp-max-freq = <2208000>;

		/* RK3588 cluster1 OPPs */
		opp-408000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-600000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-816000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1008000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1200000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1416000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <725000 725000 1000000>,
					<725000 725000 1000000>;
			opp-microvolt-L2 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L3 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L4 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L5 = <687500 687500 1000000>,
					   <687500 687500 1000000>;
			opp-microvolt-L6 = <675000 675000 1000000>,
					   <675000 675000 1000000>;
			opp-microvolt-L7 = <675000 675000 1000000>,
					   <675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1608000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <762500 762500 1000000>,
					<762500 762500 1000000>;
			opp-microvolt-L2 = <750000 750000 1000000>,
					   <750000 750000 1000000>;
			opp-microvolt-L3 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L4 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L5 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L6 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L7 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1800000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <850000 850000 1000000>,
					<850000 850000 1000000>;
			opp-microvolt-L1 = <837500 837500 1000000>,
					   <837500 837500 1000000>;
			opp-microvolt-L2 = <825000 825000 1000000>,
					   <825000 825000 1000000>;
			opp-microvolt-L3 = <812500 812500 1000000>,
					   <812500 812500 1000000>;
			opp-microvolt-L4 = <800000 800000 1000000>,
					   <800000 800000 1000000>;
			opp-microvolt-L5 = <787500 787500 1000000>,
					   <787500 787500 1000000>;
			opp-microvolt-L6 = <775000 775000 1000000>,
					   <775000 775000 1000000>;
			opp-microvolt-L7 = <762500 762500 1000000>,
					   <762500 762500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2016000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <2016000000>;
			opp-microvolt = <925000 925000 1000000>,
					<925000 925000 1000000>;
			opp-microvolt-L1 = <912500 912500 1000000>,
					   <912500 912500 1000000>;
			opp-microvolt-L2 = <900000 900000 1000000>,
					   <900000 900000 1000000>;
			opp-microvolt-L3 = <887500 887500 1000000>,
					   <887500 887500 1000000>;
			opp-microvolt-L4 = <875000 875000 1000000>,
					   <875000 875000 1000000>;
			opp-microvolt-L5 = <862500 862500 1000000>,
					   <862500 862500 1000000>;
			opp-microvolt-L6 = <850000 850000 1000000>,
					   <850000 850000 1000000>;
			opp-microvolt-L7 = <837500 837500 1000000>,
					   <837500 837500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2208000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <2208000000>;
			opp-microvolt = <987500 987500 1000000>,
					<987500 987500 1000000>;
			opp-microvolt-L1 = <975000 975000 1000000>,
					   <975000 975000 1000000>;
			opp-microvolt-L2 = <962500 962500 1000000>,
					   <962500 962500 1000000>;
			opp-microvolt-L3 = <950000 950000 1000000>,
					   <950000 950000 1000000>;
			opp-microvolt-L4 = <962500 962500 1000000>,
					   <962500 962500 1000000>;
			opp-microvolt-L5 = <950000 950000 1000000>,
					   <950000 950000 1000000>;
			opp-microvolt-L6 = <925000 925000 1000000>,
					   <925000 925000 1000000>;
			opp-microvolt-L7 = <912500 912500 1000000>,
					   <912500 912500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2256000000 {
			opp-supported-hw = <0xe9 0x13>;
			opp-hz = /bits/ 64 <2256000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2304000000 {
			opp-supported-hw = <0xe9 0x24>;
			opp-hz = /bits/ 64 <2304000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2352000000 {
			opp-supported-hw = <0xe9 0x48>;
			opp-hz = /bits/ 64 <2352000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2400000000 {
			opp-supported-hw = <0xe9 0x80>;
			opp-hz = /bits/ 64 <2400000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};

		opp-b-408000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-b-600000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-816000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1008000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1200000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1416000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <750000 750000 1000000>,
					<750000 750000 1000000>;
			opp-microvolt-L1 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L2 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L3 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L4 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L5 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L6 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L7 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L8 = <687500 687500 1000000>,
					   <687500 687500 1000000>;
			opp-microvolt-L9 = <675000 675000 1000000>,
					   <675000 675000 1000000>;
			opp-microvolt-L10 = <675000 675000 1000000>,
					    <675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1608000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <800000 800000 1000000>,
					<800000 800000 1000000>;
			opp-microvolt-L1 = <787500 787500 1000000>,
					   <787500 787500 1000000>;
			opp-microvolt-L2 = <775000 775000 1000000>,
					   <775000 775000 1000000>;
			opp-microvolt-L3 = <762500 762500 1000000>,
					   <762500 762500 1000000>;
			opp-microvolt-L4 = <762500 762500 1000000>,
					   <762500 762500 1000000>;
			opp-microvolt-L5 = <750000 750000 1000000>,
					   <750000 750000 1000000>;
			opp-microvolt-L6 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L7 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L8 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L9 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L10 = <700000 700000 1000000>,
					    <700000 700000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1800000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <875000 875000 1000000>,
					<875000 875000 1000000>;
			opp-microvolt-L1 = <875000 875000 1000000>,
					   <875000 875000 1000000>;
			opp-microvolt-L2 = <862500 862500 1000000>,
					   <862500 862500 1000000>;
			opp-microvolt-L3 = <850000 850000 1000000>,
					   <850000 850000 1000000>;
			opp-microvolt-L4 = <837500 837500 1000000>,
					   <837500 837500 1000000>;
			opp-microvolt-L5 = <825000 825000 1000000>,
					   <825000 825000 1000000>;
			opp-microvolt-L6 = <812500 812500 1000000>,
					   <812500 812500 1000000>;
			opp-microvolt-L7 = <800000 800000 1000000>,
					   <800000 800000 1000000>;
			opp-microvolt-L8 = <787500 787500 1000000>,
					   <787500 787500 1000000>;
			opp-microvolt-L9 = <775000 775000 1000000>,
					   <775000 775000 1000000>;
			opp-microvolt-L10 = <762500 762500 1000000>,
					    <762500 762500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-b-2016000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <2016000000>;
			opp-microvolt = <962500 962500 1000000>,
					<962500 962500 1000000>;
			opp-microvolt-L1 = <950000 950000 1000000>,
					   <950000 950000 1000000>;
			opp-microvolt-L2 = <937500 937500 1000000>,
					   <937500 937500 1000000>;
			opp-microvolt-L3 = <925000 925000 1000000>,
					   <925000 925000 1000000>;
			opp-microvolt-L4 = <912500 912500 1000000>,
					   <912500 912500 1000000>;
			opp-microvolt-L5 = <900000 900000 1000000>,
					   <900000 900000 1000000>;
			opp-microvolt-L6 = <887500 887500 1000000>,
					   <887500 887500 1000000>;
			opp-microvolt-L7 = <875000 875000 1000000>,
					   <875000 875000 1000000>;
			opp-microvolt-L8 = <862500 862500 1000000>,
					   <862500 862500 1000000>;
			opp-microvolt-L9 = <850000 850000 1000000>,
					   <850000 850000 1000000>;
			opp-microvolt-L10 = <837500 837500 1000000>,
					    <837500 837500 1000000>;
			clock-latency-ns = <40000>;
		};

		/* RK3588J/M cluster1 OPPs */
		opp-j-m-408000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-600000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-816000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1008000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1200000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1416000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			opp-microvolt-L0 = <762500 762500 950000>,
					<762500 762500 950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-j-m-1608000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <787500 787500 950000>,
					<787500 787500 950000>;
			opp-microvolt-L2 = <775000 775000 950000>,
					   <775000 775000 950000>;
			opp-microvolt-L3 = <762500 762500 950000>,
					   <762500 762500 950000>;
			opp-microvolt-L4 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L5 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L6 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L7 = <750000 750000 950000>,
					   <750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1800000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <875000 875000 950000>,
					<875000 875000 950000>;
			opp-microvolt-L1 = <862500 862500 950000>,
					   <862500 862500 950000>;
			opp-microvolt-L2 = <850000 850000 950000>,
					   <850000 850000 950000>;
			opp-microvolt-L3 = <837500 837500 950000>,
					   <837500 837500 950000>;
			opp-microvolt-L4 = <825000 825000 950000>,
					   <825000 825000 950000>;
			opp-microvolt-L5 = <812500 812500 950000>,
					   <812500 812500 950000>;
			opp-microvolt-L6 = <800000 800000 950000>,
					   <800000 800000 950000>;
			opp-microvolt-L7 = <787500 787500 950000>,
					   <787500 787500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-2016000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <2016000000>;
			opp-microvolt = <950000 950000 950000>,
					<950000 950000 950000>;
			opp-microvolt-L1 = <950000 950000 950000>,
					   <950000 950000 950000>;
			opp-microvolt-L2 = <937500 937500 950000>,
					   <937500 937500 950000>;
			opp-microvolt-L3 = <925000 925000 950000>,
					   <925000 925000 950000>;
			opp-microvolt-L4 = <912500 912500 950000>,
					   <912500 912500 950000>;
			opp-microvolt-L5 = <900000 900000 950000>,
					   <900000 900000 950000>;
			opp-microvolt-L6 = <887500 887500 950000>,
					   <887500 887500 950000>;
			opp-microvolt-L7 = <875000 875000 950000>,
					   <875000 875000 950000>;
			clock-latency-ns = <40000>;
		};
	};

	cluster2_opp_table: cluster2-opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		nvmem-cells = <&cpub1_leakage>, <&cpub23_opp_info>,
			      <&specification_serial_number>, <&customer_demand>;
		nvmem-cell-names = "leakage", "opp-info",
				   "specification_serial_number", "customer_demand";
		rockchip,supported-hw;

		rockchip,pvtm-hw = <0x06>;
		rockchip,pvtm-voltage-sel-hw = <
			0	1539	0
			1540	1564	1
			1565	1589	2
			1590	1614	3
			1615	1644	4
			1645	1674	5
			1675	1704	6
			1705	9999	7
		>;
		rockchip,pvtm-voltage-sel-B4 = <
			0	1525	0
			1526	1545	1
			1546	1565	2
			1566	1595	3
			1596	1615	4
			1616	1640	5
			1641	1675	6
			1676	1710	7
			1711	1743	8
			1744	1776	9
			1777	9999	10
		>;
		rockchip,pvtm-voltage-sel = <
			0	1595	0
			1596	1615	1
			1616	1640	2
			1641	1675	3
			1676	1710	4
			1711	1743	5
			1744	1776	6
			1777	9999	7
		>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x18>;
		rockchip,pvtm-sample-time = <1100>;
		rockchip,pvtm-freq = <1608000>;
		rockchip,pvtm-volt = <750000>;
		rockchip,pvtm-ref-temp = <25>;
		rockchip,pvtm-temp-prop = <270 270>;
		rockchip,pvtm-thermal-zone = "soc-thermal";
		rockchip,pvtm-low-len-sel = <3>;

		rockchip,grf = <&bigcore1_grf>;
		volt-mem-read-margin = <
			855000	1
			765000	2
			675000	3
			495000	4
		>;
		low-volt-mem-read-margin = <4>;
		intermediate-threshold-freq = <1008000>;	/* KHz */
		rockchip,idle-threshold-freq = <2208000>;	/* KHz */
		rockchip,reboot-freq = <1800000>;		/* KHz */

		rockchip,temp-hysteresis = <5000>;
		rockchip,low-temp = <15000>;
		rockchip,low-temp-min-volt = <800000>;
		rockchip,high-temp = <85000>;
		rockchip,high-temp-max-freq = <2208000>;

		/* RK3588 cluster2 OPPs */
		opp-408000000 {
			opp-supported-hw = <0xe9 0x0ffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-600000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-816000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1008000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1200000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <675000 675000 1000000>,
					<675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1416000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <725000 725000 1000000>,
					<725000 725000 1000000>;
			opp-microvolt-L2 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L3 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L4 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L5 = <687500 687500 1000000>,
					   <687500 687500 1000000>;
			opp-microvolt-L6 = <675000 675000 1000000>,
					   <675000 675000 1000000>;
			opp-microvolt-L7 = <675000 675000 1000000>,
					   <675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1608000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <762500 762500 1000000>,
					<762500 762500 1000000>;
			opp-microvolt-L2 = <750000 750000 1000000>,
					   <750000 750000 1000000>;
			opp-microvolt-L3 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L4 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L5 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L6 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L7 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-1800000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <850000 850000 1000000>,
					<850000 850000 1000000>;
			opp-microvolt-L1 = <837500 837500 1000000>,
					   <837500 837500 1000000>;
			opp-microvolt-L2 = <825000 825000 1000000>,
					   <825000 825000 1000000>;
			opp-microvolt-L3 = <812500 812500 1000000>,
					   <812500 812500 1000000>;
			opp-microvolt-L4 = <800000 800000 1000000>,
					   <800000 800000 1000000>;
			opp-microvolt-L5 = <787500 787500 1000000>,
					   <787500 787500 1000000>;
			opp-microvolt-L6 = <775000 775000 1000000>,
					   <775000 775000 1000000>;
			opp-microvolt-L7 = <762500 762500 1000000>,
					   <762500 762500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2016000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <2016000000>;
			opp-microvolt = <925000 925000 1000000>,
					<925000 925000 1000000>;
			opp-microvolt-L1 = <912500 912500 1000000>,
					   <912500 912500 1000000>;
			opp-microvolt-L2 = <900000 900000 1000000>,
					   <900000 900000 1000000>;
			opp-microvolt-L3 = <887500 887500 1000000>,
					   <887500 887500 1000000>;
			opp-microvolt-L4 = <875000 875000 1000000>,
					   <875000 875000 1000000>;
			opp-microvolt-L5 = <862500 862500 1000000>,
					   <862500 862500 1000000>;
			opp-microvolt-L6 = <850000 850000 1000000>,
					   <850000 850000 1000000>;
			opp-microvolt-L7 = <837500 837500 1000000>,
					   <837500 837500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2208000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <2208000000>;
			opp-microvolt = <987500 987500 1000000>,
					<987500 987500 1000000>;
			opp-microvolt-L3 = <975000 975000 1000000>,
					   <975000 975000 1000000>;
			opp-microvolt-L4 = <962500 962500 1000000>,
					   <962500 962500 1000000>;
			opp-microvolt-L5 = <950000 950000 1000000>,
					   <950000 950000 1000000>;
			opp-microvolt-L6 = <925000 925000 1000000>,
					   <925000 925000 1000000>;
			opp-microvolt-L7 = <912500 912500 1000000>,
					   <912500 912500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2256000000 {
			opp-supported-hw = <0xe9 0x13>;
			opp-hz = /bits/ 64 <2256000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2304000000 {
			opp-supported-hw = <0xe9 0x24>;
			opp-hz = /bits/ 64 <2304000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2352000000 {
			opp-supported-hw = <0xe9 0x48>;
			opp-hz = /bits/ 64 <2352000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-2400000000 {
			opp-supported-hw = <0xe9 0x80>;
			opp-hz = /bits/ 64 <2400000000>;
			opp-microvolt = <1000000 1000000 1000000>,
					<1000000 1000000 1000000>;
			clock-latency-ns = <40000>;
		};

		opp-b-408000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-b-600000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-816000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1008000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1200000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <675000 675000 950000>,
					<675000 675000 950000>;
			opp-microvolt-L0 = <712500 712500 950000>,
					   <712500 712500 950000>;
			opp-microvolt-L1 = <700000 700000 950000>,
					   <700000 700000 950000>;
			opp-microvolt-L2 = <687500 687500 950000>,
					   <687500 687500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1416000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <750000 750000 1000000>,
					<750000 750000 1000000>;
			opp-microvolt-L1 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L2 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L3 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L4 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L5 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L6 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L7 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L8 = <687500 687500 1000000>,
					   <687500 687500 1000000>;
			opp-microvolt-L9 = <675000 675000 1000000>,
					   <675000 675000 1000000>;
			opp-microvolt-L10 = <675000 675000 1000000>,
					    <675000 675000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1608000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <800000 800000 1000000>,
					<800000 800000 1000000>;
			opp-microvolt-L1 = <787500 787500 1000000>,
					   <787500 787500 1000000>;
			opp-microvolt-L2 = <775000 775000 1000000>,
					   <775000 775000 1000000>;
			opp-microvolt-L3 = <762500 762500 1000000>,
					   <762500 762500 1000000>;
			opp-microvolt-L4 = <762500 762500 1000000>,
					   <762500 762500 1000000>;
			opp-microvolt-L5 = <750000 750000 1000000>,
					   <750000 750000 1000000>;
			opp-microvolt-L6 = <737500 737500 1000000>,
					   <737500 737500 1000000>;
			opp-microvolt-L7 = <725000 725000 1000000>,
					   <725000 725000 1000000>;
			opp-microvolt-L8 = <712500 712500 1000000>,
					   <712500 712500 1000000>;
			opp-microvolt-L9 = <700000 700000 1000000>,
					   <700000 700000 1000000>;
			opp-microvolt-L10 = <700000 700000 1000000>,
					    <700000 700000 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-b-1800000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <875000 875000 1000000>,
					<875000 875000 1000000>;
			opp-microvolt-L1 = <875000 875000 1000000>,
					   <875000 875000 1000000>;
			opp-microvolt-L2 = <862500 862500 1000000>,
					   <862500 862500 1000000>;
			opp-microvolt-L3 = <850000 850000 1000000>,
					   <850000 850000 1000000>;
			opp-microvolt-L4 = <837500 837500 1000000>,
					   <837500 837500 1000000>;
			opp-microvolt-L5 = <825000 825000 1000000>,
					   <825000 825000 1000000>;
			opp-microvolt-L6 = <812500 812500 1000000>,
					   <812500 812500 1000000>;
			opp-microvolt-L7 = <800000 800000 1000000>,
					   <800000 800000 1000000>;
			opp-microvolt-L8 = <787500 787500 1000000>,
					   <787500 787500 1000000>;
			opp-microvolt-L9 = <775000 775000 1000000>,
					   <775000 775000 1000000>;
			opp-microvolt-L10 = <762500 762500 1000000>,
					    <762500 762500 1000000>;
			clock-latency-ns = <40000>;
		};
		opp-b-2016000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <2016000000>;
			opp-microvolt = <962500 962500 1000000>,
					<962500 962500 1000000>;
			opp-microvolt-L1 = <950000 950000 1000000>,
					   <950000 950000 1000000>;
			opp-microvolt-L2 = <937500 937500 1000000>,
					   <937500 937500 1000000>;
			opp-microvolt-L3 = <925000 925000 1000000>,
					   <925000 925000 1000000>;
			opp-microvolt-L4 = <912500 912500 1000000>,
					   <912500 912500 1000000>;
			opp-microvolt-L5 = <900000 900000 1000000>,
					   <900000 900000 1000000>;
			opp-microvolt-L6 = <887500 887500 1000000>,
					   <887500 887500 1000000>;
			opp-microvolt-L7 = <875000 875000 1000000>,
					   <875000 875000 1000000>;
			opp-microvolt-L8 = <862500 862500 1000000>,
					   <862500 862500 1000000>;
			opp-microvolt-L9 = <850000 850000 1000000>,
					   <850000 850000 1000000>;
			opp-microvolt-L10 = <837500 837500 1000000>,
					    <837500 837500 1000000>;
			clock-latency-ns = <40000>;
		};

		/* RK3588J/M cluster2 OPPs */
		opp-j-m-408000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <408000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-600000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-816000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <816000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1008000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1008000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1200000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1416000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1416000000>;
			opp-microvolt = <750000 750000 950000>,
					<750000 750000 950000>;
			opp-microvolt-L0 = <762500 762500 950000>,
					<762500 762500 950000>;
			clock-latency-ns = <40000>;
			opp-suspend;
		};
		opp-j-m-1608000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1608000000>;
			opp-microvolt = <787500 787500 950000>,
					<787500 787500 950000>;
			opp-microvolt-L2 = <775000 775000 950000>,
					   <775000 775000 950000>;
			opp-microvolt-L3 = <762500 762500 950000>,
					   <762500 762500 950000>;
			opp-microvolt-L4 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L5 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L6 = <750000 750000 950000>,
					   <750000 750000 950000>;
			opp-microvolt-L7 = <750000 750000 950000>,
					   <750000 750000 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-1800000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <875000 875000 950000>,
					<875000 875000 950000>;
			opp-microvolt-L1 = <862500 862500 950000>,
					   <862500 862500 950000>;
			opp-microvolt-L2 = <850000 850000 950000>,
					   <850000 850000 950000>;
			opp-microvolt-L3 = <837500 837500 950000>,
					   <837500 837500 950000>;
			opp-microvolt-L4 = <825000 825000 950000>,
					   <825000 825000 950000>;
			opp-microvolt-L5 = <812500 812500 950000>,
					   <812500 812500 950000>;
			opp-microvolt-L6 = <800000 800000 950000>,
					   <800000 800000 950000>;
			opp-microvolt-L7 = <787500 787500 950000>,
					   <787500 787500 950000>;
			clock-latency-ns = <40000>;
		};
		opp-j-m-2016000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <2016000000>;
			opp-microvolt = <950000 950000 950000>,
					<950000 950000 950000>;
			opp-microvolt-L1 = <950000 950000 950000>,
					   <950000 950000 950000>;
			opp-microvolt-L2 = <937500 937500 950000>,
					   <937500 937500 950000>;
			opp-microvolt-L3 = <925000 925000 950000>,
					   <925000 925000 950000>;
			opp-microvolt-L4 = <912500 912500 950000>,
					   <912500 912500 950000>;
			opp-microvolt-L5 = <900000 900000 950000>,
					   <900000 900000 950000>;
			opp-microvolt-L6 = <887500 887500 950000>,
					   <887500 887500 950000>;
			opp-microvolt-L7 = <875000 875000 950000>,
					   <875000 875000 950000>;
			clock-latency-ns = <40000>;
		};
	};

	arm_pmu: arm-pmu {
		compatible = "arm,armv8-pmuv3";
		interrupts = <GIC_PPI 7 IRQ_TYPE_LEVEL_LOW>;
		interrupt-affinity = <&cpu_l0>, <&cpu_l1>, <&cpu_l2>, <&cpu_l3>,
				     <&cpu_b0>, <&cpu_b1>, <&cpu_b2>, <&cpu_b3>;
	};

	cpuinfo {
		compatible = "rockchip,cpuinfo";
		nvmem-cells = <&otp_id>, <&otp_cpu_version>, <&cpu_code>;
		nvmem-cell-names = "id", "cpu-version", "cpu-code";
	};

	csi2_dcphy0: csi2-dcphy0 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	csi2_dcphy1: csi2-dcphy1 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	csi2_dphy0: csi2-dphy0 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	csi2_dphy1: csi2-dphy1 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	csi2_dphy2: csi2-dphy2 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	csi2_dphy3: csi2-dphy3 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	csi2_dphy4: csi2-dphy4 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	csi2_dphy5: csi2-dphy5 {
		compatible = "rockchip,rk3588-csi2-dphy";
		rockchip,hw = <&csi2_dphy0_hw>, <&csi2_dphy1_hw>;
		phys = <&mipidcphy0>, <&mipidcphy1>;
		phy-names = "dcphy0", "dcphy1";
		status = "disabled";
	};

	display_subsystem: display-subsystem {
		compatible = "rockchip,display-subsystem";
		ports = <&vop_out>;

		route {
			route_dp0: route-dp0 {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <&vp1_out_dp0>;
			};

			route_dsi0: route-dsi0 {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <&vp3_out_dsi0>;
			};

			route_dsi1: route-dsi1 {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <&vp3_out_dsi1>;
			};

			route_edp0: route-edp0 {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <&vp2_out_edp0>;
			};

			route_edp1: route-edp1 {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
			};

			route_hdmi0: route-hdmi0 {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <&vp0_out_hdmi0>;
			};

			route_rgb: route-rgb {
				status = "disabled";
				logo,uboot = "logo.bmp";
				logo,kernel = "logo_kernel.bmp";
				logo,mode = "center";
				charge_logo,mode = "center";
				connect = <&vp3_out_rgb>;
			};
		};
	};

	dmc: dmc {
		compatible = "rockchip,rk3588-dmc";
		interrupts = <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "complete";
		devfreq-events = <&dfi>;
		clocks = <&scmi_clk 4>;
		clock-names = "dmc_clk";
		operating-points-v2 = <&dmc_opp_table>;
		upthreshold = <40>;
		downdifferential = <20>;
		system-status-level = <
			/*system status         freq level*/
			SYS_STATUS_NORMAL       DMC_FREQ_LEVEL_MID_HIGH
			SYS_STATUS_REBOOT       DMC_FREQ_LEVEL_HIGH
			SYS_STATUS_SUSPEND      DMC_FREQ_LEVEL_LOW
			SYS_STATUS_VIDEO_4K     DMC_FREQ_LEVEL_MID_HIGH
			SYS_STATUS_VIDEO_4K_10B DMC_FREQ_LEVEL_MID_HIGH
			SYS_STATUS_VIDEO_SVEP   DMC_FREQ_LEVEL_MID_HIGH
			SYS_STATUS_BOOST        DMC_FREQ_LEVEL_HIGH
			SYS_STATUS_ISP          DMC_FREQ_LEVEL_HIGH
			SYS_STATUS_PERFORMANCE  DMC_FREQ_LEVEL_HIGH
			SYS_STATUS_DUALVIEW     DMC_FREQ_LEVEL_HIGH
			SYS_STATUS_HDMIRX       DMC_FREQ_LEVEL_HIGH
			SYS_STATUS_DEEP_SUSPEND DMC_FREQ_LEVEL_HIGH
		>;
		auto-freq-en = <1>;
		status = "disabled";
	};

	dmc_opp_table: dmc-opp-table {
		compatible = "operating-points-v2";

		nvmem-cells = <&log_leakage>, <&dmc_opp_info>,
			      <&specification_serial_number>, <&customer_demand>;
		nvmem-cell-names = "leakage", "opp-info",
				   "specification_serial_number", "customer_demand";
		rockchip,supported-hw;

		rockchip,leakage-voltage-sel = <
			1	31	0
			32	44	1
			45	57	2
			58	254	3
		>;
		rockchip,temp-hysteresis = <5000>;
		rockchip,low-temp = <15000>;
		rockchip,low-temp-min-volt = <750000>;

		/* RK3588 dmc OPPs */
		opp-528000000 {
			opp-supported-hw = <0xf9 0xffff>;
			opp-hz = /bits/ 64 <528000000>;
			opp-microvolt = <675000 675000 875000>,
					<725000 725000 800000>;
			opp-microvolt-L1 = <675000 675000 875000>,
					   <700000 700000 800000>;
			opp-microvolt-L2 = <675000 675000 875000>,
					   <687500 687500 800000>;
			opp-microvolt-L3 = <675000 675000 875000>,
					   <675000 675000 800000>;
		};
		opp-1068000000 {
			opp-supported-hw = <0xf9 0xffff>;
			opp-hz = /bits/ 64 <1068000000>;
			opp-microvolt = <725000 725000 875000>,
					<737500 737500 800000>;
			opp-microvolt-L1 = <700000 700000 875000>,
					   <712500 712500 800000>;
			opp-microvolt-L2 = <675000 675000 875000>,
					   <700000 700000 800000>;
			opp-microvolt-L3 = <675000 675000 875000>,
					   <687500 687500 800000>;
		};
		opp-1560000000 {
			opp-supported-hw = <0xf9 0xffff>;
			opp-hz = /bits/ 64 <1560000000>;
			opp-microvolt = <800000 800000 875000>,
					<750000 750000 800000>;
			opp-microvolt-L1 = <775000 775000 875000>,
					   <725000 725000 800000>;
			opp-microvolt-L2 = <750000 750000 875000>,
					   <712500 712500 800000>;
			opp-microvolt-L3 = <725000 725000 875000>,
					   <700000 700000 800000>;
		};
		opp-2750000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <2750000000>;
			opp-microvolt = <875000 875000 875000>,
					<750000 750000 800000>;
			opp-microvolt-L1 = <850000 850000 875000>,
					   <750000 750000 800000>;
			opp-microvolt-L2 = <837500 837500 875000>,
					   <725000 725000 800000>;
			opp-microvolt-L3 = <825000 820000 875000>,
					   <700000 700000 800000>;
		};

		opp-b-1848000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <1848000000>;
			opp-microvolt = <875000 875000 875000>,
					<750000 750000 750000>;
			opp-microvolt-L1 = <850000 850000 875000>,
					   <750000 750000 750000>;
			opp-microvolt-L2 = <837500 837500 875000>,
					   <725000 725000 750000>;
			opp-microvolt-L3 = <825000 820000 875000>,
					   <700000 700000 750000>;
		};
		/* RK3588J/M dmc OPPs */
		opp-j-m-528000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <528000000>;
			opp-microvolt = <750000 750000 875000>,
					<750000 750000 800000>;
		};
		opp-j-m-1068000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1068000000>;
			opp-microvolt = <750000 750000 875000>,
					<750000 750000 800000>;
		};
		opp-j-m-1560000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <1560000000>;
			opp-microvolt = <800000 800000 875000>,
					<750000 750000 800000>;
			opp-microvolt-L1 = <775000 775000 875000>,
					   <750000 750000 800000>;
			opp-microvolt-L2 = <750000 750000 875000>,
					   <750000 750000 800000>;
			opp-microvolt-L3 = <750000 750000 875000>,
					   <750000 750000 800000>;
		};
		opp-j-m-2750000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <2750000000>;
			opp-microvolt = <875000 875000 875000>,
					<750000 750000 800000>;
			opp-microvolt-L1 = <850000 850000 875000>,
					   <750000 750000 800000>;
			opp-microvolt-L2 = <837500 837500 875000>,
					   <750000 750000 800000>;
			opp-microvolt-L3 = <825000 820000 875000>,
					   <750000 750000 800000>;
		};
	};

	firmware {
		scmi: scmi {
			compatible = "arm,scmi-smc";
			shmem = <&scmi_shmem>;
			arm,smc-id = <0x82000010>;
			#address-cells = <1>;
			#size-cells = <0>;

			scmi_clk: protocol@14 {
				reg = <0x14>;
				#clock-cells = <1>;

				assigned-clocks = <&scmi_clk SCMI_CLK_CPUL>,
						  <&scmi_clk SCMI_CLK_CPUB01>,
						  <&scmi_clk SCMI_CLK_CPUB23>;
				assigned-clock-rates = <816000000>,
						       <816000000>,
						       <816000000>;
			};

			scmi_reset: protocol@16 {
				reg = <0x16>;
				#reset-cells = <1>;
			};
		};

		sdei: sdei {
			compatible = "arm,sdei-1.0";
			method = "smc";
		};
	};

	jpege_ccu: jpege-ccu {
		compatible = "rockchip,vpu-jpege-ccu";
		status = "disabled";
	};

	/omit-if-no-ref/
	mipi_dcphy1: mipi_dcphy0: mipi-dcphy-dummy {
	};

	mipi0_csi2: mipi0-csi2 {
		compatible = "rockchip,rk3588-mipi-csi2";
		rockchip,hw = <&mipi0_csi2_hw>, <&mipi1_csi2_hw>,
			      <&mipi2_csi2_hw>, <&mipi3_csi2_hw>,
			      <&mipi4_csi2_hw>, <&mipi5_csi2_hw>;
		status = "disabled";
	};

	mipi1_csi2: mipi1-csi2 {
		compatible = "rockchip,rk3588-mipi-csi2";
		rockchip,hw = <&mipi0_csi2_hw>, <&mipi1_csi2_hw>,
			      <&mipi2_csi2_hw>, <&mipi3_csi2_hw>,
			      <&mipi4_csi2_hw>, <&mipi5_csi2_hw>;
		status = "disabled";
	};

	mipi2_csi2: mipi2-csi2 {
		compatible = "rockchip,rk3588-mipi-csi2";
		rockchip,hw = <&mipi0_csi2_hw>, <&mipi1_csi2_hw>,
			      <&mipi2_csi2_hw>, <&mipi3_csi2_hw>,
			      <&mipi4_csi2_hw>, <&mipi5_csi2_hw>;
		status = "disabled";
	};

	mipi3_csi2: mipi3-csi2 {
		compatible = "rockchip,rk3588-mipi-csi2";
		rockchip,hw = <&mipi0_csi2_hw>, <&mipi1_csi2_hw>,
			      <&mipi2_csi2_hw>, <&mipi3_csi2_hw>,
			      <&mipi4_csi2_hw>, <&mipi5_csi2_hw>;
		status = "disabled";
	};

	mipi4_csi2: mipi4-csi2 {
		compatible = "rockchip,rk3588-mipi-csi2";
		rockchip,hw = <&mipi0_csi2_hw>, <&mipi1_csi2_hw>,
			      <&mipi2_csi2_hw>, <&mipi3_csi2_hw>,
			      <&mipi4_csi2_hw>, <&mipi5_csi2_hw>;
		status = "disabled";
	};

	mipi5_csi2: mipi5-csi2 {
		compatible = "rockchip,rk3588-mipi-csi2";
		rockchip,hw = <&mipi0_csi2_hw>, <&mipi1_csi2_hw>,
			      <&mipi2_csi2_hw>, <&mipi3_csi2_hw>,
			      <&mipi4_csi2_hw>, <&mipi5_csi2_hw>;
		status = "disabled";
	};

	mpp_srv: mpp-srv {
		compatible = "rockchip,mpp-service";
		rockchip,taskqueue-count = <12>;
		rockchip,resetgroup-count = <1>;
		status = "disabled";
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	rkcif_dvp: rkcif-dvp {
		compatible = "rockchip,rkcif-dvp";
		rockchip,hw = <&rkcif>;
		iommus = <&rkcif_mmu>;
		status = "disabled";
	};

	rkcif_dvp_sditf: rkcif-dvp-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_dvp>;
		status = "disabled";
	};

	rkcif_mipi_lvds: rkcif-mipi-lvds {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <&rkcif>;
		iommus = <&rkcif_mmu>;
		status = "disabled";
	};

	rkcif_mipi_lvds_sditf: rkcif-mipi-lvds-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds>;
		status = "disabled";
	};

	rkcif_mipi_lvds_sditf_vir1: rkcif-mipi-lvds-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds>;
		status = "disabled";
	};

	rkcif_mipi_lvds_sditf_vir2: rkcif-mipi-lvds-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds>;
		status = "disabled";
	};

	rkcif_mipi_lvds_sditf_vir3: rkcif-mipi-lvds-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds>;
		status = "disabled";
	};

	rkcif_mipi_lvds1: rkcif-mipi-lvds1 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <&rkcif>;
		iommus = <&rkcif_mmu>;
		status = "disabled";
	};

	rkcif_mipi_lvds1_sditf: rkcif-mipi-lvds1-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds1>;
		status = "disabled";
	};

	rkcif_mipi_lvds1_sditf_vir1: rkcif-mipi-lvds1-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds1>;
		status = "disabled";
	};

	rkcif_mipi_lvds1_sditf_vir2: rkcif-mipi-lvds1-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds1>;
		status = "disabled";
	};

	rkcif_mipi_lvds1_sditf_vir3: rkcif-mipi-lvds1-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds1>;
		status = "disabled";
	};

	rkcif_mipi_lvds2: rkcif-mipi-lvds2 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <&rkcif>;
		iommus = <&rkcif_mmu>;
		status = "disabled";
	};

	rkcif_mipi_lvds2_sditf: rkcif-mipi-lvds2-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds2>;
		status = "disabled";
	};

	rkcif_mipi_lvds2_sditf_vir1: rkcif-mipi-lvds2-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds2>;
		status = "disabled";
	};

	rkcif_mipi_lvds2_sditf_vir2: rkcif-mipi-lvds2-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds2>;
		status = "disabled";
	};

	rkcif_mipi_lvds2_sditf_vir3: rkcif-mipi-lvds2-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds2>;
		status = "disabled";
	};

	rkcif_mipi_lvds3: rkcif-mipi-lvds3 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <&rkcif>;
		iommus = <&rkcif_mmu>;
		status = "disabled";
	};

	rkcif_mipi_lvds3_sditf: rkcif-mipi-lvds3-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds3>;
		status = "disabled";
	};

	rkcif_mipi_lvds3_sditf_vir1: rkcif-mipi-lvds3-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds3>;
		status = "disabled";
	};

	rkcif_mipi_lvds3_sditf_vir2: rkcif-mipi-lvds3-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds3>;
		status = "disabled";
	};

	rkcif_mipi_lvds3_sditf_vir3: rkcif-mipi-lvds3-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds3>;
		status = "disabled";
	};

	rkcif_mipi_lvds4: rkcif-mipi-lvds4 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <&rkcif>;
		iommus = <&rkcif_mmu>;
		status = "disabled";
	};

	rkcif_mipi_lvds4_sditf: rkcif-mipi-lvds4-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds4>;
		status = "disabled";
	};

	rkcif_mipi_lvds4_sditf_vir1: rkcif-mipi-lvds4-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds4>;
		status = "disabled";
	};

	rkcif_mipi_lvds4_sditf_vir2: rkcif-mipi-lvds4-sditf-vir2 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds4>;
		status = "disabled";
	};

	rkcif_mipi_lvds4_sditf_vir3: rkcif-mipi-lvds4-sditf-vir3 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds4>;
		status = "disabled";
	};

	rkcif_mipi_lvds5: rkcif-mipi-lvds5 {
		compatible = "rockchip,rkcif-mipi-lvds";
		rockchip,hw = <&rkcif>;
		iommus = <&rkcif_mmu>;
		status = "disabled";
	};

	rkcif_mipi_lvds5_sditf: rkcif-mipi-lvds5-sditf {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds5>;
		status = "disabled";
	};

	rkcif_mipi_lvds5_sditf_vir1: rkcif-mipi-lvds5-sditf-vir1 {
		compatible = "rockchip,rkcif-sditf";
		rockchip,cif = <&rkcif_mipi_lvds5>;
		status = "disabled";
	};

	rkisp0_vir0: rkisp0-vir0 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp0>;
		/*
		 * dual isp process image case
		 * other rkisp hw and virtual nodes should disabled
		 * rockchip,hw = <&rkisp_unite>;
		 */
		status = "disabled";
	};

	rkisp0_vir1: rkisp0-vir1 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp0>;
		status = "disabled";
	};

	rkisp0_vir2: rkisp0-vir2 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp0>;
		status = "disabled";
	};

	rkisp0_vir3: rkisp0-vir3 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp0>;
		status = "disabled";
	};

	rkisp1_vir0: rkisp1-vir0 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp1>;
		status = "disabled";
	};

	rkisp1_vir1: rkisp1-vir1 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp1>;
		status = "disabled";
	};

	rkisp1_vir2: rkisp1-vir2 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp1>;
		status = "disabled";
	};

	rkisp1_vir3: rkisp1-vir3 {
		compatible = "rockchip,rkisp-vir";
		rockchip,hw = <&rkisp1>;
		status = "disabled";
	};

	rkispp0_vir0: rkispp0-vir0 {
		compatible = "rockchip,rk3588-rkispp-vir";
		rockchip,hw = <&rkispp0>;
		status = "disabled";
	};

	rkispp1_vir0: rkispp1-vir0 {
		compatible = "rockchip,rk3588-rkispp-vir";
		rockchip,hw = <&rkispp1>;
		status = "disabled";
	};

	rkvenc_ccu: rkvenc-ccu {
		compatible = "rockchip,rkv-encoder-v2-ccu";
		status = "disabled";
	};

	rkvtunnel: rkvtunnel {
		compatible = "rockchip,video-tunnel";
		status = "disabled";
	};

	rockchip_suspend: rockchip-suspend {
		compatible = "rockchip,pm-rk3588";
		status = "disabled";
		rockchip,sleep-debug-en = <0>;
		rockchip,sleep-mode-config = <
			(0
			| RKPM_SLP_ARMOFF_LOGOFF
			| RKPM_SLP_PMU_PMUALIVE_32K
			| RKPM_SLP_PMU_DIS_OSC
			| RKPM_SLP_32K_EXT
			)
		>;
		rockchip,wakeup-config = <
			(0
			| RKPM_GPIO_WKUP_EN
			)
		>;
		power-domains = <&power RK3588_PD_USB>;
	};

	rockchip_system_monitor: rockchip-system-monitor {
		compatible = "rockchip,system-monitor";

		rockchip,thermal-zone = "soc-thermal";
	};

	thermal_zones: thermal-zones {
		soc_thermal: soc-thermal {
			polling-delay-passive = <20>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			sustainable-power = <2100>; /* milliwatts */

			thermal-sensors = <&tsadc 0>;
			trips {
				threshold: trip-point-0 {
					temperature = <75000>;
					hysteresis = <2000>;
					type = "passive";
				};
				target: trip-point-1 {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};
				soc_crit: soc-crit {
					/* millicelsius */
					temperature = <115000>;
					/* millicelsius */
					hysteresis = <2000>;
					type = "critical";
				};
			};
			cooling-maps {
				map0 {
					trip = <&target>;
					cooling-device = <&cpu_l0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
					contribution = <1024>;
				};
				map1 {
					trip = <&target>;
					cooling-device = <&cpu_b0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
					contribution = <1024>;
				};
				map2 {
					trip = <&target>;
					cooling-device = <&cpu_b2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
					contribution = <1024>;
				};
				map3 {
					trip = <&target>;
					cooling-device = <&gpu THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
					contribution = <1024>;
				};
			};
		};

		bigcore0_thermal: bigcore0-thermal {
			polling-delay-passive = <20>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&tsadc 1>;
			trips {
				bigcore0_crit: bigcore0-crit {
					/* millicelsius */
					temperature = <115000>;
					/* millicelsius */
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		bigcore1_thermal: bigcore1-thermal {
			polling-delay-passive = <20>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&tsadc 2>;
			trips {
				bigcore1_crit: bigcore1-crit {
					/* millicelsius */
					temperature = <115000>;
					/* millicelsius */
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		little_core_thermal: littlecore-thermal {
			polling-delay-passive = <20>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&tsadc 3>;
			trips {
				litcore_crit: litcore-crit {
					/* millicelsius */
					temperature = <115000>;
					/* millicelsius */
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		center_thermal: center-thermal {
			polling-delay-passive = <20>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&tsadc 4>;
			trips {
				center_crit: center-crit {
					/* millicelsius */
					temperature = <115000>;
					/* millicelsius */
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		gpu_thermal: gpu-thermal {
			polling-delay-passive = <20>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&tsadc 5>;
			trips {
				gpu_crit: gpu-crit {
					/* millicelsius */
					temperature = <115000>;
					/* millicelsius */
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};

		npu_thermal: npu-thermal {
			polling-delay-passive = <20>; /* milliseconds */
			polling-delay = <1000>; /* milliseconds */
			thermal-sensors = <&tsadc 6>;
			trips {
				npu_crit: npu-crit {
					/* millicelsius */
					temperature = <115000>;
					/* millicelsius */
					hysteresis = <2000>;
					type = "critical";
				};
			};
		};
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
	};

	sram@10f000 {
		compatible = "mmio-sram";
		reg = <0x0 0x0010f000 0x0 0x100>;
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0 0x0 0x0010f000 0x100>;

		scmi_shmem: sram@0 {
			compatible = "arm,scmi-shmem";
			reg = <0x0 0x100>;
		};
	};

	gpu: gpu@fb000000 {
		compatible = "arm,mali-bifrost";
		reg = <0x0 0xfb000000 0x0 0x200000>;
		interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 93 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 92 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "GPU", "MMU", "JOB";

		clocks = <&scmi_clk SCMI_CLK_GPU>, <&cru CLK_GPU_COREGROUP>,
			 <&cru CLK_GPU_STACKS>, <&cru CLK_GPU>;
		clock-names = "clk_mali", "clk_gpu_coregroup",
			      "clk_gpu_stacks", "clk_gpu";
		assigned-clocks = <&scmi_clk SCMI_CLK_GPU>;
		assigned-clock-rates = <200000000>;
		power-domains = <&power RK3588_PD_GPU>;
		operating-points-v2 = <&gpu_opp_table>;
		#cooling-cells = <2>;
		dynamic-power-coefficient = <2982>;

		upthreshold = <30>;
		downdifferential = <10>;

		status = "disabled";
	};

	gpu_opp_table: gpu-opp-table {
		compatible = "operating-points-v2";

		nvmem-cells = <&gpu_leakage>, <&gpu_opp_info>,
			      <&specification_serial_number>, <&customer_demand>;
		nvmem-cell-names = "leakage", "opp-info",
				   "specification_serial_number", "customer_demand";
		rockchip,supported-hw;

		rockchip,pvtm-hw = <0x04>;
		rockchip,pvtm-voltage-sel-hw = <
			0	799	0
			800	819	1
			820	844	2
			845	869	3
			870	894	4
			895	9999	5
		>;
		rockchip,pvtm-voltage-sel-B4 = <
			0	755	0
			756	775	1
			776	795	2
			796	815	3
			816	835	4
			836	860	5
			861	885	6
			886	910	7
			911	9999	8
		>;
		rockchip,pvtm-voltage-sel = <
			0	815	0
			816	835	1
			836	860	2
			861	885	3
			886	910	4
			911	9999	5
		>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x1c>;
		rockchip,pvtm-sample-time = <1100>;
		rockchip,pvtm-freq = <800000>;
		rockchip,pvtm-volt = <750000>;
		rockchip,pvtm-ref-temp = <25>;
		rockchip,pvtm-temp-prop = <(-135) (-135)>;
		rockchip,pvtm-thermal-zone = "gpu-thermal";

		rockchip,opp-clocks = <&cru CLK_GPU>;
		rockchip,grf = <&gpu_grf>;
		volt-mem-read-margin = <
			855000	1
			765000	2
			675000	3
			495000	4
		>;
		low-volt-mem-read-margin = <4>;
		intermediate-threshold-freq = <400000>;	/* KHz */

		rockchip,temp-hysteresis = <5000>;
		rockchip,low-temp = <15000>;
		rockchip,low-temp-min-volt = <750000>;
		rockchip,high-temp = <85000>;
		rockchip,high-temp-max-freq = <800000>;

		/* RK3588 gpu OPPs */
		opp-300000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
		};
		opp-400000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
		};
		opp-500000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
		};
		opp-600000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
		};
		opp-700000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt = <700000 700000 850000>,
					<700000 700000 850000>;
			opp-microvolt-L2 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L3 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L4 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L5 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-800000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
			opp-microvolt-L1 = <737500 737500 850000>,
					   <737500 737500 850000>;
			opp-microvolt-L2 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L3 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L4 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L5 = <700000 700000 850000>,
					   <700000 700000 850000>;
		};
		opp-900000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <900000000>;
			opp-microvolt = <800000 800000 850000>,
					<800000 800000 850000>;
			opp-microvolt-L1 = <787500 787500 850000>,
					   <787500 787500 850000>;
			opp-microvolt-L2 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L3 = <762500 762500 850000>,
					   <762500 762500 850000>;
			opp-microvolt-L4 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L5 = <737500 737500 850000>,
					   <737500 737500 850000>;
		};
		opp-1000000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1000000000>;
			opp-microvolt = <850000 850000 850000>,
					<850000 850000 850000>;
			opp-microvolt-L1 = <837500 837500 850000>,
					   <837500 837500 850000>;
			opp-microvolt-L2 = <825000 825000 850000>,
					   <825000 825000 850000>;
			opp-microvolt-L3 = <812500 812500 850000>,
					   <812500 812500 850000>;
			opp-microvolt-L4 = <800000 800000 850000>,
					   <800000 800000 850000>;
			opp-microvolt-L5 = <787500 787500 850000>,
					   <787500 787500 850000>;
		};

		opp-b-300000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L1 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L2 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-400000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L1 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L2 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-500000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L1 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L2 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-600000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L1 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L2 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-700000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt-L0 = <737500 737500 850000>,
					   <737500 737500 850000>;
			opp-microvolt-L1 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L2 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L3 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L4 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L5 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L6 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L7 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L8 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-b-800000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt-L0 = <787500 787500 850000>,
					   <787500 787500 850000>;
			opp-microvolt-L1 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L2 = <762500 762500 850000>,
					   <762500 762500 850000>;
			opp-microvolt-L3 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L4 = <737500 737500 850000>,
					   <737500 737500 850000>;
			opp-microvolt-L5 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L6 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L7 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L8 = <700000 700000 850000>,
					   <700000 700000 850000>;
		};
		opp-b-950000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <950000000>;
			opp-microvolt = <850000 850000 850000>,
					<850000 850000 850000>;
			opp-microvolt-L2 = <837500 837500 850000>,
					   <837500 837500 850000>;
			opp-microvolt-L3 = <825000 825000 850000>,
					   <825000 825000 850000>;
			opp-microvolt-L4 = <812500 812500 850000>,
					   <812500 812500 850000>;
			opp-microvolt-L5 = <800000 800000 850000>,
					   <800000 800000 850000>;
			opp-microvolt-L6 = <787500 787500 850000>,
					   <787500 787500 850000>;
			opp-microvolt-L7 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L8 = <762500 762500 850000>,
					   <762500 762500 850000>;
		};

		/* RK3588J/M gpu OPPs */
		opp-j-m-300000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-400000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-500000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-600000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-700000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		/* RK3588J gpu OPPs */
		opp-j-850000000 {
			opp-supported-hw = <0x04 0xffff>;
			opp-hz = /bits/ 64 <850000000>;
			opp-microvolt = <787500 787500 850000>,
					<787500 787500 850000>;
			opp-microvolt-L1 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L2 = <762500 762500 850000>,
					   <762500 762500 850000>;
			opp-microvolt-L3 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L4 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L5 = <750000 750000 850000>,
					   <750000 750000 850000>;
		};
		/* RK3588M gpu OPPs */
		opp-m-800000000 {
			opp-supported-hw = <0x02 0xffff>;
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-m-900000000 {
			opp-supported-hw = <0x02 0xffff>;
			opp-hz = /bits/ 64 <900000000>;
			opp-microvolt = <800000 800000 850000>,
					<800000 800000 850000>;
			opp-microvolt-L1 = <787500 787500 850000>,
					   <787500 787500 850000>;
			opp-microvolt-L2 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L3 = <762500 762500 850000>,
					   <762500 762500 850000>;
			opp-microvolt-L4 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L5 = <750000 750000 850000>,
					   <750000 750000 850000>;
		};
		opp-m-1000000000 {
			opp-supported-hw = <0x02 0xffff>;
			opp-hz = /bits/ 64 <1000000000>;
			opp-microvolt = <850000 850000 850000>,
					<850000 850000 850000>;
			opp-microvolt-L1 = <837500 837500 850000>,
					   <837500 837500 850000>;
			opp-microvolt-L2 = <825000 825000 850000>,
					   <825000 825000 850000>;
			opp-microvolt-L3 = <812500 812500 850000>,
					   <812500 812500 850000>;
			opp-microvolt-L4 = <800000 800000 850000>,
					   <800000 800000 850000>;
			opp-microvolt-L5 = <787500 787500 850000>,
					   <787500 787500 850000>;
		};
	};

	usbdrd3_0: usbdrd3_0 {
		compatible = "rockchip,rk3588-dwc3", "rockchip,rk3399-dwc3";
		clocks = <&cru REF_CLK_USB3OTG0>, <&cru SUSPEND_CLK_USB3OTG0>,
			 <&cru ACLK_USB3OTG0>;
		clock-names = "ref", "suspend", "bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		status = "disabled";

		usbdrd_dwc3_0: usb@fc000000 {
			compatible = "snps,dwc3";
			reg = <0x0 0xfc000000 0x0 0x400000>;
			interrupts = <GIC_SPI 220 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&power RK3588_PD_USB>;
			resets = <&cru SRST_A_USB3OTG0>;
			reset-names = "usb3-otg";
			dr_mode = "otg";
			phys = <&u2phy0_otg>, <&usbdp_phy0_u3>;
			phy-names = "usb2-phy", "usb3-phy";
			phy_type = "utmi_wide";
			snps,dis_enblslpm_quirk;
			snps,dis-u1-entry-quirk;
			snps,dis-u2-entry-quirk;
			snps,dis-u2-freeclk-exists-quirk;
			snps,dis-del-phy-power-chg-quirk;
			snps,dis-tx-ipgap-linecheck-quirk;
			snps,parkmode-disable-hs-quirk;
			snps,parkmode-disable-ss-quirk;
			quirk-skip-phy-init;
			status = "disabled";
		};
	};

	usb_host0_ehci: usb@fc800000 {
		compatible = "rockchip,rk3588-ehci", "generic-ehci";
		reg = <0x0 0xfc800000 0x0 0x40000>;
		interrupts = <GIC_SPI 215 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST0>, <&cru HCLK_HOST_ARB0>, <&u2phy2>, <&aclk_usb>;
		clock-names = "usbhost", "arbiter", "utmi", "alk_usb";
		companion = <&usb_host0_ohci>;
		phys = <&u2phy2_host>;
		phy-names = "usb2-phy";
		power-domains = <&power RK3588_PD_USB>;
		status = "disabled";
	};

	usb_host0_ohci: usb@fc840000 {
		compatible = "rockchip,rk3588-ohci", "generic-ohci";
		reg = <0x0 0xfc840000 0x0 0x40000>;
		interrupts = <GIC_SPI 216 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST0>, <&cru HCLK_HOST_ARB0>, <&u2phy2>, <&aclk_usb>;
		clock-names = "usbhost", "arbiter", "utmi", "alk_usb";
		phys = <&u2phy2_host>;
		phy-names = "usb2-phy";
		power-domains = <&power RK3588_PD_USB>;
		status = "disabled";
	};

	usb_host1_ehci: usb@fc880000 {
		compatible = "rockchip,rk3588-ehci", "generic-ehci";
		reg = <0x0 0xfc880000 0x0 0x40000>;
		interrupts = <GIC_SPI 218 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST1>, <&cru HCLK_HOST_ARB1>, <&u2phy3>, <&aclk_usb>;
		clock-names = "usbhost", "arbiter", "utmi", "alk_usb";
		companion = <&usb_host1_ohci>;
		phys = <&u2phy3_host>;
		phy-names = "usb2-phy";
		power-domains = <&power RK3588_PD_USB>;
		status = "disabled";
	};

	usb_host1_ohci: usb@fc8c0000 {
		compatible = "rockchip,rk3588-ohci", "generic-ohci";
		reg = <0x0 0xfc8c0000 0x0 0x40000>;
		interrupts = <GIC_SPI 219 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_HOST1>, <&cru HCLK_HOST_ARB1>, <&u2phy3>, <&aclk_usb>;
		clock-names = "usbhost", "arbiter", "utmi", "alk_usb";
		phys = <&u2phy3_host>;
		phy-names = "usb2-phy";
		power-domains = <&power RK3588_PD_USB>;
		status = "disabled";
	};

	mmu600_pcie: iommu@fc900000 {
		compatible = "arm,smmu-v3";
		reg = <0x0 0xfc900000 0x0 0x200000>;
		interrupts = <GIC_SPI 369 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 371 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 374 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 367 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "eventq", "gerror", "priq", "cmdq-sync";
		#iommu-cells = <1>;
		status = "disabled";
	};

	mmu600_php: iommu@fcb00000 {
		compatible = "arm,smmu-v3";
		reg = <0x0 0xfcb00000 0x0 0x200000>;
		interrupts = <GIC_SPI 381 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 383 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 386 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 379 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "eventq", "gerror", "priq", "cmdq-sync";
		#iommu-cells = <1>;
		status = "disabled";
	};

	usbhost3_0: usbhost3_0 {
		compatible = "rockchip,rk3588-dwc3", "rockchip,rk3399-dwc3";
		clocks = <&cru REF_CLK_USB3OTG2>, <&cru SUSPEND_CLK_USB3OTG2>,
			 <&cru ACLK_USB3OTG2>, <&cru CLK_UTMI_OTG2>,
			 <&cru PCLK_PHP_ROOT>, <&cru CLK_PIPEPHY2_PIPE_U3_G>;
		clock-names = "ref", "suspend", "bus", "utmi", "php", "pipe";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		status = "disabled";

		usbhost_dwc3_0: usb@fcd00000 {
			compatible = "snps,dwc3";
			reg = <0x0 0xfcd00000 0x0 0x400000>;
			interrupts = <GIC_SPI 222 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&cru SRST_A_USB3OTG2>;
			reset-names = "usb3-host";
			dr_mode = "host";
			phys = <&combphy2_psu PHY_TYPE_USB3>;
			phy-names = "usb3-phy";
			phy_type = "utmi_wide";
			snps,dis_enblslpm_quirk;
			snps,dis-u2-freeclk-exists-quirk;
			snps,dis-del-phy-power-chg-quirk;
			snps,dis-tx-ipgap-linecheck-quirk;
			snps,dis_rxdet_inp3_quirk;
			snps,parkmode-disable-hs-quirk;
			snps,parkmode-disable-ss-quirk;
			status = "disabled";
		};
	};

	pmu0_grf: syscon@fd588000 {
		compatible = "rockchip,rk3588-pmu0-grf", "syscon", "simple-mfd";
		reg = <0x0 0xfd588000 0x0 0x2000>;

		reboot_mode: reboot-mode {
			compatible = "syscon-reboot-mode";
			offset = <0x80>;
			mode-bootloader = <BOOT_BL_DOWNLOAD>;
			mode-charge = <BOOT_CHARGING>;
			mode-fastboot = <BOOT_FASTBOOT>;
			mode-loader = <BOOT_BL_DOWNLOAD>;
			mode-normal = <BOOT_NORMAL>;
			mode-recovery = <BOOT_RECOVERY>;
			mode-ums = <BOOT_UMS>;
			mode-panic = <BOOT_PANIC>;
			mode-watchdog = <BOOT_WATCHDOG>;
			mode-quiescent = <BOOT_QUIESCENT>;
			/* add a mode to capture the ramdump through usb */
			mode-winusb = <BOOT_WINUSB>;
		};
	};

	pmu1_grf: syscon@fd58a000 {
		compatible = "rockchip,rk3588-pmu1-grf", "syscon";
		reg = <0x0 0xfd58a000 0x0 0x2000>;
	};

	sys_grf: syscon@fd58c000 {
		compatible = "rockchip,rk3588-sys-grf", "syscon", "simple-mfd";
		reg = <0x0 0xfd58c000 0x0 0x1000>;

		rgb: rgb {
			compatible = "rockchip,rk3588-rgb";
			pinctrl-names = "default";
			pinctrl-0 = <&bt1120_pins>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
					#address-cells = <1>;
					#size-cells = <0>;

					rgb_in_vp3: endpoint@2 {
						reg = <2>;
						remote-endpoint = <&vp3_out_rgb>;
						status = "disabled";
					};
				};
			};
		};
	};

	bigcore0_grf: syscon@fd590000 {
		compatible = "rockchip,rk3588-bigcore0-grf", "syscon";
		reg = <0x0 0xfd590000 0x0 0x100>;
	};

	bigcore1_grf: syscon@fd592000 {
		compatible = "rockchip,rk3588-bigcore1-grf", "syscon";
		reg = <0x0 0xfd592000 0x0 0x100>;
	};

	litcore_grf: syscon@fd594000 {
		compatible = "rockchip,rk3588-litcore-grf", "syscon";
		reg = <0x0 0xfd594000 0x0 0x100>;
	};

	dsu_grf: syscon@fd598000 {
		compatible = "rockchip,rk3588-dsu-grf", "syscon";
		reg = <0x0 0xfd598000 0x0 0x100>;
	};

	gpu_grf: syscon@fd5a0000 {
		compatible = "rockchip,rk3588-gpu-grf", "syscon";
		reg = <0x0 0xfd5a0000 0x0 0x100>;
	};

	npu_grf: syscon@fd5a2000 {
		compatible = "rockchip,rk3588-npu-grf", "syscon";
		reg = <0x0 0xfd5a2000 0x0 0x100>;
	};

	vop_grf: syscon@fd5a4000 {
		compatible = "rockchip,rk3588-vop-grf", "syscon";
		reg = <0x0 0xfd5a4000 0x0 0x2000>;
	};

	vo0_grf: syscon@fd5a6000 {
		compatible = "rockchip,rk3588-vo-grf", "syscon";
		reg = <0x0 0xfd5a6000 0x0 0x2000>;
		clocks = <&pclk_vo0_grf>;
	};

	vo1_grf: syscon@fd5a8000 {
		compatible = "rockchip,rk3588-vo-grf", "syscon";
		reg = <0x0 0xfd5a8000 0x0 0x100>;
		clocks = <&pclk_vo1_grf>;
	};

	usb_grf: syscon@fd5ac000 {
		compatible = "rockchip,rk3588-usb-grf", "syscon";
		reg = <0x0 0xfd5ac000 0x0 0x4000>;
	};

	php_grf: syscon@fd5b0000 {
		compatible = "rockchip,rk3588-php-grf", "syscon";
		reg = <0x0 0xfd5b0000 0x0 0x1000>;
	};

	mipidphy0_grf: syscon@fd5b4000 {
		compatible = "rockchip,mipi-dphy-grf", "syscon";
		reg = <0x0 0xfd5b4000 0x0 0x1000>;
	};

	mipidphy1_grf: syscon@fd5b5000 {
		compatible = "rockchip,mipi-dphy-grf", "syscon";
		reg = <0x0 0xfd5b5000 0x0 0x1000>;
	};

	pipe_phy0_grf: syscon@fd5bc000 {
		compatible = "rockchip,pipe-phy-grf", "syscon";
		reg = <0x0 0xfd5bc000 0x0 0x100>;
	};

	pipe_phy2_grf: syscon@fd5c4000 {
		compatible = "rockchip,pipe-phy-grf", "syscon";
		reg = <0x0 0xfd5c4000 0x0 0x100>;
	};

	usbdpphy0_grf: syscon@fd5c8000 {
		compatible = "rockchip,rk3588-usbdpphy-grf", "syscon";
		reg = <0x0 0xfd5c8000 0x0 0x4000>;
	};

	usb2phy0_grf: syscon@fd5d0000 {
		compatible = "rockchip,rk3588-usb2phy-grf", "syscon",
			     "simple-mfd";
		reg = <0x0 0xfd5d0000 0x0 0x4000>;
		#address-cells = <1>;
		#size-cells = <1>;

		u2phy0: usb2-phy@0 {
			compatible = "rockchip,rk3588-usb2phy";
			reg = <0x0 0x10>;
			interrupts = <GIC_SPI 393 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&cru SRST_OTGPHY_U3_0>, <&cru SRST_P_USB2PHY_U3_0_GRF0>;
			reset-names = "phy", "apb";
			clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>;
			clock-names = "phyclk";
			clock-output-names = "usb480m_phy0";
			#clock-cells = <0>;
			rockchip,usbctrl-grf = <&usb_grf>;
			status = "disabled";

			u2phy0_otg: otg-port {
				#phy-cells = <0>;
				status = "disabled";
			};
		};
	};

	usb2phy2_grf: syscon@fd5d8000 {
		compatible = "rockchip,rk3588-usb2phy-grf", "syscon",
			     "simple-mfd";
		reg = <0x0 0xfd5d8000 0x0 0x4000>;
		#address-cells = <1>;
		#size-cells = <1>;

		u2phy2: usb2-phy@8000 {
			compatible = "rockchip,rk3588-usb2phy";
			reg = <0x8000 0x10>;
			interrupts = <GIC_SPI 391 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&cru SRST_OTGPHY_U2_0>, <&cru SRST_P_USB2PHY_U2_0_GRF0>;
			reset-names = "phy", "apb";
			clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>;
			clock-names = "phyclk";
			clock-output-names = "usb480m_phy2";
			#clock-cells = <0>;
			status = "disabled";

			u2phy2_host: host-port {
				#phy-cells = <0>;
				status = "disabled";
			};
		};
	};

	usb2phy3_grf: syscon@fd5dc000 {
		compatible = "rockchip,rk3588-usb2phy-grf", "syscon",
			     "simple-mfd";
		reg = <0x0 0xfd5dc000 0x0 0x4000>;
		#address-cells = <1>;
		#size-cells = <1>;

		u2phy3: usb2-phy@c000 {
			compatible = "rockchip,rk3588-usb2phy";
			reg = <0xc000 0x10>;
			interrupts = <GIC_SPI 392 IRQ_TYPE_LEVEL_HIGH>;
			resets = <&cru SRST_OTGPHY_U2_1>, <&cru SRST_P_USB2PHY_U2_1_GRF0>;
			reset-names = "phy", "apb";
			clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>;
			clock-names = "phyclk";
			clock-output-names = "usb480m_phy3";
			#clock-cells = <0>;
			status = "disabled";

			u2phy3_host: host-port {
				#phy-cells = <0>;
				status = "disabled";
			};
		};
	};

	hdptxphy0_grf: syscon@fd5e0000 {
		compatible = "rockchip,rk3588-hdptxphy-grf", "syscon";
		reg = <0x0 0xfd5e0000 0x0 0x100>;
	};

	mipidcphy0_grf: syscon@fd5e8000 {
		compatible = "rockchip,mipi-dcphy-grf", "syscon";
		reg = <0x0 0xfd5e8000 0x0 0x4000>;
	};

	mipidcphy1_grf: syscon@fd5ec000 {
		compatible = "rockchip,mipi-dcphy-grf", "syscon";
		reg = <0x0 0xfd5ec000 0x0 0x4000>;
	};

	ioc: syscon@fd5f0000 {
		compatible = "rockchip,rk3588-ioc", "syscon";
		reg = <0x0 0xfd5f0000 0x0 0x10000>;
	};

	cru: clock-controller@fd7c0000 {
		compatible = "rockchip,rk3588-cru";
		rockchip,grf = <&php_grf>;
		reg = <0x0 0xfd7c0000 0x0 0x5c000>;
		#clock-cells = <1>;
		#reset-cells = <1>;

		assigned-clocks =
			<&cru PLL_PPLL>, <&cru PLL_AUPLL>,
			<&cru PLL_NPLL>, <&cru PLL_GPLL>,
			<&cru ACLK_CENTER_ROOT>,
			<&cru HCLK_CENTER_ROOT>, <&cru ACLK_CENTER_LOW_ROOT>,
			<&cru ACLK_TOP_ROOT>, <&cru PCLK_TOP_ROOT>,
			<&cru ACLK_LOW_TOP_ROOT>, <&cru PCLK_PMU0_ROOT>,
			<&cru HCLK_PMU_CM0_ROOT>,
			<&cru ACLK_BUS_ROOT>, <&cru CLK_150M_SRC>,
			<&cru CLK_GPU>, <&cru CLK_SPDIF2_DP0>,
			<&cru CLK_SPDIF5_DP1>, <&cru CLK_HDMIRX_AUD>,
			<&cru DCLK_DECOM>;
		assigned-clock-rates =
			<1100000000>, <786432000>,
			<850000000>, <1188000000>,
			<702000000>,
			<400000000>, <500000000>,
			<750000000>, <100000000>,
			<400000000>, <100000000>,
			<200000000>,
			<375000000>, <150000000>,
			<200000000>, <12000000>,
			<12000000>, <99000000>,
			<20000000>;
	};

	i2c0: i2c@fd880000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfd880000 0x0 0x1000>;
		clocks = <&cru CLK_I2C0>, <&cru PCLK_I2C0>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 317 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c0m0_xfer>;
		resets = <&cru SRST_I2C0>, <&cru SRST_P_I2C0>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	uart0: serial@fd890000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfd890000 0x0 0x100>;
		interrupts = <GIC_SPI 331 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART0>, <&cru PCLK_UART0>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac0 6>, <&dmac0 7>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart0m1_xfer>;
		status = "disabled";
	};

	pwm0: pwm@fd8b0000 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfd8b0000 0x0 0x10>;
		interrupts = <GIC_SPI 344 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm0m0_pins>;
		clocks = <&cru CLK_PMU1PWM>, <&cru PCLK_PMU1PWM>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm1: pwm@fd8b0010 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfd8b0010 0x0 0x10>;
		interrupts = <GIC_SPI 344 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm1m0_pins>;
		clocks = <&cru CLK_PMU1PWM>, <&cru PCLK_PMU1PWM>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm2: pwm@fd8b0020 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfd8b0020 0x0 0x10>;
		interrupts = <GIC_SPI 344 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm2m0_pins>;
		clocks = <&cru CLK_PMU1PWM>, <&cru PCLK_PMU1PWM>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm3: pwm@fd8b0030 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfd8b0030 0x0 0x10>;
		interrupts = <GIC_SPI 344 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 345 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm3m0_pins>;
		clocks = <&cru CLK_PMU1PWM>, <&cru PCLK_PMU1PWM>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pmu: power-management@fd8d8000 {
		compatible = "rockchip,rk3588-pmu", "syscon", "simple-mfd";
		reg = <0x0 0xfd8d8000 0x0 0x400>;

		power: power-controller {
			compatible = "rockchip,rk3588-power-controller";
			#power-domain-cells = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			/* These power domains are grouped by VD_NPU */
			power-domain@RK3588_PD_NPU {
				reg = <RK3588_PD_NPU>;
				#address-cells = <1>;
				#size-cells = <0>;

				power-domain@RK3588_PD_NPUTOP {
					reg = <RK3588_PD_NPUTOP>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&cru HCLK_NPU_ROOT>,
						 <&cru PCLK_NPU_ROOT>,
						 <&cru CLK_NPU_DSU0>,
						 <&cru HCLK_NPU_CM0_ROOT>;
					pm_qos = <&qos_npu0_mwr>,
						 <&qos_npu0_mro>,
						 <&qos_mcu_npu>;

					power-domain@RK3588_PD_NPU1 {
						reg = <RK3588_PD_NPU1>;
						clocks = <&cru HCLK_NPU_ROOT>,
							 <&cru PCLK_NPU_ROOT>,
							 <&cru CLK_NPU_DSU0>;
						pm_qos = <&qos_npu1>;
					};
					power-domain@RK3588_PD_NPU2 {
						reg = <RK3588_PD_NPU2>;
						clocks = <&cru HCLK_NPU_ROOT>,
							 <&cru PCLK_NPU_ROOT>,
							 <&cru CLK_NPU_DSU0>;
						pm_qos = <&qos_npu2>;
					};
				};
			};
			/* These power domains are grouped by VD_GPU */
			power-domain@RK3588_PD_GPU {
				reg = <RK3588_PD_GPU>;
				clocks = <&cru CLK_GPU>,
					 <&cru CLK_GPU_COREGROUP>,
					 <&cru CLK_GPU_STACKS>;
				pm_qos = <&qos_gpu_m0>,
					 <&qos_gpu_m1>,
					 <&qos_gpu_m2>,
					 <&qos_gpu_m3>;
			};
			/* These power domains are grouped by VD_VCODEC */
			power-domain@RK3588_PD_VCODEC {
				reg = <RK3588_PD_VCODEC>;
				#address-cells = <1>;
				#size-cells = <0>;

				power-domain@RK3588_PD_RKVDEC0 {
					reg = <RK3588_PD_RKVDEC0>;
					clocks = <&cru HCLK_RKVDEC0>,
						 <&cru HCLK_VDPU_ROOT>,
						 <&cru ACLK_VDPU_ROOT>,
						 <&cru ACLK_RKVDEC0>,
						 <&cru ACLK_RKVDEC_CCU>;
					pm_qos = <&qos_rkvdec0>;
				};
				power-domain@RK3588_PD_RKVDEC1 {
					reg = <RK3588_PD_RKVDEC1>;
					clocks = <&cru HCLK_RKVDEC1>,
						 <&cru HCLK_VDPU_ROOT>,
						 <&cru ACLK_VDPU_ROOT>,
						 <&cru ACLK_RKVDEC1>;
					pm_qos = <&qos_rkvdec1>;
				};
				power-domain@RK3588_PD_VENC0 {
					reg = <RK3588_PD_VENC0>;
					#address-cells = <1>;
					#size-cells = <0>;
					clocks = <&cru HCLK_RKVENC0>,
						 <&cru ACLK_RKVENC0>;
					pm_qos = <&qos_rkvenc0_m0ro>,
						 <&qos_rkvenc0_m1ro>,
						 <&qos_rkvenc0_m2wo>;

					power-domain@RK3588_PD_VENC1 {
						reg = <RK3588_PD_VENC1>;
						clocks = <&cru HCLK_RKVENC1>,
							 <&cru HCLK_RKVENC0>,
							 <&cru ACLK_RKVENC0>,
							 <&cru ACLK_RKVENC1>;
						pm_qos = <&qos_rkvenc1_m0ro>,
							 <&qos_rkvenc1_m1ro>,
							 <&qos_rkvenc1_m2wo>;
					};
				};
			};
			/* These power domains are grouped by VD_LOGIC */
			power-domain@RK3588_PD_VDPU {
				reg = <RK3588_PD_VDPU>;
				#address-cells = <1>;
				#size-cells = <0>;
				clocks = <&cru HCLK_VDPU_ROOT>,
					 <&cru ACLK_VDPU_LOW_ROOT>,
					 <&cru ACLK_VDPU_ROOT>,
					 <&cru ACLK_JPEG_DECODER_ROOT>,
					 <&cru ACLK_IEP2P0>,
					 <&cru HCLK_IEP2P0>,
					 <&cru ACLK_JPEG_ENCODER0>,
					 <&cru HCLK_JPEG_ENCODER0>,
					 <&cru ACLK_JPEG_ENCODER1>,
					 <&cru HCLK_JPEG_ENCODER1>,
					 <&cru ACLK_JPEG_ENCODER2>,
					 <&cru HCLK_JPEG_ENCODER2>,
					 <&cru ACLK_JPEG_ENCODER3>,
					 <&cru HCLK_JPEG_ENCODER3>,
					 <&cru ACLK_JPEG_DECODER>,
					 <&cru HCLK_JPEG_DECODER>,
					 <&cru ACLK_RGA2>,
					 <&cru HCLK_RGA2>;
				pm_qos = <&qos_iep>,
					 <&qos_jpeg_dec>,
					 <&qos_jpeg_enc0>,
					 <&qos_jpeg_enc1>,
					 <&qos_jpeg_enc2>,
					 <&qos_jpeg_enc3>,
					 <&qos_rga2_mro>,
					 <&qos_rga2_mwo>;

				power-domain@RK3588_PD_AV1 {
					reg = <RK3588_PD_AV1>;
					clocks = <&cru PCLK_AV1>,
						 <&cru ACLK_AV1>,
						 <&cru HCLK_VDPU_ROOT>;
					pm_qos = <&qos_av1>;
				};
				power-domain@RK3588_PD_RKVDEC0 {
					reg = <RK3588_PD_RKVDEC0>;
					clocks = <&cru HCLK_RKVDEC0>,
						 <&cru HCLK_VDPU_ROOT>,
						 <&cru ACLK_VDPU_ROOT>,
						 <&cru ACLK_RKVDEC0>;
					pm_qos = <&qos_rkvdec0>;
				};
				power-domain@RK3588_PD_RKVDEC1 {
					reg = <RK3588_PD_RKVDEC1>;
					clocks = <&cru HCLK_RKVDEC1>,
						 <&cru HCLK_VDPU_ROOT>,
						 <&cru ACLK_VDPU_ROOT>;
					pm_qos = <&qos_rkvdec1>;
				};
				power-domain@RK3588_PD_RGA30 {
					reg = <RK3588_PD_RGA30>;
					clocks = <&cru ACLK_RGA3_0>,
						 <&cru HCLK_RGA3_0>;
					pm_qos = <&qos_rga3_0>;
				};
			};
			power-domain@RK3588_PD_VOP {
				reg = <RK3588_PD_VOP>;
				#address-cells = <1>;
				#size-cells = <0>;
				clocks = <&cru PCLK_VOP_ROOT>,
					 <&cru HCLK_VOP_ROOT>,
					 <&cru ACLK_VOP>;
				pm_qos = <&qos_vop_m0>,
					 <&qos_vop_m1>;

				power-domain@RK3588_PD_VO0 {
					reg = <RK3588_PD_VO0>;
					clocks = <&cru PCLK_VO0_ROOT>,
						 <&cru PCLK_VO0_S_ROOT>,
						 <&cru HCLK_VO0_S_ROOT>,
						 <&cru ACLK_VO0_ROOT>,
						 <&cru HCLK_HDCP0>,
						 <&cru ACLK_HDCP0>,
						 <&cru HCLK_VOP_ROOT>;
					pm_qos = <&qos_hdcp0>;
				};
			};
			power-domain@RK3588_PD_VO1 {
				reg = <RK3588_PD_VO1>;
				clocks = <&cru PCLK_VO1_ROOT>,
					 <&cru PCLK_VO1_S_ROOT>,
					 <&cru HCLK_VO1_S_ROOT>,
					 <&cru HCLK_HDCP1>,
					 <&cru ACLK_HDCP1>,
					 <&cru ACLK_HDMIRX_ROOT>,
					 <&cru HCLK_VO1USB_TOP_ROOT>;
				pm_qos = <&qos_hdcp1>,
					 <&qos_hdmirx>;
			};
			power-domain@RK3588_PD_VI {
				reg = <RK3588_PD_VI>;
				#address-cells = <1>;
				#size-cells = <0>;
				clocks = <&cru HCLK_VI_ROOT>,
					 <&cru PCLK_VI_ROOT>,
					 <&cru HCLK_ISP0>,
					 <&cru ACLK_ISP0>,
					 <&cru HCLK_VICAP>,
					 <&cru ACLK_VICAP>;
				pm_qos = <&qos_isp0_mro>,
					 <&qos_isp0_mwo>,
					 <&qos_vicap_m0>,
					 <&qos_vicap_m1>;

				power-domain@RK3588_PD_ISP1 {
					reg = <RK3588_PD_ISP1>;
					clocks = <&cru HCLK_ISP1>,
						 <&cru ACLK_ISP1>,
						 <&cru HCLK_VI_ROOT>,
						 <&cru PCLK_VI_ROOT>;
					pm_qos = <&qos_isp1_mwo>,
						 <&qos_isp1_mro>;
				};
				power-domain@RK3588_PD_FEC {
					reg = <RK3588_PD_FEC>;
					clocks = <&cru HCLK_FISHEYE0>,
						 <&cru ACLK_FISHEYE0>,
						 <&cru HCLK_FISHEYE1>,
						 <&cru ACLK_FISHEYE1>,
						 <&cru PCLK_VI_ROOT>;
					pm_qos = <&qos_fisheye0>,
						 <&qos_fisheye1>;
				};
			};
			power-domain@RK3588_PD_RGA31 {
				reg = <RK3588_PD_RGA31>;
				clocks = <&cru HCLK_RGA3_1>,
					 <&cru ACLK_RGA3_1>;
				pm_qos = <&qos_rga3_1>;
			};
			power-domain@RK3588_PD_USB {
				reg = <RK3588_PD_USB>;
				clocks = <&cru PCLK_PHP_ROOT>,
					 <&cru ACLK_USB3OTG0>,
					 <&cru ACLK_USB3OTG1>,
					 <&cru HCLK_HOST0>,
					 <&cru HCLK_HOST_ARB0>,
					 <&cru HCLK_HOST1>,
					 <&cru HCLK_HOST_ARB1>;
				pm_qos = <&qos_usb3_0>,
					 <&qos_usb3_1>,
					 <&qos_usb2host_0>,
					 <&qos_usb2host_1>;
			};
			power-domain@RK3588_PD_GMAC {
				reg = <RK3588_PD_GMAC>;
				clocks = <&cru PCLK_PHP_ROOT>,
					 <&cru ACLK_PCIE_ROOT>,
					 <&cru ACLK_PHP_ROOT>;
			};
			power-domain@RK3588_PD_PCIE {
				reg = <RK3588_PD_PCIE>;
				clocks = <&cru PCLK_PHP_ROOT>,
					 <&cru ACLK_PCIE_ROOT>,
					 <&cru ACLK_PHP_ROOT>;
				pm_qos = <&qos_gic600_m0>,
					 <&qos_gic600_m1>;
			};
			power-domain@RK3588_PD_SDIO {
				reg = <RK3588_PD_SDIO>;
				clocks = <&cru HCLK_SDIO>,
					 <&cru HCLK_NVM_ROOT>;
				pm_qos = <&qos_sdio>;
			};
			power-domain@RK3588_PD_AUDIO {
				reg = <RK3588_PD_AUDIO>;
				clocks = <&cru HCLK_AUDIO_ROOT>,
					 <&cru PCLK_AUDIO_ROOT>;
			};
			power-domain@RK3588_PD_SDMMC {
				reg = <RK3588_PD_SDMMC>;
				pm_qos = <&qos_sdmmc>;
			};
		};
	};

	pvtm@fda40000 {
		compatible = "rockchip,rk3588-bigcore0-pvtm";
		reg = <0x0 0xfda40000 0x0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		pvtm@0 {
			reg = <0>;
			clocks = <&cru CLK_BIGCORE0_PVTM>, <&cru PCLK_BIGCORE0_PVTM>;
			clock-names = "clk", "pclk";
		};
	};

	pvtm@fda50000 {
		compatible = "rockchip,rk3588-bigcore1-pvtm";
		reg = <0x0 0xfda50000 0x0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		pvtm@1 {
			reg = <1>;
			clocks = <&cru CLK_BIGCORE1_PVTM>, <&cru PCLK_BIGCORE1_PVTM>;
			clock-names = "clk", "pclk";
		};
	};

	pvtm@fda60000 {
		compatible = "rockchip,rk3588-litcore-pvtm";
		reg = <0x0 0xfda60000 0x0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		pvtm@2 {
			reg = <2>;
			clocks = <&cru CLK_LITCORE_PVTM>, <&cru PCLK_LITCORE_PVTM>;
			clock-names = "clk", "pclk";
		};
	};

	pvtm@fdaf0000 {
		compatible = "rockchip,rk3588-npu-pvtm";
		reg = <0x0 0xfdaf0000 0x0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		pvtm@3 {
			reg = <3>;
			clocks = <&cru CLK_NPU_PVTM>, <&cru PCLK_NPU_PVTM>;
			clock-names = "clk", "pclk";
			resets = <&cru SRST_NPU_PVTM>, <&cru SRST_P_NPU_PVTM>;
			reset-names = "rts", "rst-p";
		};
	};

	pvtm@fdb30000 {
		compatible = "rockchip,rk3588-gpu-pvtm";
		reg = <0x0 0xfdb30000 0x0 0x100>;
		#address-cells = <1>;
		#size-cells = <0>;
		pvtm@4 {
			reg = <4>;
			clocks = <&cru CLK_GPU_PVTM>;
			clock-names = "clk";
			resets = <&cru SRST_GPU_PVTM>, <&cru SRST_P_GPU_PVTM>;
			reset-names = "rts", "rst-p";
		};
	};

	rknpu: npu@fdab0000 {
		compatible = "rockchip,rk3588-rknpu";
		reg = <0x0 0xfdab0000 0x0 0x10000>,
		      <0x0 0xfdac0000 0x0 0x10000>,
		      <0x0 0xfdad0000 0x0 0x10000>;
		interrupts = <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "npu0_irq", "npu1_irq", "npu2_irq";
		clocks = <&scmi_clk SCMI_CLK_NPU>, <&cru ACLK_NPU0>,
			 <&cru ACLK_NPU1>, <&cru ACLK_NPU2>,
			 <&cru HCLK_NPU0>, <&cru HCLK_NPU1>,
			 <&cru HCLK_NPU2>, <&cru PCLK_NPU_ROOT>;
		clock-names = "clk_npu", "aclk0",
			      "aclk1", "aclk2",
			      "hclk0", "hclk1",
			      "hclk2", "pclk";
		assigned-clocks = <&scmi_clk SCMI_CLK_NPU>;
		assigned-clock-rates = <200000000>;
		resets = <&cru SRST_A_RKNN0>, <&cru SRST_A_RKNN1>, <&cru SRST_A_RKNN2>,
			 <&cru SRST_H_RKNN0>, <&cru SRST_H_RKNN1>, <&cru SRST_H_RKNN2>;
		reset-names = "srst_a0", "srst_a1", "srst_a2",
			      "srst_h0", "srst_h1", "srst_h2";
		power-domains = <&power RK3588_PD_NPUTOP>,
				<&power RK3588_PD_NPU1>,
				<&power RK3588_PD_NPU2>;
		power-domain-names = "npu0", "npu1", "npu2";
		operating-points-v2 = <&npu_opp_table>;
		iommus = <&rknpu_mmu>;
		status = "disabled";
	};

	npu_opp_table: npu-opp-table {
		compatible = "operating-points-v2";

		nvmem-cells = <&npu_leakage>, <&npu_opp_info>,
			      <&specification_serial_number>, <&customer_demand>;
		nvmem-cell-names = "leakage", "opp-info",
				   "specification_serial_number", "customer_demand";
		rockchip,supported-hw;

		rockchip,pvtm-hw = <0x06>;
		rockchip,pvtm-voltage-sel-hw = <
			0	799	0
			800	819	1
			820	844	2
			845	869	3
			870	894	4
			895	9999	5
		>;
		rockchip,pvtm-voltage-sel-B4 = <
			0	755	0
			756	775	1
			776	795	2
			796	815	3
			816	835	4
			836	860	5
			861	885	6
			886	910	7
			911	9999	8
		>;
		rockchip,pvtm-voltage-sel = <
			0	815	0
			816	835	1
			836	860	2
			861	885	3
			886	910	4
			911	9999	5
		>;
		rockchip,pvtm-pvtpll;
		rockchip,pvtm-offset = <0x50>;
		rockchip,pvtm-sample-time = <1100>;
		rockchip,pvtm-freq = <800000>;
		rockchip,pvtm-volt = <750000>;
		rockchip,pvtm-ref-temp = <25>;
		rockchip,pvtm-temp-prop = <(-113) (-113)>;
		rockchip,pvtm-thermal-zone = "npu-thermal";

		rockchip,opp-clocks = <&cru PCLK_NPU_GRF>, <&cru HCLK_NPU_ROOT>;
		rockchip,grf = <&npu_grf>;
		volt-mem-read-margin = <
			855000	1
			765000	2
			675000	3
			495000	4
		>;
		low-volt-mem-read-margin = <4>;
		intermediate-threshold-freq = <500000>;	/* KHz*/
		rockchip,init-freq = <1000000>;		/* KHz */

		rockchip,temp-hysteresis = <5000>;
		rockchip,low-temp = <15000>;
		rockchip,low-temp-min-volt = <750000>;
		rockchip,high-temp = <85000>;
		rockchip,high-temp-max-freq = <800000>;

		/* RK3588 npu OPPs */
		opp-300000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <700000 700000 850000>,
					<700000 700000 850000>;
			opp-microvolt-L1 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L2 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L3 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L4 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L5 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-400000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <700000 700000 850000>,
					<700000 700000 850000>;
			opp-microvolt-L1 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L2 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L3 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L4 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L5 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-500000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <700000 700000 850000>,
					<700000 700000 850000>;
			opp-microvolt-L1 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L2 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L3 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L4 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L5 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-600000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <700000 700000 850000>,
					<700000 700000 850000>;
			opp-microvolt-L1 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L2 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L3 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L4 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L5 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-700000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt = <700000 700000 850000>,
					<700000 700000 850000>;
			opp-microvolt-L3 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L4 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L5 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-800000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
			opp-microvolt-L2 = <737500 737500 850000>,
					   <737500 737500 850000>;
			opp-microvolt-L3 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L4 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L5 = <700000 700000 850000>,
					   <700000 700000 850000>;
		};
		opp-900000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <900000000>;
			opp-microvolt = <800000 800000 850000>,
					<800000 800000 850000>;
			opp-microvolt-L1 = <787500 787500 850000>,
					   <787500 787500 850000>;
			opp-microvolt-L2 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L3 = <762500 762500 850000>,
					   <762500 762500 850000>;
			opp-microvolt-L4 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L5 = <737500 737500 850000>,
					   <737500 737500 850000>;
		};
		opp-1000000000 {
			opp-supported-hw = <0xe9 0xffff>;
			opp-hz = /bits/ 64 <1000000000>;
			opp-microvolt = <850000 850000 850000>,
					<850000 850000 850000>;
			opp-microvolt-L1 = <837500 837500 850000>,
					   <837500 837500 850000>;
			opp-microvolt-L2 = <825000 825000 850000>,
					   <825000 825000 850000>;
			opp-microvolt-L3 = <812500 812500 850000>,
					   <812500 812500 850000>;
			opp-microvolt-L4 = <800000 800000 850000>,
					   <800000 800000 850000>;
			opp-microvolt-L5 = <787500 787500 850000>,
					   <787500 787500 850000>;
		};

		opp-b-300000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L1 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L2 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L3 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L4 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-400000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L1 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L2 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L3 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L4 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-500000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L1 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L2 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L3 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L4 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-600000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <675000 675000 850000>,
					<675000 675000 850000>;
			opp-microvolt-L0 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L1 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L2 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L3 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L4 = <687500 687500 850000>,
					   <687500 687500 850000>;
		};
		opp-b-700000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt = <737500 737500 850000>,
					<737500 737500 850000>;
			opp-microvolt-L1 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L2 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L3 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L4 = <700000 700000 850000>,
					   <700000 700000 850000>;
			opp-microvolt-L5 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L6 = <687500 687500 850000>,
					   <687500 687500 850000>;
			opp-microvolt-L7 = <675000 675000 850000>,
					   <675000 675000 850000>;
			opp-microvolt-L8 = <675000 675000 850000>,
					   <675000 675000 850000>;
		};
		opp-b-800000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <787500 787500 850000>,
					<787500 787500 850000>;
			opp-microvolt-L1 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L2 = <762500 762500 850000>,
					   <762500 762500 850000>;
			opp-microvolt-L3 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L4 = <750000 750000 850000>,
					   <750000 750000 850000>;
			opp-microvolt-L5 = <737500 737500 850000>,
					   <737500 737500 850000>;
			opp-microvolt-L6 = <725000 725000 850000>,
					   <725000 725000 850000>;
			opp-microvolt-L7 = <712500 712500 850000>,
					   <712500 712500 850000>;
			opp-microvolt-L8 = <700000 700000 850000>,
					   <700000 700000 850000>;
		};
		opp-950000000 {
			opp-supported-hw = <0x10 0xffff>;
			opp-hz = /bits/ 64 <950000000>;
			opp-microvolt = <850000 850000 850000>,
					<850000 850000 850000>;
			opp-microvolt-L2 = <850000 850000 850000>,
					   <850000 850000 850000>;
			opp-microvolt-L3 = <837500 837500 850000>,
					   <837500 837500 850000>;
			opp-microvolt-L4 = <825000 825000 850000>,
					   <825000 825000 850000>;
			opp-microvolt-L5 = <812500 812500 850000>,
					   <812500 812500 850000>;
			opp-microvolt-L6 = <800000 800000 850000>,
					   <800000 800000 850000>;
			opp-microvolt-L7 = <787500 787500 850000>,
					   <787500 787500 850000>;
			opp-microvolt-L8 = <775000 775000 850000>,
					   <775000 775000 850000>;
		};

		/* RK3588J/M npu OPPs */
		opp-j-m-300000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <300000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-400000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <400000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-500000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-600000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <600000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-700000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <700000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-800000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
		};
		opp-j-m-950000000 {
			opp-supported-hw = <0x06 0xffff>;
			opp-hz = /bits/ 64 <950000000>;
			opp-microvolt = <837500 837500 850000>,
					<837500 837500 850000>;
			opp-microvolt-L1 = <825000 825000 850000>,
					   <825000 825000 850000>;
			opp-microvolt-L2 = <812500 812500 850000>,
					   <812500 812500 850000>;
			opp-microvolt-L3 = <800000 800000 850000>,
					   <800000 800000 850000>;
			opp-microvolt-L4 = <787500 787500 850000>,
					   <787500 787500 850000>;
			opp-microvolt-L5 = <775000 775000 850000>,
					   <775000 775000 850000>;
		};
	};

	rknpu_mmu: iommu@fdab9000 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdab9000 0x0 0x100>,
		      <0x0 0xfdaba000 0x0 0x100>,
		      <0x0 0xfdaca000 0x0 0x100>,
		      <0x0 0xfdada000 0x0 0x100>;
		interrupts = <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "npu0_mmu", "npu1_mmu", "npu2_mmu";
		clocks = <&cru ACLK_NPU0>, <&cru ACLK_NPU1>, <&cru ACLK_NPU2>,
			 <&cru HCLK_NPU0>, <&cru HCLK_NPU1>, <&cru HCLK_NPU2>;
		clock-names = "aclk0", "aclk1", "aclk2",
			      "iface0", "iface1", "iface2";
		#iommu-cells = <0>;
		status = "disabled";
	};

	vepu: vepu@fdb50000 {
		compatible = "rockchip,vpu-encoder-v2";
		reg = <0x0 0xfdb50000 0x0 0x400>;
		interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_vepu";
		clocks = <&cru ACLK_VPU>, <&cru HCLK_VPU>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_VPU>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_VPU>, <&cru SRST_H_VPU>;
		reset-names = "shared_video_a", "shared_video_h";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		iommus = <&vdpu_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <0>;
		rockchip,resetgroup-node = <0>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	vdpu: vdpu@fdb50400 {
		compatible = "rockchip,vpu-decoder-v2";
		reg = <0x0 0xfdb50400 0x0 0x400>;
		interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_vdpu";
		clocks = <&cru ACLK_VPU>, <&cru HCLK_VPU>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_VPU>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_VPU>, <&cru SRST_H_VPU>;
		reset-names = "shared_video_a", "shared_video_h";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		iommus = <&vdpu_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <0>;
		rockchip,resetgroup-node = <0>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	vdpu_mmu: iommu@fdb50800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdb50800 0x0 0x40>;
		interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_vdpu_mmu";
		clocks = <&cru ACLK_VPU>, <&cru HCLK_VPU>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VDPU>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	avsd: avsd-plus@fdb51000 {
		compatible = "rockchip,avs-plus-decoder";
		reg = <0x0 0xfdb51000 0x0 0x200>;
		interrupts = <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_avsd";
		clocks = <&cru ACLK_VPU>, <&cru HCLK_VPU>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_VPU>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_VPU>, <&cru SRST_H_VPU>;
		reset-names = "shared_video_a", "shared_video_h";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		iommus = <&vdpu_mmu>;
		power-domains = <&power RK3588_PD_VDPU>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <0>;
		rockchip,resetgroup-node = <0>;
		status = "disabled";
	};

	rga3_core0: rga@fdb60000 {
		compatible = "rockchip,rga3_core0";
		reg = <0x0 0xfdb60000 0x0 0x1000>;
		interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "rga3_core0_irq";
		clocks = <&cru ACLK_RGA3_0>, <&cru HCLK_RGA3_0>, <&cru CLK_RGA3_0_CORE>;
		clock-names = "aclk_rga3_0", "hclk_rga3_0", "clk_rga3_0";
		power-domains = <&power RK3588_PD_RGA30>;
		iommus = <&rga3_0_mmu>;
		status = "disabled";
	};

	rga3_0_mmu: iommu@fdb60f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdb60f00 0x0 0x100>;
		interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "rga3_0_mmu";
		clocks = <&cru ACLK_RGA3_0>, <&cru HCLK_RGA3_0>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_RGA30>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	rga3_core1: rga@fdb70000 {
		compatible = "rockchip,rga3_core1";
		reg = <0x0 0xfdb70000 0x0 0x1000>;
		interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "rga3_core1_irq";
		clocks = <&cru ACLK_RGA3_1>, <&cru HCLK_RGA3_1>, <&cru CLK_RGA3_1_CORE>;
		clock-names = "aclk_rga3_1", "hclk_rga3_1", "clk_rga3_1";
		power-domains = <&power RK3588_PD_RGA31>;
		iommus = <&rga3_1_mmu>;
		status = "disabled";
	};

	rga3_1_mmu: iommu@fdb70f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdb70f00 0x0 0x100>;
		interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "rga3_1_mmu";
		clocks = <&cru ACLK_RGA3_1>, <&cru HCLK_RGA3_1>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_RGA31>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	rga2: rga@fdb80000 {
		compatible = "rockchip,rga2_core0";
		reg = <0x0 0xfdb80000 0x0 0x1000>;
		interrupts = <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "rga2_irq";
		clocks = <&cru ACLK_RGA2>, <&cru HCLK_RGA2>, <&cru CLK_RGA2_CORE>;
		clock-names = "aclk_rga2", "hclk_rga2", "clk_rga2";
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	jpegd: jpegd@fdb90000 {
		compatible = "rockchip,rkv-jpeg-decoder-v1";
		reg = <0x0 0xfdb90000 0x0 0x400>;
		interrupts = <GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpegd";
		clocks = <&cru ACLK_JPEG_DECODER>, <&cru HCLK_JPEG_DECODER>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <600000000>, <0>;
		assigned-clocks = <&cru ACLK_JPEG_DECODER>;
		assigned-clock-rates = <600000000>;
		resets = <&cru SRST_A_JPEG_DECODER>, <&cru SRST_H_JPEG_DECODER>;
		reset-names = "video_a", "video_h";
		rockchip,skip-pmu-idle-request;
		iommus = <&jpegd_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <1>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	jpegd_mmu: iommu@fdb90480 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdb90480 0x0 0x40>;
		interrupts = <GIC_SPI 130 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpegd_mmu";
		clocks = <&cru ACLK_JPEG_DECODER>, <&cru HCLK_JPEG_DECODER>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VDPU>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	jpege0: jpege-core@fdba0000 {
		compatible = "rockchip,vpu-jpege-core";
		reg = <0x0 0xfdba0000 0x0 0x400>;
		interrupts = <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege0";
		clocks = <&cru ACLK_JPEG_ENCODER0>, <&cru HCLK_JPEG_ENCODER0>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_JPEG_ENCODER0>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_JPEG_ENCODER0>, <&cru SRST_H_JPEG_ENCODER0>;
		reset-names = "video_a", "video_h";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		iommus = <&jpege0_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <2>;
		rockchip,ccu = <&jpege_ccu>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	jpege0_mmu: iommu@fdba0800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdba0800 0x0 0x40>;
		interrupts = <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege0_mmu";
		clocks = <&cru ACLK_JPEG_ENCODER0>, <&cru HCLK_JPEG_ENCODER0>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VDPU>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	jpege1: jpege-core@fdba4000 {
		compatible = "rockchip,vpu-jpege-core";
		reg = <0x0 0xfdba4000 0x0 0x400>;
		interrupts = <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege1";
		clocks = <&cru ACLK_JPEG_ENCODER1>, <&cru HCLK_JPEG_ENCODER1>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_JPEG_ENCODER1>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_JPEG_ENCODER1>, <&cru SRST_H_JPEG_ENCODER1>;
		reset-names = "video_a", "video_h";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		iommus = <&jpege1_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <2>;
		rockchip,ccu = <&jpege_ccu>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	jpege1_mmu: iommu@fdba4800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdba4800 0x0 0x40>;
		interrupts = <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege1_mmu";
		clocks = <&cru ACLK_JPEG_ENCODER1>, <&cru HCLK_JPEG_ENCODER1>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VDPU>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	jpege2: jpege-core@fdba8000 {
		compatible = "rockchip,vpu-jpege-core";
		reg = <0x0 0xfdba8000 0x0 0x400>;
		interrupts = <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege2";
		clocks = <&cru ACLK_JPEG_ENCODER2>, <&cru HCLK_JPEG_ENCODER2>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_JPEG_ENCODER2>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_JPEG_ENCODER2>, <&cru SRST_H_JPEG_ENCODER2>;
		reset-names = "video_a", "video_h";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		iommus = <&jpege2_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <2>;
		rockchip,ccu = <&jpege_ccu>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	jpege2_mmu: iommu@fdba8800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdba8800 0x0 0x40>;
		interrupts = <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege2_mmu";
		clocks = <&cru ACLK_JPEG_ENCODER2>, <&cru HCLK_JPEG_ENCODER2>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VDPU>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	jpege3: jpege-core@fdbac000 {
		compatible = "rockchip,vpu-jpege-core";
		reg = <0x0 0xfdbac000 0x0 0x400>;
		interrupts = <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege3";
		clocks = <&cru ACLK_JPEG_ENCODER3>, <&cru HCLK_JPEG_ENCODER3>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_JPEG_ENCODER3>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_JPEG_ENCODER3>, <&cru SRST_H_JPEG_ENCODER3>;
		reset-names = "video_a", "video_h";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		iommus = <&jpege3_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <2>;
		rockchip,ccu = <&jpege_ccu>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	jpege3_mmu: iommu@fdbac800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdbac800 0x0 0x40>;
		interrupts = <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_jpege3_mmu";
		clocks = <&cru ACLK_JPEG_ENCODER3>, <&cru HCLK_JPEG_ENCODER3>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VDPU>;
		#iommu-cells = <0>;
		status = "disabled";
	};

	iep: iep@fdbb0000 {
		compatible = "rockchip,iep-v2";
		reg = <0x0 0xfdbb0000 0x0 0x500>;
		interrupts = <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_iep";
		clocks = <&cru ACLK_IEP2P0>, <&cru HCLK_IEP2P0>, <&cru CLK_IEP2P0_CORE>;
		clock-names = "aclk", "hclk", "sclk";
		rockchip,normal-rates = <594000000>, <0>;
		assigned-clocks = <&cru ACLK_IEP2P0>;
		assigned-clock-rates = <594000000>;
		resets = <&cru SRST_A_IEP2P0>, <&cru SRST_H_IEP2P0>, <&cru SRST_IEP2P0_CORE>;
		reset-names = "rst_a", "rst_h", "rst_s";
		rockchip,skip-pmu-idle-request;
		rockchip,disable-auto-freq;
		power-domains = <&power RK3588_PD_VDPU>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <6>;
		iommus = <&iep_mmu>;
		status = "disabled";
	};

	iep_mmu: iommu@fdbb0800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdbb0800 0x0 0x100>;
		interrupts = <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_iep_mmu";
		clocks = <&cru ACLK_IEP2P0>, <&cru HCLK_IEP2P0>;
		clock-names = "aclk", "iface";
		#iommu-cells = <0>;
		power-domains = <&power RK3588_PD_VDPU>;
		status = "disabled";
	};

	rkvenc0: rkvenc-core@fdbd0000 {
		compatible = "rockchip,rkv-encoder-v2-core";
		reg = <0x0 0xfdbd0000 0x0 0x6000>;
		interrupts = <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvenc0";
		clocks = <&cru ACLK_RKVENC0>, <&cru HCLK_RKVENC0>, <&cru CLK_RKVENC0_CORE>;
		clock-names = "aclk_vcodec", "hclk_vcodec", "clk_core";
		rockchip,normal-rates = <500000000>, <0>, <800000000>;
		assigned-clocks = <&cru ACLK_RKVENC0>, <&cru CLK_RKVENC0_CORE>;
		assigned-clock-rates = <500000000>, <800000000>;
		resets = <&cru SRST_A_RKVENC0>, <&cru SRST_H_RKVENC0>, <&cru SRST_RKVENC0_CORE>;
		reset-names = "video_a", "video_h", "video_core";
		rockchip,skip-pmu-idle-request;
		iommus = <&rkvenc0_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,ccu = <&rkvenc_ccu>;
		rockchip,taskqueue-node = <7>;
		rockchip,task-capacity = <8>;
		power-domains = <&power RK3588_PD_VENC0>;
		operating-points-v2 = <&venc_opp_table>;
		status = "disabled";
	};

	rkvenc0_mmu: iommu@fdbdf000 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdbdf000 0x0 0x40>, <0x0 0xfdbdf040 0x0 0x40>;
		interrupts = <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvenc0_mmu0", "irq_rkvenc0_mmu1";
		clocks = <&cru ACLK_RKVENC0>, <&cru HCLK_RKVENC0>;
		clock-names = "aclk", "iface";
		rockchip,disable-mmu-reset;
		rockchip,enable-cmd-retry;
		rockchip,shootdown-entire;
		#iommu-cells = <0>;
		power-domains = <&power RK3588_PD_VENC0>;
		status = "disabled";
	};

	rkvenc1: rkvenc-core@fdbe0000 {
		compatible = "rockchip,rkv-encoder-v2-core";
		reg = <0x0 0xfdbe0000 0x0 0x6000>;
		interrupts = <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvenc1";
		clocks = <&cru ACLK_RKVENC1>, <&cru HCLK_RKVENC1>, <&cru CLK_RKVENC1_CORE>;
		clock-names = "aclk_vcodec", "hclk_vcodec", "clk_core";
		rockchip,normal-rates = <500000000>, <0>, <800000000>;
		assigned-clocks = <&cru ACLK_RKVENC1>, <&cru CLK_RKVENC1_CORE>;
		assigned-clock-rates = <500000000>, <800000000>;
		resets = <&cru SRST_A_RKVENC1>, <&cru SRST_H_RKVENC1>, <&cru SRST_RKVENC1_CORE>;
		reset-names = "video_a", "video_h", "video_core";
		rockchip,skip-pmu-idle-request;
		iommus = <&rkvenc1_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,ccu = <&rkvenc_ccu>;
		rockchip,taskqueue-node = <7>;
		rockchip,task-capacity = <8>;
		power-domains = <&power RK3588_PD_VENC1>;
		operating-points-v2 = <&venc_opp_table>;
		status = "disabled";
	};

	rkvenc1_mmu: iommu@fdbef000 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdbef000 0x0 0x40>, <0x0 0xfdbef040 0x0 0x40>;
		interrupts = <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvenc1_mmu0", "irq_rkvenc1_mmu1";
		clocks = <&cru ACLK_RKVENC1>, <&cru HCLK_RKVENC1>;
		lock-names = "aclk", "iface";
		rockchip,disable-mmu-reset;
		rockchip,enable-cmd-retry;
		rockchip,shootdown-entire;
		#iommu-cells = <0>;
		power-domains = <&power RK3588_PD_VENC1>;
		status = "disabled";
	};

	venc_opp_table: venc-opp-table {
		compatible = "operating-points-v2";

		nvmem-cells = <&codec_leakage>, <&venc_opp_info>;
		nvmem-cell-names = "leakage", "opp-info";
		rockchip,leakage-voltage-sel = <
			1	15	0
			16	25	1
			26	254	2
		>;

		rockchip,grf = <&sys_grf>;
		volt-mem-read-margin = <
			855000	1
			765000	2
			675000	3
			495000	4
		>;

		opp-800000000 {
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt = <750000 750000 850000>,
					<750000 750000 850000>;
			opp-microvolt-L0 = <800000 800000 850000>,
					   <800000 800000 850000>;
			opp-microvolt-L1 = <775000 775000 850000>,
					   <775000 775000 850000>;
			opp-microvolt-L2 = <750000 750000 850000>,
					   <750000 750000 850000>;
		};
	};

	rkvdec_ccu: rkvdec-ccu@fdc30000 {
		compatible = "rockchip,rkv-decoder-v2-ccu";
		reg = <0x0 0xfdc30000 0x0 0x100>;
		reg-names = "ccu";
		clocks = <&cru ACLK_RKVDEC_CCU>;
		clock-names = "aclk_ccu";
		assigned-clocks = <&cru ACLK_RKVDEC_CCU>;
		assigned-clock-rates = <600000000>;
		resets = <&cru SRST_A_RKVDEC_CCU>;
		reset-names = "video_ccu";
		rockchip,skip-pmu-idle-request;
		/* 1: soft ccu 2: hw ccu */
		rockchip,ccu-mode = <1>;
		power-domains = <&power RK3588_PD_RKVDEC0>;
		status = "disabled";
	};

	rkvdec0: rkvdec-core@fdc38000 {
		compatible = "rockchip,rkv-decoder-v2";
		reg = <0x0 0xfdc38100 0x0 0x400>, <0x0 0xfdc38000 0x0 0x100>;
		reg-names = "regs", "link";
		interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvdec0";
		clocks = <&cru ACLK_RKVDEC0>, <&cru HCLK_RKVDEC0>, <&cru CLK_RKVDEC0_CORE>,
			 <&cru CLK_RKVDEC0_CA>, <&cru CLK_RKVDEC0_HEVC_CA>;
		clock-names = "aclk_vcodec", "hclk_vcodec", "clk_core",
			      "clk_cabac", "clk_hevc_cabac";
		rockchip,normal-rates = <800000000>, <0>, <600000000>,
					<600000000>, <1000000000>;
		assigned-clocks = <&cru ACLK_RKVDEC0>, <&cru CLK_RKVDEC0_CORE>,
				  <&cru CLK_RKVDEC0_CA>, <&cru CLK_RKVDEC0_HEVC_CA>;
		assigned-clock-rates = <800000000>, <600000000>,
				       <600000000>, <1000000000>;
		resets = <&cru SRST_A_RKVDEC0>, <&cru SRST_H_RKVDEC0>, <&cru SRST_RKVDEC0_CORE>,
			 <&cru SRST_RKVDEC0_CA>, <&cru SRST_RKVDEC0_HEVC_CA>;
		reset-names = "video_a", "video_h", "video_core",
			      "video_cabac", "video_hevc_cabac";
		rockchip,skip-pmu-idle-request;
		iommus = <&rkvdec0_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,ccu = <&rkvdec_ccu>;
		rockchip,core-mask = <0x00010001>;
		rockchip,task-capacity = <16>;
		rockchip,taskqueue-node = <9>;
		rockchip,sram = <&rkvdec0_sram>;
		/* rcb_iova: start and size 1M@4095M */
		rockchip,rcb-iova = <0xFFF00000 0x100000>;
		rockchip,rcb-info = <136 24576>, <137 49152>, <141 90112>, <140 49152>,
				    <139 180224>, <133 49152>, <134 8192>, <135 4352>,
				    <138 13056>, <142 291584>;
		rockchip,rcb-min-width = <512>;
		power-domains = <&power RK3588_PD_RKVDEC0>;
		status = "disabled";
	};

	rkvdec0_mmu: iommu@fdc38700 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdc38700 0x0 0x40>, <0x0 0xfdc38740 0x0 0x40>;
		interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvdec0_mmu";
		clocks = <&cru ACLK_RKVDEC0>, <&cru HCLK_RKVDEC0>;
		clock-names = "aclk", "iface";
		rockchip,disable-mmu-reset;
		rockchip,enable-cmd-retry;
		rockchip,shootdown-entire;
		rockchip,master-handle-irq;
		#iommu-cells = <0>;
		power-domains = <&power RK3588_PD_RKVDEC0>;
		status = "disabled";
	};

	rkvdec1: rkvdec-core@fdc48000 {
		compatible = "rockchip,rkv-decoder-v2";
		reg = <0x0 0xfdc48100 0x0 0x400>, <0x0 0xfdc48000 0x0 0x100>;
		reg-names = "regs", "link";
		interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvdec1";
		clocks = <&cru ACLK_RKVDEC1>, <&cru HCLK_RKVDEC1>, <&cru CLK_RKVDEC1_CORE>,
			 <&cru CLK_RKVDEC1_CA>, <&cru CLK_RKVDEC1_HEVC_CA>;
		clock-names = "aclk_vcodec", "hclk_vcodec", "clk_core",
			      "clk_cabac", "clk_hevc_cabac";
		rockchip,normal-rates = <800000000>, <0>, <600000000>,
					<600000000>, <1000000000>;
		assigned-clocks = <&cru ACLK_RKVDEC1>, <&cru CLK_RKVDEC1_CORE>,
				  <&cru CLK_RKVDEC1_CA>, <&cru CLK_RKVDEC1_HEVC_CA>;
		assigned-clock-rates = <800000000>, <600000000>,
				       <600000000>, <1000000000>;
		resets = <&cru SRST_A_RKVDEC1>, <&cru SRST_H_RKVDEC1>, <&cru SRST_RKVDEC1_CORE>,
			 <&cru SRST_RKVDEC1_CA>, <&cru SRST_RKVDEC1_HEVC_CA>;
		reset-names = "video_a", "video_h", "video_core",
			      "video_cabac", "video_hevc_cabac";
		rockchip,skip-pmu-idle-request;
		iommus = <&rkvdec1_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,ccu = <&rkvdec_ccu>;
		rockchip,core-mask = <0x00020002>;
		rockchip,task-capacity = <16>;
		rockchip,taskqueue-node = <9>;
		rockchip,sram = <&rkvdec1_sram>;
		/* rcb_iova: start and size 1M@4094M */
		rockchip,rcb-iova = <0xFFE00000 0x100000>;
		rockchip,rcb-info = <136 24576>, <137 49152>, <141 90112>, <140 49152>,
				    <139 180224>, <133 49152>, <134 8192>, <135 4352>,
				    <138 13056>, <142 291584>;
		rockchip,rcb-min-width = <512>;
		power-domains = <&power RK3588_PD_RKVDEC1>;
		status = "disabled";
	};

	rkvdec1_mmu: iommu@fdc48700 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdc48700 0x0 0x40>, <0x0 0xfdc48740 0x0 0x40>;
		interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_rkvdec1_mmu";
		clocks = <&cru ACLK_RKVDEC1>, <&cru HCLK_RKVDEC1>;
		clock-names = "aclk", "iface";
		rockchip,disable-mmu-reset;
		rockchip,enable-cmd-retry;
		rockchip,shootdown-entire;
		rockchip,master-handle-irq;
		#iommu-cells = <0>;
		power-domains = <&power RK3588_PD_RKVDEC1>;
		status = "disabled";
	};

	av1d: av1d@fdc70000 {
		compatible = "rockchip,av1-decoder";
		reg = <0x0 0xfdc70000 0x0 0x800>,  <0x0 0xfdc80000 0x0 0x400>,
		      <0x0 0xfdc90000 0x0 0x400>;
		reg-names = "vcd", "cache", "afbc";
		interrupts = <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>, <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_av1d", "irq_cache", "irq_afbc";
		clocks = <&cru ACLK_AV1>, <&cru PCLK_AV1>;
		clock-names = "aclk_vcodec", "hclk_vcodec";
		rockchip,normal-rates = <400000000>, <400000000>;
		assigned-clocks = <&cru ACLK_AV1>, <&cru PCLK_AV1>;
		assigned-clock-rates = <400000000>, <400000000>;
		resets = <&cru SRST_A_AV1>, <&cru SRST_P_AV1>;
		reset-names = "video_a", "video_h";
		iommus = <&av1d_mmu>;
		rockchip,srv = <&mpp_srv>;
		rockchip,taskqueue-node = <11>;
		power-domains = <&power RK3588_PD_AV1>;
		status = "disabled";
	};

	av1d_mmu: iommu@fdca0000 {
		compatible = "rockchip,iommu-av1d";
		reg = <0x0 0xfdca0000 0x0 0x600>;
		interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_av1d_mmu";
		clocks = <&cru ACLK_AV1>, <&cru PCLK_AV1>;
		clock-names = "aclk", "iface";
		#iommu-cells = <0>;
		power-domains = <&power RK3588_PD_AV1>;
		status = "disabled";
	};

	rkisp_unite: rkisp-unite@fdcb0000 {
		compatible = "rockchip,rk3588-rkisp-unite";
		reg = <0x0 0xfdcb0000 0x0 0x10000>,
		      <0x0 0xfdcc0000 0x0 0x10000>;
		interrupts = <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "isp_irq", "mi_irq", "mipi_irq";
		clocks = <&cru ACLK_ISP0>, <&cru HCLK_ISP0>,
			 <&cru CLK_ISP0_CORE>, <&cru CLK_ISP0_CORE_MARVIN>,
			 <&cru CLK_ISP0_CORE_VICAP>, <&cru ACLK_ISP1>,
			 <&cru HCLK_ISP1>, <&cru CLK_ISP1_CORE>,
			 <&cru CLK_ISP1_CORE_MARVIN>, <&cru CLK_ISP1_CORE_VICAP>;
		clock-names = "aclk_isp0", "hclk_isp0", "clk_isp_core0",
			      "clk_isp_core_marvin0", "clk_isp_core_vicap0",
			      "aclk_isp1", "hclk_isp1", "clk_isp_core1",
			      "clk_isp_core_marvin1", "clk_isp_core_vicap1";
		power-domains = <&power RK3588_PD_ISP1>;
		iommus = <&rkisp_unite_mmu>;
		status = "disabled";
	};

	rkisp0: rkisp@fdcb0000 {
		compatible = "rockchip,rk3588-rkisp";
		reg = <0x0 0xfdcb0000 0x0 0x7f00>;
		interrupts = <GIC_SPI 131 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 133 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "isp_irq", "mi_irq", "mipi_irq";
		clocks = <&cru ACLK_ISP0>, <&cru HCLK_ISP0>,
			 <&cru CLK_ISP0_CORE>, <&cru CLK_ISP0_CORE_MARVIN>,
			 <&cru CLK_ISP0_CORE_VICAP>;
		clock-names = "aclk_isp", "hclk_isp", "clk_isp_core",
			      "clk_isp_core_marvin", "clk_isp_core_vicap";
		power-domains = <&power RK3588_PD_VI>;
		iommus = <&isp0_mmu>;
		status = "disabled";
	};

	rkisp_unite_mmu: rkisp-unite-mmu@fdcb7f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdcb7f00 0x0 0x100>, <0x0 0xfdcc7f00 0x0 0x100>;
		interrupts = <GIC_SPI 132 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "isp0_mmu", "isp1_mmu";
		clocks = <&cru ACLK_ISP0>, <&cru HCLK_ISP0>,
			 <&cru ACLK_ISP1>, <&cru HCLK_ISP1>;
		clock-names = "aclk0", "iface0", "aclk1", "iface1";
		power-domains = <&power RK3588_PD_ISP1>;
		#iommu-cells = <0>;
		rockchip,disable-mmu-reset;
		status = "disabled";
	};

	isp0_mmu: iommu@fdcb7f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdcb7f00 0x0 0x100>;
		interrupts = <GIC_SPI 132 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "isp0_mmu";
		clocks = <&cru ACLK_ISP0>, <&cru HCLK_ISP0>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VI>;
		#iommu-cells = <0>;
		rockchip,disable-mmu-reset;
		status = "disabled";
	};

	rkisp1: rkisp@fdcc0000 {
		compatible = "rockchip,rk3588-rkisp";
		reg = <0x0 0xfdcc0000 0x0 0x7f00>;
		interrupts = <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "isp_irq", "mi_irq", "mipi_irq";
		clocks = <&cru ACLK_ISP1>, <&cru HCLK_ISP1>,
			 <&cru CLK_ISP1_CORE>, <&cru CLK_ISP1_CORE_MARVIN>,
			 <&cru CLK_ISP1_CORE_VICAP>;
		clock-names = "aclk_isp", "hclk_isp", "clk_isp_core",
			      "clk_isp_core_marvin", "clk_isp_core_vicap";
		power-domains = <&power RK3588_PD_ISP1>;
		iommus = <&isp1_mmu>;
		status = "disabled";
	};

	isp1_mmu: iommu@fdcc7f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdcc7f00 0x0 0x100>;
		interrupts = <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "isp1_mmu";
		clocks = <&cru ACLK_ISP1>, <&cru HCLK_ISP1>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_ISP1>;
		#iommu-cells = <0>;
		rockchip,disable-mmu-reset;
		status = "disabled";
	};

	rkispp0: rkispp@fdcd0000 {
		compatible = "rockchip,rk3588-rkispp";
		reg = <0x0 0xfdcd0000 0x0 0x0f00>;
		interrupts = <GIC_SPI 139 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "fec_irq";
		clocks = <&cru ACLK_FISHEYE0>, <&cru HCLK_FISHEYE0>,
			 <&cru CLK_FISHEYE0_CORE>;
		clock-names = "aclk_ispp", "hclk_ispp", "clk_ispp";
		assigned-clocks = <&cru HCLK_FISHEYE0>;
		assigned-clock-rates = <100000000>;
		power-domains = <&power RK3588_PD_FEC>;
		iommus = <&fec0_mmu>;
		status = "disabled";
	};

	fec0_mmu: iommu@fdcd0f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdcd0f00 0x0 0x100>;
		interrupts = <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "fec0_mmu";
		clocks = <&cru ACLK_FISHEYE0>, <&cru HCLK_FISHEYE0>, <&cru CLK_FISHEYE0_CORE>;
		clock-names = "aclk", "iface", "pclk";
		power-domains = <&power RK3588_PD_FEC>;
		#iommu-cells = <0>;
		rockchip,disable-mmu-reset;
		status = "disabled";
	};

	rkispp1: rkispp@fdcd8000 {
		compatible = "rockchip,rk3588-rkispp";
		reg = <0x0 0xfdcd8000 0x0 0x0f00>;
		interrupts = <GIC_SPI 141 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "fec_irq";
		clocks = <&cru ACLK_FISHEYE1>, <&cru HCLK_FISHEYE1>,
			 <&cru CLK_FISHEYE1_CORE>;
		clock-names = "aclk_ispp", "hclk_ispp", "clk_ispp";
		assigned-clocks = <&cru HCLK_FISHEYE1>;
		assigned-clock-rates = <100000000>;
		power-domains = <&power RK3588_PD_FEC>;
		iommus = <&fec1_mmu>;
		status = "disabled";
	};

	fec1_mmu: iommu@fdcd8f00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdcd8f00 0x0 0x100>;
		interrupts = <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "fec1_mmu";
		clocks = <&cru ACLK_FISHEYE1>, <&cru HCLK_FISHEYE1>,  <&cru CLK_FISHEYE1_CORE>;
		clock-names = "aclk", "iface", "pclk";
		power-domains = <&power RK3588_PD_FEC>;
		#iommu-cells = <0>;
		rockchip,disable-mmu-reset;
		status = "disabled";
	};

	rkcif: rkcif@fdce0000 {
		compatible = "rockchip,rk3588-cif";
		reg = <0x0 0xfdce0000 0x0 0x800>;
		reg-names = "cif_regs";
		interrupts = <GIC_SPI 155 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "cif-intr";
		clocks = <&cru ACLK_VICAP>, <&cru HCLK_VICAP>, <&cru DCLK_VICAP>,
			 <&cru ICLK_CSIHOST0>, <&cru ICLK_CSIHOST1>;
		clock-names = "aclk_cif", "hclk_cif", "dclk_cif",
			      "iclk_host0", "iclk_host1";
		resets = <&cru SRST_A_VICAP>, <&cru SRST_H_VICAP>, <&cru SRST_D_VICAP>,
			 <&cru SRST_CSIHOST0_VICAP>, <&cru SRST_CSIHOST1_VICAP>,
			 <&cru SRST_CSIHOST2_VICAP>, <&cru SRST_CSIHOST3_VICAP>,
			 <&cru SRST_CSIHOST4_VICAP>, <&cru SRST_CSIHOST5_VICAP>;
		reset-names = "rst_cif_a", "rst_cif_h", "rst_cif_d",
			      "rst_cif_host0", "rst_cif_host1", "rst_cif_host2",
			      "rst_cif_host3", "rst_cif_host4", "rst_cif_host5";
		assigned-clocks = <&cru DCLK_VICAP>;
		assigned-clock-rates = <600000000>;
		power-domains = <&power RK3588_PD_VI>;
		rockchip,grf = <&sys_grf>;
		iommus = <&rkcif_mmu>;
		nvmem-cells = <&specification_serial_number>,
			      <&package_serial_number_low>,
			      <&package_serial_number_high>;
		nvmem-cell-names = "specification",
				   "package_low",
				   "package_high";
		status = "disabled";
	};

	rkcif_mmu: iommu@fdce0800 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdce0800 0x0 0x100>,
		      <0x0 0xfdce0900 0x0 0x100>;
		interrupts = <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "cif_mmu";
		clocks = <&cru ACLK_VICAP>, <&cru HCLK_VICAP>;
		clock-names = "aclk", "iface";
		power-domains = <&power RK3588_PD_VI>;
		rockchip,disable-mmu-reset;
		#iommu-cells = <0>;
		status = "disabled";
	};

	mipi0_csi2_hw: mipi0-csi2-hw@fdd10000 {
		compatible = "rockchip,rk3588-mipi-csi2-hw";
		reg = <0x0 0xfdd10000 0x0 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "csi-intr1", "csi-intr2";
		clocks = <&cru PCLK_CSI_HOST_0>;
		clock-names = "pclk_csi2host";
		resets = <&cru SRST_P_CSI_HOST_0>;
		reset-names = "srst_csihost_p";
		status = "okay";
	};

	mipi1_csi2_hw: mipi1-csi2-hw@fdd20000 {
		compatible = "rockchip,rk3588-mipi-csi2-hw";
		reg = <0x0 0xfdd20000 0x0 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "csi-intr1", "csi-intr2";
		clocks = <&cru PCLK_CSI_HOST_1>;
		clock-names = "pclk_csi2host";
		resets = <&cru SRST_P_CSI_HOST_1>;
		reset-names = "srst_csihost_p";
		status = "okay";
	};

	mipi2_csi2_hw: mipi2-csi2-hw@fdd30000 {
		compatible = "rockchip,rk3588-mipi-csi2-hw";
		reg = <0x0 0xfdd30000 0x0 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <GIC_SPI 147 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "csi-intr1", "csi-intr2";
		clocks = <&cru PCLK_CSI_HOST_2>;
		clock-names = "pclk_csi2host";
		resets = <&cru SRST_P_CSI_HOST_2>;
		reset-names = "srst_csihost_p";
		status = "okay";
	};

	mipi3_csi2_hw: mipi3-csi2-hw@fdd40000 {
		compatible = "rockchip,rk3588-mipi-csi2-hw";
		reg = <0x0 0xfdd40000 0x0 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <GIC_SPI 149 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 150 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "csi-intr1", "csi-intr2";
		clocks = <&cru PCLK_CSI_HOST_3>;
		clock-names = "pclk_csi2host";
		resets = <&cru SRST_P_CSI_HOST_3>;
		reset-names = "srst_csihost_p";
		status = "okay";
	};

	mipi4_csi2_hw: mipi4-csi2-hw@fdd50000 {
		compatible = "rockchip,rk3588-mipi-csi2-hw";
		reg = <0x0 0xfdd50000 0x0 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 152 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "csi-intr1", "csi-intr2";
		clocks = <&cru PCLK_CSI_HOST_4>;
		clock-names = "pclk_csi2host";
		resets = <&cru SRST_P_CSI_HOST_4>;
		reset-names = "srst_csihost_p";
		status = "okay";
	};

	mipi5_csi2_hw: mipi5-csi2-hw@fdd60000 {
		compatible = "rockchip,rk3588-mipi-csi2-hw";
		reg = <0x0 0xfdd60000 0x0 0x10000>;
		reg-names = "csihost_regs";
		interrupts = <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "csi-intr1", "csi-intr2";
		clocks = <&cru PCLK_CSI_HOST_5>;
		clock-names = "pclk_csi2host";
		resets = <&cru SRST_P_CSI_HOST_5>;
		reset-names = "srst_csihost_p";
		status = "okay";
	};

	vop: vop@fdd90000 {
		compatible = "rockchip,rk3588-vop";
		reg = <0x0 0xfdd90000 0x0 0x4200>, <0x0 0xfdd95000 0x0 0x1000>;
		reg-names = "regs", "gamma_lut";
		interrupts = <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_VOP>,
			 <&cru HCLK_VOP>,
			 <&cru DCLK_VOP0>,
			 <&cru DCLK_VOP1>,
			 <&cru DCLK_VOP2>,
			 <&cru DCLK_VOP3>,
			 <&cru PCLK_VOP_ROOT>,
			 <&cru DCLK_VOP0_SRC>,
			 <&cru DCLK_VOP1_SRC>,
			 <&cru DCLK_VOP2_SRC>,
			 <&cru ACLK_VOP_DOBY>,
			 <&cru ACLK_VOP_DIV2_SRC>,
			 <&cru ACLK_VOP_ROOT>;
		clock-names = "aclk_vop",
			      "hclk_vop",
			      "dclk_vp0",
			      "dclk_vp1",
			      "dclk_vp2",
			      "dclk_vp3",
			      "pclk_vop",
			      "dclk_src_vp0",
			      "dclk_src_vp1",
			      "dclk_src_vp2",
			      "aclk_dovi",
			      "aclk_vop_div2_src",
			      "aclk_vop_root";
		assigned-clocks = <&cru ACLK_VOP>;
		assigned-clock-rates = <750000000>;
		resets = <&cru SRST_A_VOP>,
			 <&cru SRST_H_VOP>,
			 <&cru SRST_D_VOP0>,
			 <&cru SRST_D_VOP1>,
			 <&cru SRST_D_VOP2>,
			 <&cru SRST_D_VOP3>;
		reset-names = "axi",
			      "ahb",
			      "dclk_vp0",
			      "dclk_vp1",
			      "dclk_vp2",
			      "dclk_vp3";
		rockchip,aclk-normal-mode-rates = <500000000>;
		rockchip,aclk-advanced-mode-rates = <750000000>;
		operating-points-v2 = <&vop_opp_table>;
		iommus = <&vop_mmu>;
		power-domains = <&power RK3588_PD_VOP>;
		rockchip,grf = <&sys_grf>;
		rockchip,vop-grf = <&vop_grf>;
		rockchip,vo1-grf = <&vo1_grf>;
		rockchip,pmu = <&pmu>;

		status = "disabled";

		vop_out: ports {
			#address-cells = <1>;
			#size-cells = <0>;

			vp0: port@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;

				vp0_out_dp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&dp0_in_vp0>;
				};

				vp0_out_edp0: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&edp0_in_vp0>;
				};

				vp0_out_hdmi0: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&hdmi0_in_vp0>;
				};
			};

			vp1: port@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;

				vp1_out_dp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&dp0_in_vp1>;
				};

				vp1_out_edp0: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&edp0_in_vp1>;
				};

				vp1_out_hdmi0: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&hdmi0_in_vp1>;
				};
			};

			vp2: port@2 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <2>;

				assigned-clocks = <&cru DCLK_VOP2_SRC>;
				assigned-clock-parents = <&cru PLL_V0PLL>;

				vp2_out_dp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&dp0_in_vp2>;
				};

				vp2_out_edp0: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&edp0_in_vp2>;
				};

				vp2_out_hdmi0: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&hdmi0_in_vp2>;
				};

				vp2_out_dsi0: endpoint@3 {
					reg = <3>;
					remote-endpoint = <&dsi0_in_vp2>;
				};

				vp2_out_dsi1: endpoint@4 {
					reg = <4>;
					remote-endpoint = <&dsi1_in_vp2>;
				};
			};

			vp3: port@3 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <3>;

				vp3_out_dsi0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&dsi0_in_vp3>;
				};

				vp3_out_dsi1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&dsi1_in_vp3>;
				};

				vp3_out_rgb: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&rgb_in_vp3>;
				};
			};
		};
	};

	vop_opp_table: vop-opp-table {
		compatible = "operating-points-v2";

		nvmem-cells = <&log_leakage>;
		nvmem-cell-names = "leakage";

		rockchip,init-freq = <750000>; /* KHz */

		rockchip,leakage-voltage-sel = <
			1	31	0
			32	44	1
			45	57	2
			58	254	3
		>;

		opp-5000000000 {
			opp-hz = /bits/ 64 <500000000>;
			opp-microvolt = <725000 725000 800000>;
			opp-microvolt-L1 = <700000 700000 800000>;
			opp-microvolt-L2 = <687500 687500 800000>;
			opp-microvolt-L3 = <675000 675000 800000>;
		};
		opp-7500000000 {
			opp-hz = /bits/ 64 <750000000>;
			opp-microvolt = <725000 725000 800000>;
			opp-microvolt-L1 = <700000 700000 800000>;
			opp-microvolt-L2 = <687500 687500 800000>;
			opp-microvolt-L3 = <675000 675000 800000>;
		};
		opp-8500000000 {
			opp-hz = /bits/ 64 <850000000>;
			opp-microvolt = <800000 800000 800000>;
			opp-microvolt-L1 = <775000 775000 800000>;
			opp-microvolt-L2 = <750000 750000 800000>;
			opp-microvolt-L3 = <750000 750000 800000>;
		};
	};

	vop_mmu: iommu@fdd97e00 {
		compatible = "rockchip,iommu-v2";
		reg = <0x0 0xfdd97e00 0x0 0x100>, <0x0 0xfdd97f00 0x0 0x100>;
		interrupts = <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "vop_mmu";
		clocks = <&cru ACLK_VOP>, <&cru HCLK_VOP>;
		clock-names = "aclk", "iface";
		#iommu-cells = <0>;
		rockchip,disable-device-link-resume;
		rockchip,shootdown-entire;
		status = "disabled";
	};

	spdif_tx2: spdif-tx@fddb0000 {
		compatible = "rockchip,rk3588-spdif", "rockchip,rk3568-spdif";
		reg = <0x0 0xfddb0000 0x0 0x1000>;
		interrupts = <GIC_SPI 195 IRQ_TYPE_LEVEL_HIGH>;
		dmas = <&dmac1 6>;
		dma-names = "tx";
		clock-names = "mclk", "hclk";
		clocks = <&cru MCLK_SPDIF2>, <&cru HCLK_SPDIF2_DP0>;
		assigned-clocks = <&cru CLK_SPDIF2_DP0_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		power-domains = <&power RK3588_PD_VO0>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s4_8ch: i2s@fddc0000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfddc0000 0x0 0x1000>;
		interrupts = <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S4_8CH_TX>, <&cru MCLK_I2S4_8CH_TX>, <&cru HCLK_I2S4_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		assigned-clocks = <&cru CLK_I2S4_8CH_TX_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac2 0>;
		dma-names = "tx";
		power-domains = <&power RK3588_PD_VO0>;
		resets = <&cru SRST_M_I2S4_8CH_TX>;
		reset-names = "tx-m";
		rockchip,playback-only;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif_tx3: spdif-tx@fdde0000 {
		compatible = "rockchip,rk3588-spdif", "rockchip,rk3568-spdif";
		reg = <0x0 0xfdde0000 0x0 0x1000>;
		interrupts = <GIC_SPI 196 IRQ_TYPE_LEVEL_HIGH>;
		dmas = <&dmac1 7>;
		dma-names = "tx";
		clock-names = "mclk", "hclk";
		clocks = <&cru MCLK_SPDIF3>, <&cru HCLK_SPDIF3>;
		assigned-clocks = <&cru CLK_SPDIF3_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		power-domains = <&power RK3588_PD_VO1>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s5_8ch: i2s@fddf0000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfddf0000 0x0 0x1000>;
		interrupts = <GIC_SPI 185 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S5_8CH_TX>, <&cru MCLK_I2S5_8CH_TX>, <&cru HCLK_I2S5_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		assigned-clocks = <&cru CLK_I2S5_8CH_TX_SRC>;
		assigned-clock-parents = <&cru PLL_GPLL>;
		dmas = <&dmac2 2>;
		dma-names = "tx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_I2S5_8CH_TX>;
		reset-names = "tx-m";
		rockchip,always-on;
		rockchip,hdmi-path;
		rockchip,playback-only;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s9_8ch: i2s@fddfc000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfddfc000 0x0 0x1000>;
		interrupts = <GIC_SPI 189 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S9_8CH_RX>, <&cru MCLK_I2S9_8CH_RX>, <&cru HCLK_I2S9_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		assigned-clocks = <&cru CLK_I2S9_8CH_RX_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac2 23>;
		dma-names = "rx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_I2S9_8CH_RX>;
		reset-names = "rx-m";
		rockchip,capture-only;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif_rx0: spdif-rx@fde08000 {
		compatible = "rockchip,rk3588-spdifrx", "rockchip,rk3308-spdifrx";
		reg = <0x0 0xfde08000 0x0 0x1000>;
		interrupts = <GIC_SPI 199 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_SPDIFRX0>, <&cru HCLK_SPDIFRX0>;
		clock-names = "mclk", "hclk";
		assigned-clocks = <&cru MCLK_SPDIFRX0>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac0 21>;
		dma-names = "rx";
		power-domains = <&power RK3588_PD_VO1>;
		resets = <&cru SRST_M_SPDIFRX0>;
		reset-names = "spdifrx-m";
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	dsi0: dsi@fde20000 {
		compatible = "rockchip,rk3588-mipi-dsi2";
		reg = <0x0 0xfde20000 0x0 0x10000>;
		interrupts = <GIC_SPI 167 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_DSIHOST0>, <&cru CLK_DSIHOST0>;
		clock-names = "pclk", "sys_clk";
		resets = <&cru SRST_P_DSIHOST0>;
		reset-names = "apb";
		power-domains = <&power RK3588_PD_VOP>;
		phys = <&mipidcphy0>;
		phy-names = "dcphy";
		rockchip,grf = <&vop_grf>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			dsi0_in: port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				dsi0_in_vp2: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp2_out_dsi0>;
					status = "disabled";
				};

				dsi0_in_vp3: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp3_out_dsi0>;
					status = "disabled";
				};
			};
		};
	};

	dsi1: dsi@fde30000 {
		compatible = "rockchip,rk3588-mipi-dsi2";
		reg = <0x0 0xfde30000 0x0 0x10000>;
		interrupts = <GIC_SPI 168 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_DSIHOST1>, <&cru CLK_DSIHOST1>;
		clock-names = "pclk", "sys_clk";
		resets = <&cru SRST_P_DSIHOST1>;
		reset-names = "apb";
		power-domains = <&power RK3588_PD_VOP>;
		phys = <&mipidcphy1>;
		phy-names = "dcphy";
		rockchip,grf = <&vop_grf>;
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			dsi1_in: port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				dsi1_in_vp2: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp2_out_dsi1>;
					status = "disabled";
				};

				dsi1_in_vp3: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp3_out_dsi1>;
					status = "disabled";
				};
			};
		};
	};

	hdcp0: hdcp@fde40000 {
		compatible = "rockchip,rk3588-hdcp";
		reg = <0x0 0xfde40000 0x0 0x80>;
		interrupts = <GIC_SPI 159 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_HDCP0>, <&cru PCLK_HDCP0>,
			 <&cru HCLK_HDCP0>, <&cru HCLK_HDCP_KEY0>,
			 <&cru ACLK_TRNG0>, <&cru PCLK_TRNG0>;
		clock-names = "aclk", "pclk", "hclk", "hclk_key", "aclk_trng", "pclk_trng";
		resets = <&cru SRST_HDCP0>, <&cru SRST_H_HDCP0>,
			 <&cru SRST_A_HDCP0>, <&cru SRST_H_HDCP_KEY0>,
			 <&cru SRST_P_TRNG0>;
		reset-names = "hdcp", "h_hdcp", "a_hdcp", "hdcp_key", "trng";
		power-domains = <&power RK3588_PD_VO0>;
		rockchip,vo-grf = <&vo0_grf>;
		status = "disabled";
	};

	dp0: dp@fde50000 {
		compatible = "rockchip,rk3588-dp";
		reg = <0x0 0xfde50000 0x0 0x4000>;
		interrupts = <GIC_SPI 161 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_DP0>, <&cru CLK_AUX16M_0>,
			 <&cru MCLK_I2S4_8CH_TX>, <&cru MCLK_SPDIF2_DP0>,
			 <&hclk_vo0>, <&cru CLK_DP0>;
		clock-names = "apb", "aux", "i2s", "spdif", "hclk", "hdcp";
		assigned-clocks = <&cru CLK_AUX16M_0>;
		assigned-clock-rates = <16000000>;
		resets = <&cru SRST_DP0>;
		phys = <&usbdp_phy0_dp>;
		power-domains = <&power RK3588_PD_VO0>;
		#sound-dai-cells = <1>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				dp0_in_vp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp0_out_dp0>;
					status = "disabled";
				};

				dp0_in_vp1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp1_out_dp0>;
					status = "disabled";
				};

				dp0_in_vp2: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&vp2_out_dp0>;
					status = "disabled";
				};
			};

			port@1 {
				reg = <1>;

				dp0_out: endpoint { };
			};
		};
	};

	hdcp1: hdcp@fde70000 {
		compatible = "rockchip,rk3588-hdcp";
		reg = <0x0 0xfde70000 0x0 0x80>;
		interrupts = <GIC_SPI 160 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_HDCP1>, <&cru PCLK_HDCP1>,
			 <&cru HCLK_HDCP1>, <&cru HCLK_HDCP_KEY1>,
			 <&cru ACLK_TRNG1>, <&cru PCLK_TRNG1>;
		clock-names = "aclk", "pclk", "hclk", "hclk_key", "aclk_trng", "pclk_trng";
		resets = <&cru SRST_HDCP1>, <&cru SRST_H_HDCP1>,
			 <&cru SRST_A_HDCP1>, <&cru SRST_H_HDCP_KEY1>,
			 <&cru SRST_P_TRNG1>;
		reset-names = "hdcp", "h_hdcp", "a_hdcp", "hdcp_key", "trng";
		power-domains = <&power RK3588_PD_VO1>;
		rockchip,vo-grf = <&vo1_grf>;
		status = "disabled";
	};

	hdmi0: hdmi@fde80000 {
		compatible = "rockchip,rk3588-dw-hdmi";
		reg = <0x0 0xfde80000 0x0 0x10000>, <0x0 0xfde90000 0x0 0x10000>;
		interrupts = <GIC_SPI 169 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 170 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 171 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 172 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 360 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_HDMITX0>,
			 <&cru CLK_HDMIHDP0>,
			 <&cru CLK_HDMITX0_EARC>,
			 <&cru CLK_HDMITX0_REF>,
			 <&cru MCLK_I2S5_8CH_TX>,
			 <&cru DCLK_VOP0>,
			 <&cru DCLK_VOP1>,
			 <&cru DCLK_VOP2>,
			 <&cru DCLK_VOP3>,
			 <&hclk_vo1>,
			 <&hdptxphy_hdmi0>;
		clock-names = "pclk",
			      "hpd",
			      "earc",
			      "hdmitx_ref",
			      "aud",
			      "dclk_vp0",
			      "dclk_vp1",
			      "dclk_vp2",
			      "dclk_vp3",
			      "hclk_vo1",
			      "link_clk";
		resets = <&cru SRST_HDMITX0_REF>, <&cru SRST_HDMIHDP0>;
		reset-names = "ref", "hdp";
		power-domains = <&power RK3588_PD_VO1>;
		pinctrl-names = "default";
		pinctrl-0 = <&hdmim0_tx0_cec &hdmim0_tx0_hpd &hdmim0_tx0_scl &hdmim0_tx0_sda>;
		reg-io-width = <4>;
		rockchip,grf = <&sys_grf>;
		rockchip,vo1_grf = <&vo1_grf>;
		phys = <&hdptxphy_hdmi0>;
		phy-names = "hdmi";
		#sound-dai-cells = <0>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			hdmi0_in: port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				hdmi0_in_vp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp0_out_hdmi0>;
					status = "disabled";
				};

				hdmi0_in_vp1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp1_out_hdmi0>;
					status = "disabled";
				};

				hdmi0_in_vp2: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&vp2_out_hdmi0>;
					status = "disabled";
				};
			};
		};
	};

	edp0: edp@fdec0000 {
		compatible = "rockchip,rk3588-edp";
		reg = <0x0 0xfdec0000 0x0 0x1000>;
		interrupts = <GIC_SPI 163 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru CLK_EDP0_24M>, <&cru PCLK_EDP0>,
			 <&cru CLK_EDP0_200M>, <&hclk_vo1>;
		clock-names = "dp", "pclk", "spdif", "hclk";
		resets = <&cru SRST_EDP0_24M>, <&cru SRST_P_EDP0>;
		reset-names = "dp", "apb";
		phys = <&hdptxphy0>;
		phy-names = "dp";
		power-domains = <&power RK3588_PD_VO1>;
		rockchip,grf = <&vo1_grf>;
		#sound-dai-cells = <1>;
		status = "disabled";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				#address-cells = <1>;
				#size-cells = <0>;

				edp0_in_vp0: endpoint@0 {
					reg = <0>;
					remote-endpoint = <&vp0_out_edp0>;
					status = "disabled";
				};

				edp0_in_vp1: endpoint@1 {
					reg = <1>;
					remote-endpoint = <&vp1_out_edp0>;
					status = "disabled";
				};

				edp0_in_vp2: endpoint@2 {
					reg = <2>;
					remote-endpoint = <&vp2_out_edp0>;
					status = "disabled";
				};
			};

			port@1 {
				reg = <1>;

				edp0_out: endpoint { };
			};
		};
	};

	qos_gpu_m0: qos@fdf35000 {
		compatible = "syscon";
		reg = <0x0 0xfdf35000 0x0 0x20>;
	};

	qos_gpu_m1: qos@fdf35200 {
		compatible = "syscon";
		reg = <0x0 0xfdf35200 0x0 0x20>;
	};

	qos_gpu_m2: qos@fdf35400 {
		compatible = "syscon";
		reg = <0x0 0xfdf35400 0x0 0x20>;
	};

	qos_gpu_m3: qos@fdf35600 {
		compatible = "syscon";
		reg = <0x0 0xfdf35600 0x0 0x20>;
	};

	qos_rga3_1: qos@fdf36000 {
		compatible = "syscon";
		reg = <0x0 0xfdf36000 0x0 0x20>;
	};

	qos_sdio: qos@fdf39000 {
		compatible = "syscon";
		reg = <0x0 0xfdf39000 0x0 0x20>;
	};

	qos_gic600_m0: qos@fdf3a000 {
		compatible = "syscon";
		reg = <0x0 0xfdf3a000 0x0 0x20>;
	};

	qos_gic600_m1: qos@fdf3a200 {
		compatible = "syscon";
		reg = <0x0 0xfdf3a200 0x0 0x20>;
	};

	qos_sdmmc: qos@fdf3d800 {
		compatible = "syscon";
		reg = <0x0 0xfdf3d800 0x0 0x20>;
	};

	qos_usb3_1: qos@fdf3e000 {
		compatible = "syscon";
		reg = <0x0 0xfdf3e000 0x0 0x20>;
	};

	qos_usb3_0: qos@fdf3e200 {
		compatible = "syscon";
		reg = <0x0 0xfdf3e200 0x0 0x20>;
	};

	qos_usb2host_0: qos@fdf3e400 {
		compatible = "syscon";
		reg = <0x0 0xfdf3e400 0x0 0x20>;
	};

	qos_usb2host_1: qos@fdf3e600 {
		compatible = "syscon";
		reg = <0x0 0xfdf3e600 0x0 0x20>;
	};

	qos_fisheye0: qos@fdf40000 {
		compatible = "syscon";
		reg = <0x0 0xfdf40000 0x0 0x20>;
	};

	qos_fisheye1: qos@fdf40200 {
		compatible = "syscon";
		reg = <0x0 0xfdf40200 0x0 0x20>;
	};

	qos_isp0_mro: qos@fdf40400 {
		compatible = "syscon";
		reg = <0x0 0xfdf40400 0x0 0x20>;
	};

	qos_isp0_mwo: qos@fdf40500 {
		compatible = "syscon";
		reg = <0x0 0xfdf40500 0x0 0x20>;
	};

	qos_vicap_m0: qos@fdf40600 {
		compatible = "syscon";
		reg = <0x0 0xfdf40600 0x0 0x20>;
	};

	qos_vicap_m1: qos@fdf40800 {
		compatible = "syscon";
		reg = <0x0 0xfdf40800 0x0 0x20>;
	};

	qos_isp1_mwo: qos@fdf41000 {
		compatible = "syscon";
		reg = <0x0 0xfdf41000 0x0 0x20>;
	};

	qos_isp1_mro: qos@fdf41100 {
		compatible = "syscon";
		reg = <0x0 0xfdf41100 0x0 0x20>;
	};

	qos_rkvenc0_m0ro: qos@fdf60000 {
		compatible = "syscon";
		reg = <0x0 0xfdf60000 0x0 0x20>;
	};

	qos_rkvenc0_m1ro: qos@fdf60200 {
		compatible = "syscon";
		reg = <0x0 0xfdf60200 0x0 0x20>;
	};

	qos_rkvenc0_m2wo: qos@fdf60400 {
		compatible = "syscon";
		reg = <0x0 0xfdf60400 0x0 0x20>;
	};

	qos_rkvenc1_m0ro: qos@fdf61000 {
		compatible = "syscon";
		reg = <0x0 0xfdf61000 0x0 0x20>;
	};

	qos_rkvenc1_m1ro: qos@fdf61200 {
		compatible = "syscon";
		reg = <0x0 0xfdf61200 0x0 0x20>;
	};

	qos_rkvenc1_m2wo: qos@fdf61400 {
		compatible = "syscon";
		reg = <0x0 0xfdf61400 0x0 0x20>;
	};

	qos_rkvdec0: qos@fdf62000 {
		compatible = "syscon";
		reg = <0x0 0xfdf62000 0x0 0x20>;
	};

	qos_rkvdec1: qos@fdf63000 {
		compatible = "syscon";
		reg = <0x0 0xfdf63000 0x0 0x20>;
	};

	qos_av1: qos@fdf64000 {
		compatible = "syscon";
		reg = <0x0 0xfdf64000 0x0 0x20>;
	};

	qos_iep: qos@fdf66000 {
		compatible = "syscon";
		reg = <0x0 0xfdf66000 0x0 0x20>;
	};

	qos_jpeg_dec: qos@fdf66200 {
		compatible = "syscon";
		reg = <0x0 0xfdf66200 0x0 0x20>;
	};

	qos_jpeg_enc0: qos@fdf66400 {
		compatible = "syscon";
		reg = <0x0 0xfdf66400 0x0 0x20>;
	};

	qos_jpeg_enc1: qos@fdf66600 {
		compatible = "syscon";
		reg = <0x0 0xfdf66600 0x0 0x20>;
	};

	qos_jpeg_enc2: qos@fdf66800 {
		compatible = "syscon";
		reg = <0x0 0xfdf66800 0x0 0x20>;
	};

	qos_jpeg_enc3: qos@fdf66a00 {
		compatible = "syscon";
		reg = <0x0 0xfdf66a00 0x0 0x20>;
	};

	qos_rga2_mro: qos@fdf66c00 {
		compatible = "syscon";
		reg = <0x0 0xfdf66c00 0x0 0x20>;
	};

	qos_rga2_mwo: qos@fdf66e00 {
		compatible = "syscon";
		reg = <0x0 0xfdf66e00 0x0 0x20>;
	};

	qos_rga3_0: qos@fdf67000 {
		compatible = "syscon";
		reg = <0x0 0xfdf67000 0x0 0x20>;
	};

	qos_vdpu: qos@fdf67200 {
		compatible = "syscon";
		reg = <0x0 0xfdf67200 0x0 0x20>;
	};

	qos_npu1: qos@fdf70000 {
		compatible = "syscon";
		reg = <0x0 0xfdf70000 0x0 0x20>;
	};

	qos_npu2: qos@fdf71000 {
		compatible = "syscon";
		reg = <0x0 0xfdf71000 0x0 0x20>;
	};

	qos_npu0_mwr: qos@fdf72000 {
		compatible = "syscon";
		reg = <0x0 0xfdf72000 0x0 0x20>;
	};

	qos_npu0_mro: qos@fdf72200 {
		compatible = "syscon";
		reg = <0x0 0xfdf72200 0x0 0x20>;
	};

	qos_mcu_npu: qos@fdf72400 {
		compatible = "syscon";
		reg = <0x0 0xfdf72400 0x0 0x20>;
	};

	qos_hdcp0: qos@fdf80000 {
		compatible = "syscon";
		reg = <0x0 0xfdf80000 0x0 0x20>;
	};

	qos_hdcp1: qos@fdf81000 {
		compatible = "syscon";
		reg = <0x0 0xfdf81000 0x0 0x20>;
	};

	qos_hdmirx: qos@fdf81200 {
		compatible = "syscon";
		reg = <0x0 0xfdf81200 0x0 0x20>;
	};

	qos_vop_m0: qos@fdf82000 {
		compatible = "syscon";
		reg = <0x0 0xfdf82000 0x0 0x20>;
	};

	qos_vop_m1: qos@fdf82200 {
		compatible = "syscon";
		reg = <0x0 0xfdf82200 0x0 0x20>;
	};

	dfi: dfi@fe060000 {
		compatible = "rockchip,rk3588-dfi";
		reg = <0x00 0xfe060000 0x00 0x10000>;
		rockchip,pmu_grf = <&pmu1_grf>;
		clocks = <&cru PCLK_DDR_MON_CH0>, <&cru PCLK_DDR_MON_CH1>,
			 <&cru PCLK_DDR_MON_CH2>, <&cru PCLK_DDR_MON_CH3>;
		clock-names = "pclk_ddr_mon_ch0", "pclk_ddr_mon_ch1",
			      "pclk_ddr_mon_ch2", "pclk_ddr_mon_ch3";
		status = "disabled";
	};

	pcie2x1l1: pcie@fe180000 {
		compatible = "rockchip,rk3588-pcie", "snps,dw-pcie";
		#address-cells = <3>;
		#size-cells = <2>;
		bus-range = <0x30 0x3f>;
		clocks = <&cru ACLK_PCIE_1L1_MSTR>, <&cru ACLK_PCIE_1L1_SLV>,
			 <&cru ACLK_PCIE_1L1_DBI>, <&cru PCLK_PCIE_1L1>,
			 <&cru CLK_PCIE_AUX3>, <&cru CLK_PCIE1L1_PIPE>;
		clock-names = "aclk_mst", "aclk_slv",
			      "aclk_dbi", "pclk",
			      "aux", "pipe";
		device_type = "pci";
		interrupts = <GIC_SPI 248 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 247 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 246 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 245 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 244 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "sys", "pmc", "msg", "legacy", "err";
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pcie2x1l1_intc 0>,
				<0 0 0 2 &pcie2x1l1_intc 1>,
				<0 0 0 3 &pcie2x1l1_intc 2>,
				<0 0 0 4 &pcie2x1l1_intc 3>;
		linux,pci-domain = <3>;
		num-ib-windows = <8>;
		num-ob-windows = <8>;
		num-viewport = <4>;
		max-link-speed = <2>;
		msi-map = <0x3000 &its0 0x3000 0x1000>;
		num-lanes = <1>;
		phys = <&combphy2_psu PHY_TYPE_PCIE>;
		phy-names = "pcie-phy";
		ranges = <0x81000000 0x0 0xf3100000 0x0 0xf3100000 0x0 0x100000
			  0x82000000 0x0 0xf3200000 0x0 0xf3200000 0x0 0xe00000
			  0xc3000000 0x9 0xc0000000 0x9 0xc0000000 0x0 0x40000000>;
		reg = <0x0 0xfe180000 0x0 0x10000>,
		      <0xa 0x40c00000 0x0 0x400000>,
		      <0x0 0xf3000000 0x0 0x100000>;
		reg-names = "pcie-apb", "pcie-dbi", "config";
		resets = <&cru SRST_PCIE3_POWER_UP>, <&cru SRST_P_PCIE3>;
		reset-names = "pcie", "periph";
		rockchip,pipe-grf = <&php_grf>;
		status = "disabled";

		pcie2x1l1_intc: legacy-interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
			interrupt-parent = <&gic>;
			interrupts = <GIC_SPI 245 IRQ_TYPE_EDGE_RISING>;
		};
	};

	pcie2x1l2: pcie@fe190000 {
		compatible = "rockchip,rk3588-pcie", "snps,dw-pcie";
		#address-cells = <3>;
		#size-cells = <2>;
		bus-range = <0x40 0x4f>;
		clocks = <&cru ACLK_PCIE_1L2_MSTR>, <&cru ACLK_PCIE_1L2_SLV>,
			 <&cru ACLK_PCIE_1L2_DBI>, <&cru PCLK_PCIE_1L2>,
			 <&cru CLK_PCIE_AUX4>, <&cru CLK_PCIE1L2_PIPE>;
		clock-names = "aclk_mst", "aclk_slv",
			      "aclk_dbi", "pclk",
			      "aux", "pipe";
		device_type = "pci";
		interrupts = <GIC_SPI 253 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 252 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 251 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 250 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 249 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "sys", "pmc", "msg", "legacy", "err";
		#interrupt-cells = <1>;
		interrupt-map-mask = <0 0 0 7>;
		interrupt-map = <0 0 0 1 &pcie2x1l2_intc 0>,
				<0 0 0 2 &pcie2x1l2_intc 1>,
				<0 0 0 3 &pcie2x1l2_intc 2>,
				<0 0 0 4 &pcie2x1l2_intc 3>;
		linux,pci-domain = <4>;
		num-ib-windows = <8>;
		num-ob-windows = <8>;
		num-viewport = <4>;
		max-link-speed = <2>;
		msi-map = <0x4000 &its0 0x4000 0x1000>;
		num-lanes = <1>;
		phys = <&combphy0_ps PHY_TYPE_PCIE>;
		phy-names = "pcie-phy";
		ranges = <0x81000000 0x0 0xf4100000 0x0 0xf4100000 0x0 0x100000
			  0x82000000 0x0 0xf4200000 0x0 0xf4200000 0x0 0xe00000
			  0xc3000000 0xa 0x00000000 0xa 0x00000000 0x0 0x40000000>;
		reg = <0x0 0xfe190000 0x0 0x10000>,
		      <0xa 0x41000000 0x0 0x400000>,
		      <0x0 0xf4000000 0x0 0x100000>;
		reg-names = "pcie-apb", "pcie-dbi", "config";
		resets = <&cru SRST_PCIE4_POWER_UP>, <&cru SRST_P_PCIE4>;
		reset-names = "pcie", "periph";
		rockchip,pipe-grf = <&php_grf>;
		status = "disabled";

		pcie2x1l2_intc: legacy-interrupt-controller {
			interrupt-controller;
			#address-cells = <0>;
			#interrupt-cells = <1>;
			interrupt-parent = <&gic>;
			interrupts = <GIC_SPI 250 IRQ_TYPE_EDGE_RISING>;
		};
	};

	gmac_uio1: uio@fe1c0000 {
		compatible = "rockchip,uio-gmac";
		reg = <0x0 0xfe1c0000 0x0 0x10000>;
		rockchip,ethernet = <&gmac1>;
		status = "disabled";
	};

	gmac1: ethernet@fe1c0000 {
		compatible = "rockchip,rk3588-gmac", "snps,dwmac-4.20a";
		reg = <0x0 0xfe1c0000 0x0 0x10000>;
		interrupts = <GIC_SPI 234 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 233 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "macirq", "eth_wake_irq";
		rockchip,grf = <&sys_grf>;
		rockchip,php_grf = <&php_grf>;
		clocks = <&cru CLK_GMAC_125M>, <&cru CLK_GMAC_50M>,
			 <&cru PCLK_GMAC1>, <&cru ACLK_GMAC1>,
			 <&cru CLK_GMAC1_PTP_REF>;
		clock-names = "stmmaceth", "clk_mac_ref",
			      "pclk_mac", "aclk_mac",
			      "ptp_ref";
		resets = <&cru SRST_A_GMAC1>;
		reset-names = "stmmaceth";
		power-domains = <&power RK3588_PD_GMAC>;

		snps,mixed-burst;
		snps,tso;

		snps,axi-config = <&gmac1_stmmac_axi_setup>;
		snps,mtl-rx-config = <&gmac1_mtl_rx_setup>;
		snps,mtl-tx-config = <&gmac1_mtl_tx_setup>;
		status = "disabled";

		mdio1: mdio {
			compatible = "snps,dwmac-mdio";
			#address-cells = <0x1>;
			#size-cells = <0x0>;
		};

		gmac1_stmmac_axi_setup: stmmac-axi-config {
			snps,wr_osr_lmt = <4>;
			snps,rd_osr_lmt = <8>;
			snps,blen = <0 0 0 0 16 8 4>;
		};

		gmac1_mtl_rx_setup: rx-queues-config {
			snps,rx-queues-to-use = <1>;
			queue0 {};
		};

		gmac1_mtl_tx_setup: tx-queues-config {
			snps,tx-queues-to-use = <1>;
			queue0 {};
		};
	};

	sata0: sata@fe210000 {
		compatible = "rockchip,rk-ahci", "snps,dwc-ahci";
		reg = <0 0xfe210000 0 0x1000>;
		clocks = <&cru ACLK_SATA0>, <&cru CLK_PMALIVE0>,
			 <&cru CLK_RXOOB0>, <&cru CLK_PIPEPHY0_REF>,
			 <&cru CLK_PIPEPHY0_PIPE_ASIC_G>;
		clock-names = "sata", "pmalive", "rxoob", "ref", "asic";
		interrupts = <GIC_SPI 273 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "hostc";
		phys = <&combphy0_ps PHY_TYPE_SATA>;
		phy-names = "sata-phy";
		ports-implemented = <0x1>;
		status = "disabled";
	};

	sata2: sata@fe230000 {
		compatible = "rockchip,rk-ahci", "snps,dwc-ahci";
		reg = <0 0xfe230000 0 0x1000>;
		clocks = <&cru ACLK_SATA2>, <&cru CLK_PMALIVE2>,
			 <&cru CLK_RXOOB2>, <&cru CLK_PIPEPHY2_REF>,
			 <&cru CLK_PIPEPHY2_PIPE_ASIC_G>;
		clock-names = "sata", "pmalive", "rxoob", "ref", "asic";
		interrupts = <GIC_SPI 275 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "hostc";
		phys = <&combphy2_psu PHY_TYPE_SATA>;
		phy-names = "sata-phy";
		ports-implemented = <0x1>;
		status = "disabled";
	};

	sfc: spi@fe2b0000 {
		compatible = "rockchip,sfc";
		reg = <0x0 0xfe2b0000 0x0 0x4000>;
		interrupts = <GIC_SPI 206 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_SFC>, <&cru HCLK_SFC>;
		clock-names = "clk_sfc", "hclk_sfc";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	sdmmc: mmc@fe2c0000 {
		compatible = "rockchip,rk3588-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0x0 0xfe2c0000 0x0 0x4000>;
		interrupts = <GIC_SPI 203 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&scmi_clk SCMI_HCLK_SD>, <&scmi_clk SCMI_CCLK_SD>,
			 <&cru SCLK_SDMMC_DRV>, <&cru SCLK_SDMMC_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		fifo-depth = <0x100>;
		max-frequency = <200000000>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdmmc_clk &sdmmc_cmd &sdmmc_det &sdmmc_bus4>;
		power-domains = <&power RK3588_PD_SDMMC>;
		status = "disabled";
	};

	sdio: mmc@fe2d0000 {
		compatible = "rockchip,rk3588-dw-mshc", "rockchip,rk3288-dw-mshc";
		reg = <0x0 0xfe2d0000 0x0 0x4000>;
		interrupts = <GIC_SPI 204 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru HCLK_SDIO>, <&cru CCLK_SRC_SDIO>,
			 <&cru SCLK_SDIO_DRV>, <&cru SCLK_SDIO_SAMPLE>;
		clock-names = "biu", "ciu", "ciu-drive", "ciu-sample";
		fifo-depth = <0x100>;
		max-frequency = <200000000>;
		pinctrl-names = "default";
		pinctrl-0 = <&sdiom1_pins>;
		power-domains = <&power RK3588_PD_SDIO>;
		status = "disabled";
	};

	sdhci: mmc@fe2e0000 {
		compatible = "rockchip,rk3588-dwcmshc", "rockchip,dwcmshc-sdhci";
		reg = <0x0 0xfe2e0000 0x0 0x10000>;
		interrupts = <GIC_SPI 205 IRQ_TYPE_LEVEL_HIGH>;
		assigned-clocks = <&cru BCLK_EMMC>, <&cru TMCLK_EMMC>, <&cru CCLK_EMMC>;
		assigned-clock-rates = <200000000>, <24000000>, <200000000>;
		clocks = <&cru CCLK_EMMC>, <&cru HCLK_EMMC>,
			 <&cru ACLK_EMMC>, <&cru BCLK_EMMC>,
			 <&cru TMCLK_EMMC>;
		clock-names = "core", "bus", "axi", "block", "timer";
		resets = <&cru SRST_C_EMMC>, <&cru SRST_H_EMMC>,
			 <&cru SRST_A_EMMC>, <&cru SRST_B_EMMC>,
			 <&cru SRST_T_EMMC>;
		reset-names = "core", "bus", "axi", "block", "timer";
		max-frequency = <200000000>;
		supports-cqe;
		status = "disabled";
	};

	crypto: crypto@fe370000 {
		compatible = "rockchip,rk3588-crypto";
		reg = <0x0 0xfe370000 0x0 0x2000>;
		interrupts = <GIC_SPI 209 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&scmi_clk SCMI_ACLK_SECURE_NS>, <&scmi_clk SCMI_HCLK_SECURE_NS>,
			 <&scmi_clk SCMI_CRYPTO_CORE>, <&scmi_clk SCMI_CRYPTO_PKA>;
		clock-names = "aclk", "hclk", "sclk", "pka";
		resets = <&scmi_reset SRST_CRYPTO_CORE>;
		reset-names = "crypto-rst";
		status = "disabled";
	};

	rng: rng@fe378000 {
		compatible = "rockchip,trngv1";
		reg = <0x0 0xfe378000 0x0 0x200>;
		interrupts = <GIC_SPI 400 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&scmi_clk SCMI_HCLK_SECURE_NS>;
		clock-names = "hclk_trng";
		resets = <&scmi_reset SRST_H_TRNG_NS>;
		reset-names = "reset";
		status = "disabled";
	};

	i2s0_8ch: i2s@fe470000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfe470000 0x0 0x1000>;
		interrupts = <GIC_SPI 180 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S0_8CH_TX>, <&cru MCLK_I2S0_8CH_RX>, <&cru HCLK_I2S0_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		assigned-clocks = <&cru CLK_I2S0_8CH_TX_SRC>, <&cru CLK_I2S0_8CH_RX_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>, <&cru PLL_AUPLL>;
		dmas = <&dmac0 0>, <&dmac0 1>;
		dma-names = "tx", "rx";
		power-domains = <&power RK3588_PD_AUDIO>;
		resets = <&cru SRST_M_I2S0_8CH_TX>, <&cru SRST_M_I2S0_8CH_RX>;
		reset-names = "tx-m", "rx-m";
		rockchip,trcm-sync-tx-only;
		i2s-lrck-gpio = <&gpio1 RK_PC5 GPIO_ACTIVE_HIGH>; /* i2s0_lrck */
		pinctrl-names = "default", "idle", "clk";
		pinctrl-0 = <&i2s0_sdi0
			     &i2s0_sdi1
			     &i2s0_sdi2
			     &i2s0_sdi3
			     &i2s0_sdo0
			     &i2s0_sdo1>;
		pinctrl-1 = <&i2s0_idle>;
		pinctrl-2 = <&i2s0_lrck
			     &i2s0_sclk>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s1_8ch: i2s@fe480000 {
		compatible = "rockchip,rk3588-i2s-tdm";
		reg = <0x0 0xfe480000 0x0 0x1000>;
		interrupts = <GIC_SPI 181 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S1_8CH_TX>, <&cru MCLK_I2S1_8CH_RX>, <&cru HCLK_I2S1_8CH>;
		clock-names = "mclk_tx", "mclk_rx", "hclk";
		dmas = <&dmac0 2>, <&dmac0 3>;
		dma-names = "tx", "rx";
		resets = <&cru SRST_M_I2S1_8CH_TX>, <&cru SRST_M_I2S1_8CH_RX>;
		reset-names = "tx-m", "rx-m";
		rockchip,trcm-sync-tx-only;
		i2s-lrck-gpio = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>; /* i2s1m0_lrck */
		pinctrl-names = "default";
		pinctrl-0 = <&i2s1m0_lrck
			     &i2s1m0_sclk
			     &i2s1m0_sdi0
			     &i2s1m0_sdi1
			     &i2s1m0_sdi2
			     &i2s1m0_sdi3
			     &i2s1m0_sdo0
			     &i2s1m0_sdo1
			     &i2s1m0_sdo2
			     &i2s1m0_sdo3>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s2_2ch: i2s@fe490000 {
		compatible = "rockchip,rk3588-i2s", "rockchip,rk3066-i2s";
		reg = <0x0 0xfe490000 0x0 0x1000>;
		interrupts = <GIC_SPI 182 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S2_2CH>, <&cru HCLK_I2S2_2CH>;
		clock-names = "i2s_clk", "i2s_hclk";
		assigned-clocks = <&cru CLK_I2S2_2CH_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac1 0>, <&dmac1 1>;
		dma-names = "tx", "rx";
		power-domains = <&power RK3588_PD_AUDIO>;
		rockchip,trcm-sync-tx-only;
		pinctrl-names = "default", "idle", "clk";
		pinctrl-0 = <&i2s2m1_sdi
			     &i2s2m1_sdo>;
		pinctrl-1 = <&i2s2m1_idle>;
		pinctrl-2 = <&i2s2m1_lrck
			     &i2s2m1_sclk>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	i2s3_2ch: i2s@fe4a0000 {
		compatible = "rockchip,rk3588-i2s", "rockchip,rk3066-i2s";
		reg = <0x0 0xfe4a0000 0x0 0x1000>;
		interrupts = <GIC_SPI 183 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru MCLK_I2S3_2CH>, <&cru HCLK_I2S3_2CH>;
		clock-names = "i2s_clk", "i2s_hclk";
		assigned-clocks = <&cru CLK_I2S3_2CH_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac1 2>, <&dmac1 3>;
		dma-names = "tx", "rx";
		power-domains = <&power RK3588_PD_AUDIO>;
		rockchip,trcm-sync-tx-only;
		pinctrl-names = "default", "idle", "clk";
		pinctrl-0 = <&i2s3_sdi
			     &i2s3_sdo>;
		pinctrl-1 = <&i2s3_idle>;
		pinctrl-2 = <&i2s3_lrck
			     &i2s3_sclk>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	pdm0: pdm@fe4b0000 {
		compatible = "rockchip,rk3588-pdm";
		reg = <0x0 0xfe4b0000 0x0 0x1000>;
		clocks = <&cru MCLK_PDM0>, <&cru HCLK_PDM0>;
		clock-names = "pdm_clk", "pdm_hclk";
		dmas = <&dmac0 4>;
		dma-names = "rx";
		pinctrl-names = "default", "idle", "clk";
		pinctrl-0 = <&pdm0m0_sdi0
			     &pdm0m0_sdi1
			     &pdm0m0_sdi2
			     &pdm0m0_sdi3>;
		pinctrl-1 = <&pdm0m0_idle>;
		pinctrl-2 = <&pdm0m0_clk
			     &pdm0m0_clk1>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	pdm1: pdm@fe4c0000 {
		compatible = "rockchip,rk3588-pdm";
		reg = <0x0 0xfe4c0000 0x0 0x1000>;
		clocks = <&cru MCLK_PDM1>, <&cru HCLK_PDM1>;
		clock-names = "pdm_clk", "pdm_hclk";
		assigned-clocks = <&cru MCLK_PDM1>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		dmas = <&dmac1 4>;
		dma-names = "rx";
		power-domains = <&power RK3588_PD_AUDIO>;
		pinctrl-names = "default", "idle", "clk";
		pinctrl-0 = <&pdm1m0_sdi0
			     &pdm1m0_sdi1
			     &pdm1m0_sdi2
			     &pdm1m0_sdi3>;
		pinctrl-1 = <&pdm1m0_idle>;
		pinctrl-2 = <&pdm1m0_clk
			     &pdm1m0_clk1>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	vad: vad@fe4d0000 {
		compatible = "rockchip,rk3588-vad";
		reg = <0x0 0xfe4d0000 0x0 0x1000>;
		reg-names = "vad";
		clocks = <&cru HCLK_VAD>;
		clock-names = "hclk";
		interrupts = <GIC_SPI 202 IRQ_TYPE_LEVEL_HIGH>;
		rockchip,audio-src = <0>;
		rockchip,det-channel = <0>;
		rockchip,mode = <0>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif_tx0: spdif-tx@fe4e0000 {
		compatible = "rockchip,rk3588-spdif", "rockchip,rk3568-spdif";
		reg = <0x0 0xfe4e0000 0x0 0x1000>;
		interrupts = <GIC_SPI 193 IRQ_TYPE_LEVEL_HIGH>;
		dmas = <&dmac0 5>;
		dma-names = "tx";
		clock-names = "mclk", "hclk";
		clocks = <&cru MCLK_SPDIF0>, <&cru HCLK_SPDIF0>;
		assigned-clocks = <&cru CLK_SPDIF0_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		power-domains = <&power RK3588_PD_AUDIO>;
		pinctrl-names = "default";
		pinctrl-0 = <&spdif0m0_tx>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	spdif_tx1: spdif-tx@fe4f0000 {
		compatible = "rockchip,rk3588-spdif", "rockchip,rk3568-spdif";
		reg = <0x0 0xfe4f0000 0x0 0x1000>;
		interrupts = <GIC_SPI 194 IRQ_TYPE_LEVEL_HIGH>;
		dmas = <&dmac1 5>;
		dma-names = "tx";
		clock-names = "mclk", "hclk";
		clocks = <&cru MCLK_SPDIF1>, <&cru HCLK_SPDIF1>;
		assigned-clocks = <&cru CLK_SPDIF1_SRC>;
		assigned-clock-parents = <&cru PLL_AUPLL>;
		power-domains = <&power RK3588_PD_AUDIO>;
		pinctrl-names = "default";
		pinctrl-0 = <&spdif1m0_tx>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	acdcdig_dsm: codec-digital@fe500000 {
		compatible = "rockchip,rk3588-codec-digital", "rockchip,codec-digital-v1";
		reg = <0x0 0xfe500000 0x0 0x1000>;
		clocks = <&cru CLK_DAC_ACDCDIG>, <&cru PCLK_ACDCDIG>;
		clock-names = "dac", "pclk";
		power-domains = <&power RK3588_PD_AUDIO>;
		resets = <&cru SRST_DAC_ACDCDIG>;
		reset-names = "reset" ;
		rockchip,grf = <&sys_grf>;
		rockchip,pwm-output-mode;
		pinctrl-names = "default";
		pinctrl-0 = <&auddsm_pins>;
		#sound-dai-cells = <0>;
		status = "disabled";
	};

	hwlock: hwspinlock@fe5a0000 {
		compatible = "rockchip,hwspinlock";
		reg = <0 0xfe5a0000 0 0x100>;
		#hwlock-cells = <1>;
	};

	gic: interrupt-controller@fe600000 {
		compatible = "arm,gic-v3";
		#interrupt-cells = <3>;
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
		interrupt-controller;

		reg = <0x0 0xfe600000 0 0x10000>, /* GICD */
		      <0x0 0xfe680000 0 0x100000>; /* GICR */
		interrupts = <GIC_PPI 9 IRQ_TYPE_LEVEL_HIGH>;
		its0: msi-controller@fe640000 {
			compatible = "arm,gic-v3-its";
			msi-controller;
			#msi-cells = <1>;
			reg = <0x0 0xfe640000 0x0 0x20000>;
		};
		its1: msi-controller@fe660000 {
			compatible = "arm,gic-v3-its";
			msi-controller;
			#msi-cells = <1>;
			reg = <0x0 0xfe660000 0x0 0x20000>;
		};
	};

	dmac0: dma-controller@fea10000 {
		compatible = "arm,pl330", "arm,primecell";
		reg = <0x0 0xfea10000 0x0 0x4000>;
		interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_DMAC0>;
		clock-names = "apb_pclk";
		#dma-cells = <1>;
		arm,pl330-periph-burst;
	};

	dmac1: dma-controller@fea30000 {
		compatible = "arm,pl330", "arm,primecell";
		reg = <0x0 0xfea30000 0x0 0x4000>;
		interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_DMAC1>;
		clock-names = "apb_pclk";
		#dma-cells = <1>;
		arm,pl330-periph-burst;
	};

	can0: can@fea50000 {
		compatible = "rockchip,can-2.0";
		reg = <0x0 0xfea50000 0x0 0x1000>;
		interrupts = <GIC_SPI 341 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru CLK_CAN0>, <&cru PCLK_CAN0>;
		clock-names = "baudclk", "apb_pclk";
		resets = <&cru SRST_CAN0>, <&cru SRST_P_CAN0>;
		reset-names = "can", "can-apb";
		pinctrl-names = "default";
		pinctrl-0 = <&can0m0_pins>;
		tx-fifo-depth = <1>;
		rx-fifo-depth = <6>;
		status = "disabled";
	};

	can1: can@fea60000 {
		compatible = "rockchip,can-2.0";
		reg = <0x0 0xfea60000 0x0 0x1000>;
		interrupts = <GIC_SPI 342 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru CLK_CAN1>, <&cru PCLK_CAN1>;
		clock-names = "baudclk", "apb_pclk";
		resets = <&cru SRST_CAN1>, <&cru SRST_P_CAN1>;
		reset-names = "can", "can-apb";
		pinctrl-names = "default";
		pinctrl-0 = <&can1m0_pins>;
		tx-fifo-depth = <1>;
		rx-fifo-depth = <6>;
		status = "disabled";
	};

	can2: can@fea70000 {
		compatible = "rockchip,can-2.0";
		reg = <0x0 0xfea70000 0x0 0x1000>;
		interrupts = <GIC_SPI 343 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru CLK_CAN2>, <&cru PCLK_CAN2>;
		clock-names = "baudclk", "apb_pclk";
		resets = <&cru SRST_CAN2>, <&cru SRST_P_CAN2>;
		reset-names = "can", "can-apb";
		pinctrl-names = "default";
		pinctrl-0 = <&can2m0_pins>;
		tx-fifo-depth = <1>;
		rx-fifo-depth = <6>;
		status = "disabled";
	};

	hw_decompress: decompress@fea80000 {
		compatible = "rockchip,hw-decompress";
		reg = <0x0 0xfea80000 0x0 0x1000>;
		interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_DECOM>, <&cru DCLK_DECOM>, <&cru PCLK_DECOM>;
		clock-names = "aclk", "dclk", "pclk";
		resets = <&cru SRST_D_DECOM>;
		reset-names = "dresetn";
		status = "disabled";
	};

	i2c1: i2c@fea90000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfea90000 0x0 0x1000>;
		clocks = <&cru CLK_I2C1>, <&cru PCLK_I2C1>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 318 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c1m0_xfer>;
		resets = <&cru SRST_I2C1>, <&cru SRST_P_I2C1>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	i2c2: i2c@feaa0000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfeaa0000 0x0 0x1000>;
		clocks = <&cru CLK_I2C2>, <&cru PCLK_I2C2>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 319 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c2m0_xfer>;
		resets = <&cru SRST_I2C2>, <&cru SRST_P_I2C2>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	i2c3: i2c@feab0000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfeab0000 0x0 0x1000>;
		clocks = <&cru CLK_I2C3>, <&cru PCLK_I2C3>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 320 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c3m0_xfer>;
		resets = <&cru SRST_I2C3>, <&cru SRST_P_I2C3>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	i2c4: i2c@feac0000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfeac0000 0x0 0x1000>;
		clocks = <&cru CLK_I2C4>, <&cru PCLK_I2C4>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 321 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c4m0_xfer>;
		resets = <&cru SRST_I2C4>, <&cru SRST_P_I2C4>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	i2c5: i2c@fead0000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfead0000 0x0 0x1000>;
		clocks = <&cru CLK_I2C5>, <&cru PCLK_I2C5>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 322 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c5m0_xfer>;
		resets = <&cru SRST_I2C5>, <&cru SRST_P_I2C5>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	rktimer: timer@feae0000 {
		compatible = "rockchip,rk3588-timer", "rockchip,rk3288-timer";
		reg = <0x0 0xfeae0000 0x0 0x20>;
		interrupts = <GIC_SPI 289 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_BUSTIMER0>, <&cru CLK_BUSTIMER0>;
		clock-names = "pclk", "timer";
	};

	wdt: watchdog@feaf0000 {
		compatible = "snps,dw-wdt";
		reg = <0x0 0xfeaf0000 0x0 0x100>;
		clocks = <&cru TCLK_WDT0>, <&cru PCLK_WDT0>;
		clock-names = "tclk", "pclk";
		interrupts = <GIC_SPI 315 IRQ_TYPE_LEVEL_HIGH>;
		status = "disabled";
	};

	spi0: spi@feb00000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x0 0xfeb00000 0x0 0x1000>;
		interrupts = <GIC_SPI 326 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cru CLK_SPI0>, <&cru PCLK_SPI0>;
		clock-names = "spiclk", "apb_pclk";
		dmas = <&dmac0 14>, <&dmac0 15>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&spi0m0_cs0 &spi0m0_cs1 &spi0m0_pins>;
		num-cs = <2>;
		status = "disabled";
	};

	spi1: spi@feb10000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x0 0xfeb10000 0x0 0x1000>;
		interrupts = <GIC_SPI 327 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cru CLK_SPI1>, <&cru PCLK_SPI1>;
		clock-names = "spiclk", "apb_pclk";
		dmas = <&dmac0 16>, <&dmac0 17>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&spi1m1_cs0 &spi1m1_cs1 &spi1m1_pins>;
		num-cs = <2>;
		status = "disabled";
	};

	spi2: spi@feb20000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x0 0xfeb20000 0x0 0x1000>;
		interrupts = <GIC_SPI 328 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cru CLK_SPI2>, <&cru PCLK_SPI2>;
		clock-names = "spiclk", "apb_pclk";
		dmas = <&dmac1 15>, <&dmac1 16>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&spi2m2_cs0 &spi2m2_cs1 &spi2m2_pins>;
		num-cs = <2>;
		status = "disabled";
	};

	spi3: spi@feb30000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x0 0xfeb30000 0x0 0x1000>;
		interrupts = <GIC_SPI 329 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cru CLK_SPI3>, <&cru PCLK_SPI3>;
		clock-names = "spiclk", "apb_pclk";
		dmas = <&dmac1 17>, <&dmac1 18>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&spi3m1_cs0 &spi3m1_cs1 &spi3m1_pins>;
		num-cs = <2>;
		status = "disabled";
	};

	uart1: serial@feb40000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfeb40000 0x0 0x100>;
		interrupts = <GIC_SPI 332 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART1>, <&cru PCLK_UART1>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac0 8>, <&dmac0 9>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart1m1_xfer>;
		status = "disabled";
	};

	uart2: serial@feb50000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfeb50000 0x0 0x100>;
		interrupts = <GIC_SPI 333 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART2>, <&cru PCLK_UART2>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac0 10>, <&dmac0 11>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart2m1_xfer>;
		status = "disabled";
	};

	uart3: serial@feb60000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfeb60000 0x0 0x100>;
		interrupts = <GIC_SPI 334 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART3>, <&cru PCLK_UART3>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac0 12>, <&dmac0 13>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart3m1_xfer>;
		status = "disabled";
	};

	uart4: serial@feb70000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfeb70000 0x0 0x100>;
		interrupts = <GIC_SPI 335 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART4>, <&cru PCLK_UART4>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac1 9>, <&dmac1 10>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart4m1_xfer>;
		status = "disabled";
	};

	uart5: serial@feb80000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfeb80000 0x0 0x100>;
		interrupts = <GIC_SPI 336 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART5>, <&cru PCLK_UART5>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac1 11>, <&dmac1 12>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart5m1_xfer>;
		status = "disabled";
	};

	uart6: serial@feb90000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfeb90000 0x0 0x100>;
		interrupts = <GIC_SPI 337 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART6>, <&cru PCLK_UART6>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac1 13>, <&dmac1 14>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart6m1_xfer>;
		status = "disabled";
	};

	uart7: serial@feba0000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfeba0000 0x0 0x100>;
		interrupts = <GIC_SPI 338 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART7>, <&cru PCLK_UART7>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac2 7>, <&dmac2 8>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart7m1_xfer>;
		status = "disabled";
	};

	uart8: serial@febb0000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfebb0000 0x0 0x100>;
		interrupts = <GIC_SPI 339 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART8>, <&cru PCLK_UART8>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac2 9>, <&dmac2 10>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart8m1_xfer>;
		status = "disabled";
	};

	uart9: serial@febc0000 {
		compatible = "rockchip,rk3588-uart", "snps,dw-apb-uart";
		reg = <0x0 0xfebc0000 0x0 0x100>;
		interrupts = <GIC_SPI 340 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru SCLK_UART9>, <&cru PCLK_UART9>;
		clock-names = "baudclk", "apb_pclk";
		reg-shift = <2>;
		reg-io-width = <4>;
		dmas = <&dmac2 11>, <&dmac2 12>;
		pinctrl-names = "default";
		pinctrl-0 = <&uart9m1_xfer>;
		status = "disabled";
	};

	pwm4: pwm@febd0000 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebd0000 0x0 0x10>;
		interrupts = <GIC_SPI 346 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm4m0_pins>;
		clocks = <&cru CLK_PWM1>, <&cru PCLK_PWM1>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm5: pwm@febd0010 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebd0010 0x0 0x10>;
		interrupts = <GIC_SPI 346 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm5m0_pins>;
		clocks = <&cru CLK_PWM1>, <&cru PCLK_PWM1>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm6: pwm@febd0020 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebd0020 0x0 0x10>;
		interrupts = <GIC_SPI 346 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm6m0_pins>;
		clocks = <&cru CLK_PWM1>, <&cru PCLK_PWM1>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm7: pwm@febd0030 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebd0030 0x0 0x10>;
		interrupts = <GIC_SPI 346 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 347 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm7m0_pins>;
		clocks = <&cru CLK_PWM1>, <&cru PCLK_PWM1>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm8: pwm@febe0000 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebe0000 0x0 0x10>;
		interrupts = <GIC_SPI 348 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm8m0_pins>;
		clocks = <&cru CLK_PWM2>, <&cru PCLK_PWM2>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm9: pwm@febe0010 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebe0010 0x0 0x10>;
		interrupts = <GIC_SPI 348 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm9m0_pins>;
		clocks = <&cru CLK_PWM2>, <&cru PCLK_PWM2>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm10: pwm@febe0020 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebe0020 0x0 0x10>;
		interrupts = <GIC_SPI 348 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm10m0_pins>;
		clocks = <&cru CLK_PWM2>, <&cru PCLK_PWM2>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm11: pwm@febe0030 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebe0030 0x0 0x10>;
		interrupts = <GIC_SPI 348 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 349 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm11m0_pins>;
		clocks = <&cru CLK_PWM2>, <&cru PCLK_PWM2>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm12: pwm@febf0000 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebf0000 0x0 0x10>;
		interrupts = <GIC_SPI 350 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm12m0_pins>;
		clocks = <&cru CLK_PWM3>, <&cru PCLK_PWM3>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm13: pwm@febf0010 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebf0010 0x0 0x10>;
		interrupts = <GIC_SPI 350 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm13m0_pins>;
		clocks = <&cru CLK_PWM3>, <&cru PCLK_PWM3>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm14: pwm@febf0020 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebf0020 0x0 0x10>;
		interrupts = <GIC_SPI 350 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm14m0_pins>;
		clocks = <&cru CLK_PWM3>, <&cru PCLK_PWM3>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	pwm15: pwm@febf0030 {
		compatible = "rockchip,rk3588-pwm", "rockchip,rk3328-pwm";
		reg = <0x0 0xfebf0030 0x0 0x10>;
		interrupts = <GIC_SPI 350 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 351 IRQ_TYPE_LEVEL_HIGH>;
		#pwm-cells = <3>;
		pinctrl-names = "active";
		pinctrl-0 = <&pwm15m0_pins>;
		clocks = <&cru CLK_PWM3>, <&cru PCLK_PWM3>;
		clock-names = "pwm", "pclk";
		status = "disabled";
	};

	tsadc: tsadc@fec00000 {
		compatible = "rockchip,rk3588-tsadc";
		reg = <0x0 0xfec00000 0x0 0x400>;
		interrupts = <GIC_SPI 397 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru CLK_TSADC>, <&cru PCLK_TSADC>;
		clock-names = "tsadc", "apb_pclk";
		assigned-clocks = <&cru CLK_TSADC>;
		assigned-clock-rates = <2000000>;
		resets = <&cru SRST_TSADC>, <&cru SRST_P_TSADC>;
		reset-names = "tsadc", "tsadc-apb";
		#thermal-sensor-cells = <1>;
		rockchip,hw-tshut-temp = <120000>;
		rockchip,hw-tshut-mode = <0>; /* tshut mode 0:CRU 1:GPIO */
		rockchip,hw-tshut-polarity = <0>; /* tshut polarity 0:LOW 1:HIGH */
		pinctrl-names = "gpio", "otpout";
		pinctrl-0 = <&tsadc_gpio_func>;
		pinctrl-1 = <&tsadc_shut>;
		status = "disabled";
	};

	saradc: saradc@fec10000 {
		compatible = "rockchip,rk3588-saradc";
		reg = <0x0 0xfec10000 0x0 0x10000>;
		interrupts = <GIC_SPI 398 IRQ_TYPE_LEVEL_HIGH>;
		#io-channel-cells = <1>;
		clocks = <&cru CLK_SARADC>, <&cru PCLK_SARADC>;
		clock-names = "saradc", "apb_pclk";
		resets = <&cru SRST_P_SARADC>;
		reset-names = "saradc-apb";
		status = "disabled";
	};

	mailbox0: mailbox@fec60000 {
		compatible = "rockchip,rk3588-mailbox",
			     "rockchip,rk3368-mailbox";
		reg = <0x0 0xfec60000 0x0 0x200>;
		interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 63 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_MAILBOX0>;
		clock-names = "pclk_mailbox";
		#mbox-cells = <1>;
		status = "disabled";
	};

	mailbox1: mailbox@fec70000 {
		compatible = "rockchip,rk3588-mailbox",
			     "rockchip,rk3368-mailbox";
		reg = <0x0 0xfec70000 0x0 0x200>;
		interrupts = <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_MAILBOX1>;
		clock-names = "pclk_mailbox";
		#mbox-cells = <1>;
		status = "disabled";
	};

	i2c6: i2c@fec80000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfec80000 0x0 0x1000>;
		clocks = <&cru CLK_I2C6>, <&cru PCLK_I2C6>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 323 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c6m0_xfer>;
		resets = <&cru SRST_I2C6>, <&cru SRST_P_I2C6>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	i2c7: i2c@fec90000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfec90000 0x0 0x1000>;
		clocks = <&cru CLK_I2C7>, <&cru PCLK_I2C7>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 324 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c7m0_xfer>;
		resets = <&cru SRST_I2C7>, <&cru SRST_P_I2C7>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	i2c8: i2c@feca0000 {
		compatible = "rockchip,rk3588-i2c", "rockchip,rk3399-i2c";
		reg = <0x0 0xfeca0000 0x0 0x1000>;
		clocks = <&cru CLK_I2C8>, <&cru PCLK_I2C8>;
		clock-names = "i2c", "pclk";
		interrupts = <GIC_SPI 325 IRQ_TYPE_LEVEL_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2c8m0_xfer>;
		resets = <&cru SRST_I2C8>, <&cru SRST_P_I2C8>;
		reset-names = "i2c", "apb";
		#address-cells = <1>;
		#size-cells = <0>;
		status = "disabled";
	};

	spi4: spi@fecb0000 {
		compatible = "rockchip,rk3066-spi";
		reg = <0x0 0xfecb0000 0x0 0x1000>;
		interrupts = <GIC_SPI 330 IRQ_TYPE_LEVEL_HIGH>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&cru CLK_SPI4>, <&cru PCLK_SPI4>;
		clock-names = "spiclk", "apb_pclk";
		dmas = <&dmac2 13>, <&dmac2 14>;
		dma-names = "tx", "rx";
		pinctrl-names = "default";
		pinctrl-0 = <&spi4m0_cs0 &spi4m0_cs1 &spi4m0_pins>;
		num-cs = <2>;
		status = "disabled";
	};

	otp: otp@fecc0000 {
		compatible = "rockchip,rk3588-otp";
		reg = <0x0 0xfecc0000 0x0 0x400>;
		#address-cells = <1>;
		#size-cells = <1>;
		clocks = <&cru CLK_OTPC_NS>, <&cru PCLK_OTPC_NS>,
			 <&cru CLK_OTP_PHY_G>;
		clock-names = "otpc", "apb", "phy";
		resets = <&cru SRST_OTPC_NS>, <&cru SRST_P_OTPC_NS>,
			 <&cru SRST_OTPC_ARB>;
		reset-names = "otpc", "apb", "arb";

		/* Data cells */
		cpu_code: cpu-code@2 {
			reg = <0x02 0x2>;
		};
		package_serial_number_high: package-serial-number-high@5 {
			reg = <0x05 0x1>;
			bits = <0 1>;
		};
		package_serial_number_low: package-serial-number-low@6 {
			reg = <0x06 0x1>;
			bits = <5 3>;
		};
		specification_serial_number: specification-serial-number@6 {
			reg = <0x06 0x1>;
			bits = <0 5>;
		};
		otp_id: id@7 {
			reg = <0x07 0x10>;
		};
		otp_cpu_version: cpu-version@1c {
			reg = <0x1c 0x1>;
			bits = <3 3>;
		};
		cpub0_leakage: cpub0-leakage@17 {
			reg = <0x17 0x1>;
		};
		cpub1_leakage: cpub1-leakage@18 {
			reg = <0x18 0x1>;
		};
		cpul_leakage: cpul-leakage@19 {
			reg = <0x19 0x1>;
		};
		log_leakage: log-leakage@1a {
			reg = <0x1a 0x1>;
		};
		gpu_leakage: gpu-leakage@1b {
			reg = <0x1b 0x1>;
		};
		customer_demand: customer-demand@22 {
			reg = <0x22 0x1>;
			bits = <4 4>;
		};
		npu_leakage: npu-leakage@28 {
			reg = <0x28 0x1>;
		};
		codec_leakage: codec-leakage@29 {
			reg = <0x29 0x1>;
		};
		cpul_opp_info: cpul-opp-info@3d {
			reg = <0x3d 0x6>;
		};
		cpub01_opp_info: cpub01-opp-info@43 {
			reg = <0x43 0x6>;
		};
		cpub23_opp_info: cpub23-opp-info@49 {
			reg = <0x49 0x6>;
		};
		gpu_opp_info: gpu-opp-info@4f {
			reg = <0x4f 0x6>;
		};
		npu_opp_info: npu-opp-info@55 {
			reg = <0x55 0x6>;
		};
		dmc_opp_info: dmc-opp-info@5b {
			reg = <0x5b 0x6>;
		};
		vop_opp_info: vop-opp-info@61 {
			reg = <0x61 0x6>;
		};
		venc_opp_info: venc-opp-info@67 {
			reg = <0x67 0x6>;
		};
	};

	mailbox2: mailbox@fece0000 {
		compatible = "rockchip,rk3588-mailbox",
			     "rockchip,rk3368-mailbox";
		reg = <0x0 0xfece0000 0x0 0x200>;
		interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 79 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru PCLK_MAILBOX2>;
		clock-names = "pclk_mailbox";
		#mbox-cells = <1>;
		status = "disabled";
	};

	dmac2: dma-controller@fed10000 {
		compatible = "arm,pl330", "arm,primecell";
		reg = <0x0 0xfed10000 0x0 0x4000>;
		interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>,
			     <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&cru ACLK_DMAC2>;
		clock-names = "apb_pclk";
		#dma-cells = <1>;
		arm,pl330-periph-burst;
	};

	hdptxphy0: phy@fed60000 {
		compatible = "rockchip,rk3588-hdptx-phy";
		reg = <0x0 0xfed60000 0x0 0x2000>;
		clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>, <&cru PCLK_HDPTX0>;
		clock-names = "ref", "apb";
		resets = <&cru SRST_P_HDPTX0>, <&cru SRST_HDPTX0_INIT>,
			 <&cru SRST_HDPTX0_CMN>, <&cru SRST_HDPTX0_LANE>;
		reset-names = "apb", "init", "cmn", "lane";
		rockchip,grf = <&hdptxphy0_grf>;
		#phy-cells = <0>;
		status = "disabled";
	};

	hdptxphy_hdmi0: hdmiphy@fed60000 {
		compatible = "rockchip,rk3588-hdptx-phy-hdmi";
		reg = <0x0 0xfed60000 0x0 0x2000>;
		clocks = <&cru CLK_USB2PHY_HDPTXRXPHY_REF>, <&cru PCLK_HDPTX0>;
		clock-names = "ref", "apb";
		clock-output-names = "clk_hdmiphy_pixel0";
		#clock-cells = <0>;
		resets = <&cru SRST_HDPTX0>, <&cru SRST_P_HDPTX0>,
			 <&cru SRST_HDPTX0_INIT>, <&cru SRST_HDPTX0_CMN>,
			 <&cru SRST_HDPTX0_LANE>, <&cru SRST_HDPTX0_ROPLL>,
			 <&cru SRST_HDPTX0_LCPLL>;
		reset-names = "phy", "apb", "init", "cmn", "lane", "ropll",
			      "lcpll";
		rockchip,grf = <&hdptxphy0_grf>;
		#phy-cells = <0>;
		status = "disabled";
	};

	usbdp_phy0: phy@fed80000 {
		compatible = "rockchip,rk3588-usbdp-phy";
		reg = <0x0 0xfed80000 0x0 0x10000>;
		rockchip,u2phy-grf = <&usb2phy0_grf>;
		rockchip,usb-grf = <&usb_grf>;
		rockchip,usbdpphy-grf = <&usbdpphy0_grf>;
		rockchip,vo-grf = <&vo0_grf>;
		clocks = <&cru CLK_USBDPPHY_MIPIDCPPHY_REF>,
			 <&cru CLK_USBDP_PHY0_IMMORTAL>,
			 <&cru PCLK_USBDPPHY0>,
			 <&u2phy0>;
		clock-names = "refclk", "immortal", "pclk", "utmi";
		resets = <&cru SRST_USBDP_COMBO_PHY0_INIT>,
			 <&cru SRST_USBDP_COMBO_PHY0_CMN>,
			 <&cru SRST_USBDP_COMBO_PHY0_LANE>,
			 <&cru SRST_USBDP_COMBO_PHY0_PCS>,
			 <&cru SRST_P_USBDPPHY0>;
		reset-names = "init", "cmn", "lane", "pcs_apb", "pma_apb";
		status = "disabled";

		usbdp_phy0_dp: dp-port {
			#phy-cells = <0>;
			status = "disabled";
		};

		usbdp_phy0_u3: u3-port {
			#phy-cells = <0>;
			status = "disabled";
		};
	};

	mipidcphy0: phy@feda0000 {
		compatible = "rockchip,rk3588-mipi-dcphy";
		reg = <0x0 0xfeda0000 0x0 0x10000>;
		rockchip,grf = <&mipidcphy0_grf>;
		clocks = <&cru PCLK_MIPI_DCPHY0>,
			 <&cru CLK_USBDPPHY_MIPIDCPPHY_REF>;
		clock-names = "pclk", "ref";
		resets = <&cru SRST_M_MIPI_DCPHY0>,
			 <&cru SRST_P_MIPI_DCPHY0>,
			 <&cru SRST_P_MIPI_DCPHY0_GRF>,
			 <&cru SRST_S_MIPI_DCPHY0>;
		reset-names = "m_phy", "apb", "grf", "s_phy";
		#phy-cells = <0>;
		status = "okay";
	};

	mipidcphy1: phy@fedb0000 {
		compatible = "rockchip,rk3588-mipi-dcphy";
		reg = <0x0 0xfedb0000 0x0 0x10000>;
		rockchip,grf = <&mipidcphy1_grf>;
		clocks = <&cru PCLK_MIPI_DCPHY1>,
			 <&cru CLK_USBDPPHY_MIPIDCPPHY_REF>;
		clock-names = "pclk", "ref";
		resets = <&cru SRST_M_MIPI_DCPHY1>,
			 <&cru SRST_P_MIPI_DCPHY1>,
			 <&cru SRST_P_MIPI_DCPHY1_GRF>,
			 <&cru SRST_S_MIPI_DCPHY1>;
		reset-names = "m_phy", "apb", "grf", "s_phy";
		#phy-cells = <0>;
		status = "okay";
	};

	csi2_dphy0_hw: csi2-dphy0-hw@fedc0000 {
		compatible = "rockchip,rk3588-csi2-dphy-hw";
		reg = <0x0 0xfedc0000 0x0 0x8000>;
		clocks = <&cru PCLK_CSIPHY0>;
		clock-names = "pclk";
		resets = <&cru SRST_CSIPHY0>, <&cru SRST_P_CSIPHY0>;
		reset-names = "srst_csiphy0", "srst_p_csiphy0";
		rockchip,grf = <&mipidphy0_grf>;
		rockchip,sys_grf = <&sys_grf>;
		status = "okay";
	};

	csi2_dphy1_hw: csi2-dphy1-hw@fedc8000 {
		compatible = "rockchip,rk3588-csi2-dphy-hw";
		reg = <0x0 0xfedc8000 0x0 0x8000>;
		clocks = <&cru PCLK_CSIPHY1>;
		clock-names = "pclk";
		resets = <&cru SRST_CSIPHY1>, <&cru SRST_P_CSIPHY1>;
		reset-names = "srst_csiphy1", "srst_p_csiphy1";
		rockchip,grf = <&mipidphy1_grf>;
		rockchip,sys_grf = <&sys_grf>;
		status = "okay";
	};

	combphy0_ps: phy@fee00000 {
		compatible = "rockchip,rk3588-naneng-combphy";
		reg = <0x0 0xfee00000 0x0 0x100>;
		#phy-cells = <1>;
		clocks = <&cru CLK_REF_PIPE_PHY0>, <&cru PCLK_PCIE_COMBO_PIPE_PHY0>,
			 <&cru PCLK_PHP_ROOT>;
		clock-names = "refclk", "apbclk", "phpclk";
		assigned-clocks = <&cru CLK_REF_PIPE_PHY0>;
		assigned-clock-rates = <100000000>;
		resets = <&cru SRST_P_PCIE2_PHY0>, <&cru SRST_REF_PIPE_PHY0>;
		reset-names = "combphy-apb", "combphy";
		rockchip,pipe-grf = <&php_grf>;
		rockchip,pipe-phy-grf = <&pipe_phy0_grf>;
		status = "disabled";
	};

	combphy2_psu: phy@fee20000 {
		compatible = "rockchip,rk3588-naneng-combphy";
		reg = <0x0 0xfee20000 0x0 0x100>;
		#phy-cells = <1>;
		clocks = <&cru CLK_REF_PIPE_PHY2>, <&cru PCLK_PCIE_COMBO_PIPE_PHY2>,
			 <&cru PCLK_PHP_ROOT>;
		clock-names = "refclk", "apbclk", "phpclk";
		assigned-clocks = <&cru CLK_REF_PIPE_PHY2>;
		assigned-clock-rates = <100000000>;
		resets = <&cru SRST_P_PCIE2_PHY2>, <&cru SRST_REF_PIPE_PHY2>;
		reset-names = "combphy-apb", "combphy";
		rockchip,pipe-grf = <&php_grf>;
		rockchip,pipe-phy-grf = <&pipe_phy2_grf>;
		rockchip,pcie1ln-sel-bits = <0x100 1 1 0>;
		status = "disabled";
	};

	syssram: sram@ff001000 {
		compatible = "mmio-sram";
		reg = <0x0 0xff001000 0x0 0xef000>;

		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x0 0xff001000 0xef000>;
		/* start address and size should be 4k algin */
		rkvdec0_sram: rkvdec-sram@0 {
			reg = <0x0 0x78000>;
		};
		rkvdec1_sram: rkvdec-sram@78000 {
			reg = <0x78000 0x77000>;
		};
	};

	pinctrl: pinctrl {
		compatible = "rockchip,rk3588-pinctrl";
		rockchip,grf = <&ioc>;
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		gpio0: gpio@fd8a0000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x0 0xfd8a0000 0x0 0x100>;
			interrupts = <GIC_SPI 277 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO0>, <&cru DBCLK_GPIO0>;

			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 0 32>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio1: gpio@fec20000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x0 0xfec20000 0x0 0x100>;
			interrupts = <GIC_SPI 278 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO1>, <&cru DBCLK_GPIO1>;

			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 32 32>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio2: gpio@fec30000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x0 0xfec30000 0x0 0x100>;
			interrupts = <GIC_SPI 279 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO2>, <&cru DBCLK_GPIO2>;

			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 64 32>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio3: gpio@fec40000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x0 0xfec40000 0x0 0x100>;
			interrupts = <GIC_SPI 280 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO3>, <&cru DBCLK_GPIO3>;

			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 96 32>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};

		gpio4: gpio@fec50000 {
			compatible = "rockchip,gpio-bank";
			reg = <0x0 0xfec50000 0x0 0x100>;
			interrupts = <GIC_SPI 281 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cru PCLK_GPIO4>, <&cru DBCLK_GPIO4>;

			gpio-controller;
			#gpio-cells = <2>;
			gpio-ranges = <&pinctrl 0 128 32>;
			interrupt-controller;
			#interrupt-cells = <2>;
		};
	};
};

#include "rk3588s-pinctrl.dtsi"
