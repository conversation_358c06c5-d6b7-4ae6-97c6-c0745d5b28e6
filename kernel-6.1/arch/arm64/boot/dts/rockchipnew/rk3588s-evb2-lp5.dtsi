// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 */

#include "rk3588s.dtsi"
#include "rk3588s-evb.dtsi"
#include "rk3588s-rk806-dual.dtsi"

/ {
	es7202_sound_micarray: es7202-sound-micarray {
		status = "okay";
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,name = "rockchip,sound-micarray";
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,dai-link@0 {
			format = "pdm";
			cpu {
				sound-dai = <&pdm0>;
			};
			codec {
				sound-dai = <&es7202>;
			};
		};
	};

	es8388_sound: es8388-sound {
		status = "okay";
		compatible = "rockchip,multicodecs-card";
		rockchip,card-name = "rockchip-es8388";
		hp-det-gpio = <&gpio1 RK_PD0 GPIO_ACTIVE_LOW>;
		io-channels = <&saradc 3>;
		io-channel-names = "adc-detect";
		keyup-threshold-microvolt = <1800000>;
		poll-interval = <100>;
		spk-con-gpio = <&gpio4 RK_PA5 GPIO_ACTIVE_HIGH>;
		hp-con-gpio = <&gpio4 RK_PA4 GPIO_ACTIVE_HIGH>;
		rockchip,format = "i2s";
		rockchip,mclk-fs = <256>;
		rockchip,cpu = <&i2s0_8ch>;
		rockchip,codec = <&es8388>;
		rockchip,audio-routing =
			"Headphone", "LOUT1",
			"Headphone", "ROUT1",
			"Speaker", "LOUT2",
			"Speaker", "ROUT2",
			"Headphone", "Headphone Power",
			"Headphone", "Headphone Power",
			"Speaker", "Speaker Power",
			"Speaker", "Speaker Power",
			"LINPUT1", "Main Mic",
			"LINPUT2", "Main Mic",
			"RINPUT1", "Headset Mic",
			"RINPUT2", "Headset Mic";
		pinctrl-names = "default";
		pinctrl-0 = <&hp_det>;
		play-pause-key {
			label = "playpause";
			linux,code = <KEY_PLAYPAUSE>;
			press-threshold-microvolt = <2000>;
		};
	};

	vbus5v0_typec: vbus5v0-typec {
		compatible = "regulator-fixed";
		regulator-name = "vbus5v0_typec";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio4 RK_PA0 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&typec5v_pwren>;
	};

	vcc3v3_lcd_n: vcc3v3-lcd0-n {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_lcd0_n";
		regulator-boot-on;
		enable-active-high;
		gpio = <&gpio1 RK_PA4 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc_1v8_s0>;
	};

	vcc5v0_u2host: vcc5v0-u2host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_u2host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio4 RK_PA1 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_u2host_en>;
	};

	vcc5v0_u3host: vcc5v0-u3host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_u3host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio4 RK_PA6 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_u3host_en>;
	};
};

&backlight {
	pwms = <&pwm7 0 25000 0>;
	status = "okay";
};

&combphy0_ps {
	status = "okay";
};

&combphy2_psu {
	status = "okay";
};

&dp0 {
	pinctrl-0 = <&dp0m2_pins>;
	pinctrl-names = "default";
	status = "okay";
};

&dp0_in_vp2 {
	status = "okay";
};

/*
 * mipi_dcphy0 needs to be enabled
 * when dsi0 is enabled
 */
&dsi0 {
	status = "disabled";
};

&dsi0_in_vp2 {
	status = "disabled";
};

&dsi0_in_vp3 {
	status = "disabled";
};

&dsi0_panel {
	power-supply = <&vcc3v3_lcd_n>;

	/*
	 * because in hardware, the two screens share the reset pin,
	 * so reset-gpios need only in dsi0 enable and dsi1 disabled
	 * case.
	 */
	//reset-gpios = <&gpio4 RK_PB3 GPIO_ACTIVE_LOW>;
	//pinctrl-names = "default";
	//pinctrl-0 = <&lcd_rst_gpio>;
	phy-c-option;
	dsi,lanes  = <3>;

	panel-init-sequence = [
		23 00 02 FF 20
		23 00 02 FB 01
		23 00 02 05 D9
		/* VGH=17V */
		23 00 02 07 78
		/* VGL=-14V */
		23 00 02 08 5A
		/* EN_VMODGATE2=1 */
		23 00 02 0D 63
		/* VGH=16V */
		23 00 02 0E 91
		/* VGL=-13V */
		23 00 02 0F 73
		/* GVDD=5.2V */
		23 00 02 95 EB
		23 00 02 96 EB
		/* Disable VDDI LV */
		23 00 02 30 11
		/* ISOP */
		23 00 02 6D 66
		/* EN_GMACP */
		23 00 02 75 A2
		/* V128 */
		23 00 02 77 3B
		/* R(+) */
		29 00 11 B0  00  08  00  23  00  4D  00  6D  00  89  00  A1  00  B6  00  C9
		29 00 11 B1  00  DA  01  13  01  3C  01  7E  01  AB  01  F7  02  2F  02  31
		29 00 11 B2  02  67  02  A6  02  D1  03  08  03  2E  03  5B  03  6B  03  7B
		29 00 0D B3  03  8E  03  A2  03  B7  03  E7  03  FD  03  FF
		/* G(+) */
		29 00 11 B4  00  08  00  23  00  4D  00  6D  00  89  00  A1  00  B6  00  C9
		29 00 11 B5  00  DA  01  13  01  3C  01  7E  01  AB  01  F7  02  2F  02  31
		29 00 11 B6  02  67  02  A6  02  D1  03  08  03  2E  03  5B  03  6B  03  7B
		29 00 0D B7  03  8E  03  A2  03  B7  03  E7  03  FD  03  FF
		/* B(+) */
		29 00 11 B8  00  08  00  23  00  4D  00  6D  00  89  00  A1  00  B6  00  C9
		29 00 11 B9  00  DA  01  13  01  3C  01  7E  01  AB  01  F7  02  2F  02  31
		29 00 11 BA  02  67  02  A6  02  D1  03  08  03  2E  03  5B  03  6B  03  7B
		29 00 0D BB  03  8E  03  A2  03  B7  03  E7  03  FD  03  FF
		/* CMD2_Page1 */
		23 00 02 FF 21
		23 00 02 FB 01
		/* R(-) */
		29 00 11 B0  00  00  00  1B  00  45  00  65  00  81  00  99  00  AE  00  C1
		29 00 11 B1  00  D2  01  0B  01  34  01  76  01  A3  01  EF  02  27  02  29
		29 00 11 B2  02  5F  02  9E  02  C9  03  00  03  26  03  53  03  63  03  73
		29 00 0D B3  03  86  03  9A  03  AF  03  DF  03  F5  03  F7
		/* G(-) */
		29 00 11 B4  00  00  00  1B  00  45  00  65  00  81  00  99  00  AE  00  C1
		29 00 11 B5  00  D2  01  0B  01  34  01  76  01  A3  01  EF  02  27  02  29
		29 00 11 B6  02  5F  02  9E  02  C9  03  00  03  26  03  53  03  63  03  73
		29 00 0D B7  03  86  03  9A  03  AF  03  DF  03  F5  03  F7
		/* B(-) */
		29 00 11 B8  00  00  00  1B  00  45  00  65  00  81  00  99  00  AE  00  C1
		29 00 11 B9  00  D2  01  0B  01  34  01  76  01  A3  01  EF  02  27  02  29
		29 00 11 BA  02  5F  02  9E  02  C9  03  00  03  26  03  53  03  63  03  73
		29 00 0D BB  03  86  03  9A  03  AF  03  DF  03  F5  03  F7

		29 00 02  FF 24
		29 00 02  FB 01
		/* VGL */
		29 00 02 00 00
		29 00 02 01 00
		/* VDDO */
		29 00 02 02 1C
		29 00 02 03 1C
		/* VDDE */
		29 00 02 04 1D
		29 00 02 05 1D
		/* STV0 */
		29 00 02 06 04
		29 00 02 07 04
		/* CLK8 */
		29 00 02 08 0F
		29 00 02 09 0F
		/* CLK6 */
		29 00 02 0A 0E
		29 00 02 0B 0E
		/* CLK4 */
		29 00 02 0C 0D
		29 00 02 0D 0D
		/* CLK2 */
		29 00 02 0E 0C
		29 00 02 0F 0C
		/* STV2 */
		29 00 02 10 08
		29 00 02 11 08

		29 00 02 12 00
		29 00 02 13 00
		29 00 02 14 00
		29 00 02 15 00
		/* VGL */
		29 00 02 16 00
		29 00 02 17 00
		/* VDDO */
		29 00 02 18 1C
		29 00 02 19 1C
		/* VDDE */
		29 00 02 1A 1D
		29 00 02 1B 1D
		/* STV0 */
		29 00 02 1C 04
		29 00 02 1D 04
		/* CLK7 */
		29 00 02 1E 0F
		29 00 02 1F 0F
		/* CLK5 */
		29 00 02 20 0E
		29 00 02 21 0E
		/* CLK3 */
		29 00 02 22 0D
		29 00 02 23 0D
		/* CLK1 */
		29 00 02 24 0C
		29 00 02 25 0C
		/* STV1 */
		29 00 02 26 08
		29 00 02 27 08

		29 00 02 28 00
		29 00 02 29 00
		29 00 02 2A 00
		29 00 02 2B 00
		/* STV0 */
		29 00 02 2D 20
		29 00 02 2F 0A
		29 00 02 30 44
		29 00 02 33 0C
		29 00 02 34 32

		29 00 02 37 44
		29 00 02 38 40
		29 00 02 39 00
		29 00 02 3A 50
		29 00 02 3B 50
		29 00 02 3D 42
		/* STV */
		29 00 02 3F 06
		29 00 02 43 06

		29 00 02 47 66
		29 00 02 4A 50
		29 00 02 4B 50
		29 00 02 4C 91
		/* GCK */
		29 00 02 4D 21
		29 00 02 4E 43
		29 00 02 51 12
		29 00 02 52 34
		29 00 03 55 82 02
		29 00 02 56 04
		29 00 02 58 21
		29 00 02 59 30
		29 00 02 5A 50
		29 00 02 5B 50
		29 00 03 5E 00 06
		29 00 02 5F 00
		/* EN_LFD_SOURCE=0 */
		29 00 02 65 82
		/* VDDO, VDDE */
		29 00 02 7E 20
		29 00 02 7F 3C
		29 00 02 82 04
		29 00 02 97 C0

		29 00 0D B6 05 00 05 00 00 00 00 00 05 05 00 00
		/* qclk=96/5 Mhz */
		29 00 02 91 44
		29 00 02 92 55
		29 00 02 93 1A
		29 00 02 94 5F
		/* SOG_HBP */
		29 00 02 D7 55
		29 00 02 DA 0A
		29 00 02 DE 08
		/* Normal */
		29 00 02 DB 05
		29 00 02 DC 55
		29 00 02 DD 22
		/* Line N */
		29 00 02 DF 05
		29 00 02 E0 55
		/* Line N+1 */
		29 00 02 E1 05
		29 00 02 E2 55
		/* TP0 */
		29 00 02 E3 05
		29 00 02 E4 55
		/* TP3 */
		29 00 02 E5 05
		29 00 02 E6 55
		/* Gate EQ */
		29 00 02 5C 00
		29 00 02 5D 00
		/* TP3 */
		29 00 02 8D 00
		29 00 02 8E 00
		/* No Sync @ TP */
		29 00 02 B5 90

		29 00 02 FF 25
		29 00 02 FB 01
		/* disable auto_vbp_vfp */
		29 00 02 05 00
		/* ESD_DET_ERR_SEL */
		29 00 02 19 07
		/* DP_N_GCK */
		29 00 02 1F 50
		29 00 02 20 50
		/* DP_N_1_GCK */
		29 00 02 26 50
		29 00 02 27 50
		/* TP0_GCK */
		29 00 02 33 50
		29 00 02 34 50
		/* TP3 GCK/MUX=1 */
		29 00 02 3F E0
		/* TP3_GCK_START_LINE */
		29 00 02 40 00
		/* TP3_STV */
		29 00 02 44 00
		29 00 02 45 40
		/* TP3_GCK */
		29 00 02 48 50
		29 00 02 49 50
		/* LSTP0 */
		29 00 02 5B 00
		29 00 02 5C 00
		29 00 02 5D 00
		29 00 02 5E D0

		29 00 02 61 50
		29 00 02 62 50
		/* en_vfp_addvsync */
		29 00 02 F1 10
		/* CMD2,Page10 */
		29 00 02 FF 2A
		29 00 02 FB 01
		/* PWRONOFF */
		/* STV */
		29 00 02 64 16
		/* CLR */
		29 00 02 67 16
		/* GCK */
		29 00 02 6A 16
		/* POL */
		29 00 02 70 30
		/* ABOFF */
		29 00 02 A2 F3
		29 00 02 A3 FF
		29 00 02 A4 FF
		29 00 02 A5 FF
		/* Long_V_TIMING disable */
		29 00 02 D6 08
		/* CMD2,Page6 */
		29 00 02 FF 26
		29 00 02 FB 01
		/* TPEN */
		29 00 02 00 81
		/* 90Hz */
		29 00 02 01 30

		29 00 02 02 31
		29 00 02 0A F2
		//Table A (90Hz)
		29 00 02 04 28
		29 00 02 06 3C
		29 00 02 0C 0B
		29 00 02 0D 0C
		29 00 02 0F 00
		29 00 02 11 00
		29 00 02 12 50
		29 00 02 13 AE
		29 00 02 14 A6
		29 00 02 16 10
		29 00 02 19 08
		29 00 02 1A FF
		29 00 02 1B 08
		29 00 02 1C 80
		29 00 02 22 00
		29 00 02 23 00
		29 00 02 2A 08
		29 00 02 2B FF

		29 00 02 1D 00
		29 00 02 1E 55
		29 00 02 1F 55
		29 00 02 24 00
		29 00 02 25 55
		29 00 02 2F 05
		29 00 02 30 55
		29 00 02 31 05
		29 00 02 32 6D
		29 00 02 39 00
		29 00 02 3A 55
		/* Table B (60Hz,81*1+101*19=2000, Extra=20) */
		29 00 02 8B 28
		29 00 02 8C 13
		29 00 02 8D 0A
		29 00 02 8F 0A
		29 00 02 91 00
		29 00 02 92 50
		29 00 02 93 51
		29 00 02 94 65
		29 00 02 96 10
		29 00 02 99 0A
		29 00 02 9A 7F
		29 00 02 9B 0A
		29 00 02 9C 0C
		29 00 02 9D 0A
		29 00 02 9E 7F

		29 00 02 3F 00
		29 00 02 40 75
		29 00 02 41 75
		29 00 02 42 75
		29 00 02 43 00
		29 00 02 44 75
		29 00 02 45 05
		29 00 02 46 75
		29 00 02 47 05
		29 00 02 48 8D
		29 00 02 49 00
		29 00 02 4A 75
		/* STV0 */
		29 00 02 4D 5D
		29 00 02 4E 60
		/* STV */
		29 00 02 4F 5D
		29 00 02 50 60
		/* GCK */
		29 00 02 51 70
		29 00 02 52 60
		/* DP_N_GCK */
		29 00 02 56 70
		29 00 02 58 60
		/* DP_N_1_GCK */
		29 00 02 5B 70
		29 00 02 5C 60
		/* TP0_GCK */
		29 00 02 60 70
		29 00 02 61 60
		/* TP3_GCK */
		29 00 02 64 70
		29 00 02 65 60
		/* LSTP0 */
		29 00 02 72 70
		29 00 02 73 60
		/* PRZ1 */
		29 00 02 20 01
		/* PRZ3 */
		/* Rescan=3 */
		29 00 02 33 11
		29 00 02 34 78
		29 00 02 35 16
		/* DLH */
		29 00 02 C8 04
		29 00 02 C9 80
		29 00 02 CA 4E
		29 00 02 CB 00
		29 00 02 A9 4C
		29 00 02 AA 47
		/* CMD2,Page7 */
		29 00 02 FF 27
		29 00 02 FB 01
		/* VPOR_DYNH_EN=1, VPOR_CNT_REV=1 */
		29 00 02 56 06
		/* FR0(60Hz) */
		29 00 02 58 80
		29 00 02 59 53
		29 00 02 5A 00
		29 00 02 5B 14
		29 00 02 5C 00
		29 00 02 5D 01
		29 00 02 5E 20
		29 00 02 5F 10
		29 00 02 60 00
		29 00 02 61 1D
		29 00 02 62 00
		29 00 02 63 01
		29 00 02 64 24
		29 00 02 65 1C
		29 00 02 66 00
		29 00 02 67 01
		29 00 02 68 25
		/* FR1(90Hz) */
		29 00 02 78 80
		29 00 02 79 73
		29 00 02 7A 00
		29 00 02 7B 14
		29 00 02 7C 00
		29 00 02 7D 02
		29 00 02 7E 20
		29 00 02 7F 21
		29 00 02 80 00
		29 00 02 81 2A
		29 00 02 82 00
		29 00 02 83 01
		29 00 02 84 1C
		29 00 02 85 28
		29 00 02 86 00
		29 00 02 87 01
		29 00 02 88 1D

		29 00 02 00 00
		29 00 02 C3 00
		/* FTE output TE, FTE1 output TSVD, LEDPWM output TSHD */
		29 00 02 D1 24
		29 00 02 D2 53
		/* CMD2,Page10 */
		29 00 02 FF 2A
		29 00 02 FB 01
		29 00 02 01 05
		29 00 02 02 55
		/* TP0 */
		29 00 02 03 05
		29 00 02 04 75
		/* TP3 */
		29 00 02 05 05
		29 00 02 06 75
		/* PEN_EN=1, UL_FREQ=0 */
		29 00 02 22 2F
		/* 90Hz */
		29 00 02 23 11
		/* FR0 (60Hz) */
		29 00 02 24 00
		29 00 02 25 75
		29 00 02 27 00
		29 00 02 28 1A
		29 00 02 29 00
		29 00 02 2A 1A
		29 00 02 2B 00
		29 00 02 2D 1A
		/* FR1 (90Hz) */
		29 00 02 2F 00
		29 00 02 30 55
		29 00 02 32 00
		29 00 02 33 1A
		29 00 02 34 00
		29 00 02 35 1A
		29 00 02 36 00
		29 00 02 37 1A
		/* CMD2,Page3 */
		29 00 02 FF 23
		29 00 02 FB 01
		/* DBV=12 bit */
		29 00 02 00 80
		/* PWM frequency */
		29 00 02 07 00
		/* CMD3,PageA */
		29 00 02 FF E0
		29 00 02 FB 01
		/* VCOM Driving Ability */
		29 00 02 14 60
		29 00 02 16 C0
		/* CMD3,PageB */
		29 00 02 FF F0
		29 00 02 FB 01
		/* slave osc workaround */
		29 00 02 3A 08
		/* CMD3,PageC */
		29 00 02 FF D0
		29 00 02 FB 01
		29 00 02 1C 88
		29 00 02 1D 08
		/* CMD1 */
		29 00 02 FF 10
		29 00 02 FB 01
		/* Only Write Slave */
		29 00 02 B9 01
		/* CMD2,Page0 */
		29 00 02 FF 20
		29 00 02 FB 01
		29 00 02 18 40
		/* CMD1 */
		29 00 02 FF 10
		29 00 02 FB 01
		/* Write Master & Slave */
		29 00 02 B9 02
		29 00 02 35 00
		29 00 03 51 00 FF
		29 00 02 53 24
		29 00 02 55 00
		29 00 02 BB 13
		/* VBP+VFP=121 */
		29 00 06 3B 03 5F 1A 04 04
		/* CMD2,Page5 */
		29 00 02 FF 25
		/* FRM */
		29 00 02 EC 00
		/* CMD1 */
		29 00 02 FF 10
		29 00 02 FB 01
		05 FF 01 11
		05 FF 01 29
	];

	panel-exit-sequence = [
		05 00 01 28
		05 00 01 10
	];

	disp_timings0: display-timings {
		native-mode = <&dsi0_timing0>;
		dsi0_timing0: timing0 {
			clock-frequency = <241300000>;
			hactive = <1200>;
			vactive = <2000>;
			hfront-porch = <31>;
			hsync-len = <4>;
			hback-porch = <32>;
			vfront-porch = <26>;
			vsync-len = <2>;
			vback-porch = <93>;
			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <0>;
			pixelclk-active = <0>;
		};
	};
};

/*
 * mipi_dcphy1 needs to be enabled
 * when dsi1 is enabled
 */
&dsi1 {
	status = "okay";
};

&dsi1_in_vp2 {
	status = "disabled";
};

&dsi1_in_vp3 {
	status = "okay";
};

&dsi1_panel {
	power-supply = <&vcc3v3_lcd_n>;

	reset-gpios = <&gpio4 RK_PB3 GPIO_ACTIVE_LOW>;
	pinctrl-names = "default";
	pinctrl-0 = <&lcd_rst_gpio>;
};

&hdmi0 {
	enable-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&hdmi0_in_vp0 {
	status = "okay";
};

&hdmi0_sound {
	status = "okay";
};

&hdptxphy_hdmi0 {
	status = "okay";
};

&i2c2 {
	status = "okay";

	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "hym8563";
		pinctrl-names = "default";
		pinctrl-0 = <&hym8563_int>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PC4 IRQ_TYPE_LEVEL_LOW>;
		wakeup-source;
	};
};

&i2c5 {
	status = "okay";

	ls_stk3332: light@47 {
		compatible = "ls_stk3332";
		status = "disabled";
		reg = <0x47>;
		type = <SENSOR_TYPE_LIGHT>;
		irq_enable = <0>;
		als_threshold_high = <100>;
		als_threshold_low = <10>;
		als_ctrl_gain = <2>; /* 0:x1 1:x4 2:x16 3:x64 */
		poll_delay_ms = <100>;
	};

	ps_stk3332: proximity@47 {
		compatible = "ps_stk3332";
		status = "disabled";
		reg = <0x47>;
		type = <SENSOR_TYPE_PROXIMITY>;
		//pinctrl-names = "default";
		//pinctrl-0 = <&gpio3_c6>;
		//irq-gpio = <&gpio3 RK_PC6 IRQ_TYPE_LEVEL_LOW>;
		//irq_enable = <1>;
		ps_threshold_high = <0x200>;
		ps_threshold_low = <0x100>;
		ps_ctrl_gain = <3>; /* 0:x1 1:x2 2:x5 3:x8 */
		ps_led_current = <4>; /* 0:3.125mA 1:6.25mA 2:12.5mA 3:25mA 4:50mA 5:100mA*/
		poll_delay_ms = <100>;
	};

	mpu6500_acc: mpu_acc@68 {
		compatible = "mpu6500_acc";
		reg = <0x68>;
		irq-gpio = <&gpio3 RK_PB4 IRQ_TYPE_EDGE_RISING>;
		irq_enable = <0>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_ACCEL>;
		layout = <8>;
	};

	mpu6500_gyro: mpu_gyro@68 {
		compatible = "mpu6500_gyro";
		reg = <0x68>;
		irq_enable = <0>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_GYROSCOPE>;
		layout = <8>;
	};
};

&i2c6 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c6m3_xfer>;

	gt1x: gt1x@14 {
		compatible = "goodix,gt1x";
		reg = <0x14>;
		pinctrl-names = "default";
		pinctrl-0 = <&touch_gpio>;
		goodix,rst-gpio = <&gpio4 RK_PB2 GPIO_ACTIVE_HIGH>;
		goodix,irq-gpio = <&gpio4 RK_PB4 IRQ_TYPE_LEVEL_LOW>;
		power-supply = <&vcc3v3_lcd_n>;
	};
};

&i2c3 {
	status = "okay";

	es8388: es8388@11 {
		status = "okay";
		#sound-dai-cells = <0>;
		compatible = "everest,es8388", "everest,es8323";
		reg = <0x11>;
		clocks = <&mclkout_i2s0>;
		clock-names = "mclk";
		assigned-clocks = <&mclkout_i2s0>;
		assigned-clock-rates = <12288000>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s0_mclk>;
	};

	es7202: es7202@32 {
		status = "okay";
		#sound-dai-cells = <0>;
		compatible = "ES7202_PDM_ADC_1";
		power-supply = <&vcc_1v8_s0>;	/* only 1v8 or 3v3, default is 3v3 */
		reg = <0x32>;
	};
};

&i2s5_8ch {
	status = "okay";
};

&mipi_dcphy0 {
	status = "disabled";
};

&mipi_dcphy1 {
	status = "okay";
};

&sdmmc {
	status = "okay";
	vmmc-supply = <&vcc_3v3_sd_s0>;
};

&pdm0 {
	status = "okay";
};

&pinctrl {
	headphone {
		hp_det: hp-det {
			rockchip,pins = <1 RK_PD0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hym8563 {
		hym8563_int: hym8563-int {
			rockchip,pins = <0 RK_PC4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	lcd {
		lcd_rst_gpio: lcd-rst-gpio {
			rockchip,pins = <4 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	sensor {
		mpu6500_irq_gpio: mpu6500_irq_gpio {
			rockchip,pins = <3 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	touch {
		touch_gpio: touch-gpio {
			rockchip,pins =
				<4 RK_PB2 RK_FUNC_GPIO &pcfg_pull_up>,
				<4 RK_PB4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb {
		typec5v_pwren: typec5v-pwren {
			rockchip,pins = <4 RK_PA0 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		vcc5v0_u2host_en: vcc5v0-u2host-en {
			rockchip,pins = <4 RK_PA1 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		vcc5v0_u3host_en: vcc5v0-u3host-en {
			rockchip,pins = <4 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm7 {
	status = "okay";
};

&route_dsi0 {
	status = "disabled";
	connect = <&vp3_out_dsi0>;
};

&route_dsi1 {
	status = "okay";
	connect = <&vp3_out_dsi1>;
};

&sata0 {
	status = "okay";
};

&u2phy0_otg {
	rockchip,sel-pipe-phystatus;
	vbus-supply = <&vbus5v0_typec>;
};

&u2phy2_host {
	phy-supply = <&vcc5v0_u2host>;
};

&u2phy3_host {
	phy-supply = <&vcc5v0_u3host>;
};

&usbdp_phy0 {
	rockchip,dp-lane-mux = <0 1 2 3>;
};

&usbdrd_dwc3_0 {
	dr_mode = "otg";
	phys = <&u2phy0_otg>;
	phy-names = "usb2-phy";
	maximum-speed = "high-speed";
	extcon = <&u2phy0>;
};
