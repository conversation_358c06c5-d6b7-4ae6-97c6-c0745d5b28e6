// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 */

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/rockchip.h>

&spi2 {
	status = "okay";
	assigned-clocks = <&cru CLK_SPI2>;
	assigned-clock-rates = <200000000>;
	num-cs = <2>;

	rk806master: rk806master@0 {
		compatible = "rockchip,rk806";
		spi-max-frequency = <1000000>;
		reg = <0x0>;

		interrupt-parent = <&gpio0>;
		interrupts = <7 IRQ_TYPE_LEVEL_LOW>;

		pinctrl-names = "default", "pmic-power-off";
		pinctrl-0 = <&pmic_pins>, <&rk806_dvs1_null>, <&rk806_dvs2_null>, <&rk806_dvs3_null>;
		pinctrl-1 = <&rk806_dvs1_pwrdn>;

		/* 2800mv-3500mv */
		low_voltage_threshold = <3000>;
		/* 2700mv-3400mv */
		shutdown_voltage_threshold = <2700>;
		/* 140 160 */
		shutdown_temperture_threshold = <160>;
		hotdie_temperture_threshold = <115>;

		/* 0: restart PMU;
		 * 1: reset all the power off reset registers,
		 *    forcing the state to switch to ACTIVE mode;
		 * 2: Reset all the power off reset registers,
		 *    forcing the state to switch to ACTIVE mode,
		 *    and simultaneously pull down the RESETB PIN for 5mS before releasing
		 */
		pmic-reset-func = <1>;

		/* PWRON_ON_TIME: 0:500mS; 1:20mS */
		pwron-on-time-20ms;

		vcc1-supply = <&vcc5v0_sys>;
		vcc2-supply = <&vcc5v0_sys>;
		vcc3-supply = <&vcc5v0_sys>;
		vcc4-supply = <&vcc5v0_sys>;
		vcc5-supply = <&vcc5v0_sys>;
		vcc6-supply = <&vcc5v0_sys>;
		vcc7-supply = <&vcc5v0_sys>;
		vcc8-supply = <&vcc5v0_sys>;
		vcc9-supply = <&vcc5v0_sys>;
		vcc10-supply = <&vcc5v0_sys>;
		vcc11-supply = <&vcc_2v0_pldo_s3>;
		vcc12-supply = <&vcc5v0_sys>;
		vcc13-supply = <&vcc5v0_sys>;
		vcc14-supply = <&vcc_1v1_nldo_s3>;
		vcca-supply = <&vcc5v0_sys>;

		pwrkey {
			status = "okay";
		};

		pinctrl_rk806: pinctrl_rk806 {
			gpio-controller;
			#gpio-cells = <2>;

			rk806_dvs1_null: rk806_dvs1_null {
				pins = "gpio_pwrctrl2";
				function = "pin_fun0";
			};

			rk806_dvs1_slp: rk806_dvs1_slp {
				pins = "gpio_pwrctrl1";
				function = "pin_fun1";
			};

			rk806_dvs1_pwrdn: rk806_dvs1_pwrdn {
				pins = "gpio_pwrctrl1";
				function = "pin_fun2";
			};

			rk806_dvs1_rst: rk806_dvs1_rst {
				pins = "gpio_pwrctrl1";
				function = "pin_fun3";
			};

			rk806_dvs2_null: rk806_dvs2_null {
				pins = "gpio_pwrctrl2";
				function = "pin_fun0";
			};

			rk806_dvs2_slp: rk806_dvs2_slp {
				pins = "gpio_pwrctrl2";
				function = "pin_fun1";
			};

			rk806_dvs2_pwrdn: rk806_dvs2_pwrdn {
				pins = "gpio_pwrctrl2";
				function = "pin_fun2";
			};

			rk806_dvs2_rst: rk806_dvs2_rst {
				pins = "gpio_pwrctrl2";
				function = "pin_fun3";
			};

			rk806_dvs2_dvs: rk806_dvs2_dvs {
				pins = "gpio_pwrctrl2";
				function = "pin_fun4";
			};

			rk806_dvs2_gpio: rk806_dvs2_gpio {
				pins = "gpio_pwrctrl2";
				function = "pin_fun5";
			};

			rk806_dvs3_null: rk806_dvs3_null {
				pins = "gpio_pwrctrl3";
				function = "pin_fun0";
			};

			rk806_dvs3_slp: rk806_dvs3_slp {
				pins = "gpio_pwrctrl3";
				function = "pin_fun1";
			};

			rk806_dvs3_pwrdn: rk806_dvs3_pwrdn {
				pins = "gpio_pwrctrl3";
				function = "pin_fun2";
			};

			rk806_dvs3_rst: rk806_dvs3_rst {
				pins = "gpio_pwrctrl3";
				function = "pin_fun3";
			};

			rk806_dvs3_dvs: rk806_dvs3_dvs {
				pins = "gpio_pwrctrl3";
				function = "pin_fun4";
			};

			rk806_dvs3_gpio: rk806_dvs3_gpio {
				pins = "gpio_pwrctrl3";
				function = "pin_fun5";
			};
		};

		regulators {
			vdd_gpu_s0: DCDC_REG1 {
				regulator-boot-on;
				regulator-min-microvolt = <550000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-enable-ramp-delay = <400>;
				regulator-name = "vdd_gpu_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_npu_s0: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <550000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_npu_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_log_s0: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_log_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
					regulator-suspend-microvolt = <750000>;
				};
			};

			vdd_vdenc_s0: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <550000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_vdenc_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_gpu_mem_s0: DCDC_REG5 {
				regulator-boot-on;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-enable-ramp-delay = <400>;
				regulator-name = "vdd_gpu_mem_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_npu_mem_s0: DCDC_REG6 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_npu_mem_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_2v0_pldo_s3: DCDC_REG7 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2000000>;
				regulator-max-microvolt = <2000000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_2v0_pldo_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <2000000>;
				};
			};

			vdd_vdenc_mem_s0: DCDC_REG8 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_vdenc_mem_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd2_ddr_s3: DCDC_REG9 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vdd2_ddr_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};

			vcc_1v1_nldo_s3: DCDC_REG10 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1100000>;
				regulator-max-microvolt = <1100000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_1v1_nldo_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1100000>;
				};
			};

			avcc_1v8_s0: PLDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "avcc_1v8_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd1_1v8_ddr_s3: PLDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd1_1v8_ddr_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_1v8_s3: PLDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_1v8_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_3v3_s0: PLDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_3v3_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vccio_sd_s0: PLDO_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vccio_sd_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			master_pldo6_s3: PLDO_REG6 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "master_pldo6_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vdd_0v75_s3: NLDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <750000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_0v75_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <750000>;
				};
			};

			vdd2l_0v9_ddr_s3: NLDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <900000>;
				regulator-name = "vdd2l_0v9_ddr_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <900000>;
				};
			};

			master_nldo3: NLDO_REG3 {
				regulator-name = "master_nldo3";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			avdd_0v75_s0: NLDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <750000>;
				regulator-name = "avdd_0v75_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_0v85_s0: NLDO_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <850000>;
				regulator-name = "vdd_0v85_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};
		};
	};

	rk806slave: rk806slave@1 {
		compatible = "rockchip,rk806";
		spi-max-frequency = <1000000>;
		reg = <0x01>;

		interrupt-parent = <&gpio0>;
		interrupts = <7 IRQ_TYPE_LEVEL_LOW>;

		pinctrl-names = "default", "pmic-sleep";
		pinctrl-0 = <&rk806_slave_dvs1_null>, <&rk806_slave_dvs2_null>, <&rk806_slave_dvs3_null>;
		pinctrl-1 = <&rk806_slave_dvs1_slp>, <&rk806_slave_dvs2_null>, <&rk806_slave_dvs3_null>;

		/* 0: restart PMU;
		 * 1: reset all the power off reset registers,
		 *    forcing the state to switch to ACTIVE mode;
		 * 2: Reset all the power off reset registers,
		 *    forcing the state to switch to ACTIVE mode,
		 *    and simultaneously pull down the RESETB PIN for 5mS before releasing
		 */
		pmic-reset-func = <1>;

		vcc1-supply = <&vcc5v0_sys>;
		vcc2-supply = <&vcc5v0_sys>;
		vcc3-supply = <&vcc5v0_sys>;
		vcc4-supply = <&vcc5v0_sys>;
		vcc5-supply = <&vcc5v0_sys>;
		vcc6-supply = <&vcc5v0_sys>;
		vcc7-supply = <&vcc5v0_sys>;
		vcc8-supply = <&vcc5v0_sys>;
		vcc9-supply = <&vcc5v0_sys>;
		vcc10-supply = <&vcc5v0_sys>;
		vcc11-supply = <&vcc_2v0_pldo_s3>;
		vcc12-supply = <&vcc5v0_sys>;
		vcc13-supply = <&vcc_1v1_nldo_s3>;
		vcc14-supply = <&vcc_2v0_pldo_s3>;
		vcca-supply = <&vcc5v0_sys>;

		pwrkey {
			status = "disabled";
		};

		pinctrl_slave_rk806: pinctrl_slave_rk806 {
			gpio-controller;
			#gpio-cells = <2>;

			rk806_slave_dvs1_null: rk806_slave_dvs1_null {
				pins = "gpio_pwrctrl2";
				function = "pin_fun0";
			};

			rk806_slave_dvs1_slp: rk806_slave_dvs1_slp {
				pins = "gpio_pwrctrl1";
				function = "pin_fun1";
			};

			rk806_slave_dvs1_pwrdn: rk806_slave_dvs1_pwrdn {
				pins = "gpio_pwrctrl1";
				function = "pin_fun2";
			};

			rk806_slave_dvs1_rst: rk806_slave_dvs1_rst {
				pins = "gpio_pwrctrl1";
				function = "pin_fun3";
			};

			rk806_slave_dvs2_null: rk806_slave_dvs2_null {
				pins = "gpio_pwrctrl2";
				function = "pin_fun0";
			};

			rk806_slave_dvs2_slp: rk806_slave_dvs2_slp {
				pins = "gpio_pwrctrl2";
				function = "pin_fun1";
			};

			rk806_slave_dvs2_pwrdn: rk806_slave_dvs2_pwrdn {
				pins = "gpio_pwrctrl2";
				function = "pin_fun2";
			};

			rk806_slave_dvs2_rst: rk806_slave_dvs2_rst {
				pins = "gpio_pwrctrl2";
				function = "pin_fun3";
			};

			rk806_slave_dvs2_dvs: rk806_slave_dvs2_dvs {
				pins = "gpio_pwrctrl2";
				function = "pin_fun4";
			};

			rk806_slave_dvs2_gpio: rk806_slave_dvs2_gpio {
				pins = "gpio_pwrctrl2";
				function = "pin_fun5";
			};

			rk806_slave_dvs3_null: rk806_slave_dvs3_null {
				pins = "gpio_pwrctrl3";
				function = "pin_fun0";
			};

			rk806_slave_dvs3_slp: rk806_slave_dvs3_slp {
				pins = "gpio_pwrctrl3";
				function = "pin_fun1";
			};

			rk806_slave_dvs3_pwrdn: rk806_slave_dvs3_pwrdn {
				pins = "gpio_pwrctrl3";
				function = "pin_fun2";
			};

			rk806_slave_dvs3_rst: rk806_slave_dvs3_rst {
				pins = "gpio_pwrctrl3";
				function = "pin_fun3";
			};

			rk806_slave_dvs3_dvs: rk806_slave_dvs3_dvs {
				pins = "gpio_pwrctrl3";
				function = "pin_fun4";
			};

			rk806_slave_dvs3_gpio: rk806_slave_dvs3_gpio {
				pins = "gpio_pwrctrl3";
				function = "pin_fun5";
			};
		};

		regulators {
			vdd_cpu_big1_s0: DCDC_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-init-microvolt = <800000>;
				regulator-min-microvolt = <550000>;
				regulator-max-microvolt = <1050000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_cpu_big1_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_cpu_big0_s0: DCDC_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-init-microvolt = <800000>;
				regulator-min-microvolt = <550000>;
				regulator-max-microvolt = <1050000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_cpu_big0_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_cpu_lit_s0: DCDC_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-init-microvolt = <800000>;
				regulator-min-microvolt = <550000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_cpu_lit_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_3v3_s3: DCDC_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_3v3_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vdd_cpu_big1_mem_s0: DCDC_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-init-microvolt = <800000>;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <1050000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_cpu_big1_mem_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};


			vdd_cpu_big0_mem_s0: DCDC_REG6 {
				regulator-always-on;
				regulator-boot-on;
				regulator-init-microvolt = <800000>;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <1050000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_cpu_big0_mem_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_1v8_s0: DCDC_REG7 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_1v8_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_cpu_lit_mem_s0: DCDC_REG8 {
				regulator-always-on;
				regulator-boot-on;
				regulator-init-microvolt = <800000>;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <950000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_cpu_lit_mem_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vddq_ddr_s0: DCDC_REG9 {
				regulator-always-on;
				regulator-boot-on;
				regulator-name = "vddq_ddr_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_ddr_s0: DCDC_REG10 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <675000>;
				regulator-max-microvolt = <900000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_ddr_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_1v8_cam_s0: PLDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_1v8_cam_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			avdd1v8_ddr_pll_s0: PLDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "avdd1v8_ddr_pll_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_1v8_pll_s0: PLDO_REG3 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_1v8_pll_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_3v3_sd_s0: PLDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_3v3_sd_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vcc_2v8_cam_s0: PLDO_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <2800000>;
				regulator-max-microvolt = <2800000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vcc_2v8_cam_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			pldo6_s3: PLDO_REG6 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-name = "pldo6_s3";
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vdd_0v75_pll_s0: NLDO_REG1 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <750000>;
				regulator-max-microvolt = <750000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "vdd_0v75_pll_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			vdd_ddr_pll_s0: NLDO_REG2 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <850000>;
				regulator-max-microvolt = <850000>;
				regulator-name = "vdd_ddr_pll_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			slave_nldo3: NLDO_REG3 {
				regulator-name = "slave_nldo3";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			avdd_1v2_cam_s0: NLDO_REG4 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "avdd_1v2_cam_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};

			avdd_1v2_s0: NLDO_REG5 {
				regulator-always-on;
				regulator-boot-on;
				regulator-min-microvolt = <1200000>;
				regulator-max-microvolt = <1200000>;
				regulator-ramp-delay = <12500>;
				regulator-name = "avdd_1v2_s0";
				regulator-state-mem {
					regulator-off-in-suspend;
				};
			};
		};
	};
};
