// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 *
 */

&csi2_dphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			// mipi_in_ucam0: endpoint@1 {
			// 	reg = <1>;
			// 	remote-endpoint = <&ucam_out0>;
			// 	data-lanes = <1 2>;
			// };

			mipi_in_ucam0_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out0_6752>;
				data-lanes = <1 2>;
			};

			// mipi_in_ucam0_2815: endpoint@3 {
			// 	reg = <3>;
			// 	remote-endpoint = <&ucam_out0_2815>;
			// 	data-lanes = <1 2 3 4>;
			// };
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;
			// csidphy0_out: endpoint@0 {
			// 	reg = <0>;
			// 	remote-endpoint = <&mipi_csi2_input>;
			// };

		};
	};
};

&csi2_dphy1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			// csi_dphy1_input: endpoint@1 {
			// 	reg = <1>;
			// 	remote-endpoint = <&ucam_out1>;
			// 	data-lanes = <1 2>;
			// };

			
			csi2_dphy1_input_6752: endpoint@2 {
				reg = <2>;
				remote-endpoint = <&ucam_out1_6752>;
				data-lanes = <1 2>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			// csi2_dphy1_output: endpoint@0 {
			// 	reg = <0>;
			// 	remote-endpoint = <&isp_in>;
			// 	data-lanes = <1 2>;
			// };

		};
	};
};

&i2c1 {
	status = "okay";
	pinctrl-0 = <&i2c1m2_pins>;

	    RN6752_1: RN6752@2c {
		compatible = "sony,imx415";
		reg = <0x2c>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		 power-gpios = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA7 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			ucam_out1_6752: endpoint {
				remote-endpoint = <&csi2_dphy1_input_6752>;
				data-lanes = <1 2>;
			};
		};
	};
    
	RN6752_2: RN6752@2d {
		compatible = "sony,imx415";
		reg = <0x2d>;
		clocks = <&cru CLK_MIPI0_OUT2IO>;
		clock-names = "xvclk";
		
		 power-gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio6 RK_PA6 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&cam_clk0_pins>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		port {
			ucam_out0_6752: endpoint {
				remote-endpoint = <&mipi_in_ucam0_6752>;
				data-lanes = <1 2>;
			};
		};
	};


};

&i2c3 {
	status = "disabled";
	/delete-node/ imx415@1a;
	/delete-node/ sc450ai@30;
	/delete-node/ sc850sl@30;
};
