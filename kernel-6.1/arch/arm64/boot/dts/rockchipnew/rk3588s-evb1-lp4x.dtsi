// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 */

#include "dt-bindings/usb/pd.h"
#include "rk3588s.dtsi"
#include "rk3588s-evb.dtsi"
#include "rk3588s-rk806-dual.dtsi"

/ {
	aw883xx_sound: aw883x-sound {
		status = "disabled";
		compatible = "rockchip,multicodecs-card";
		rockchip,card-name = "rockchip-aw883xx";
		rockchip,format = "i2s";
		rockchip,mclk-fs = <256>;
		rockchip,cpu = <&i2s0_8ch>;
		rockchip,codec = <&aw883xx_1 &aw883xx_2 &aw883xx_3 &aw883xx_4>;
	};

	combophy_avdd0v85: combophy-avdd0v85 {
		compatible = "regulator-fixed";
		regulator-name = "combophy_avdd0v85";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <850000>;
		vin-supply = <&vdd_0v85_s0>;
	};

	combophy_avdd1v8: combophy-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "combophy_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	es7202_sound_micarray: es7202-sound-micarray {
		status = "okay";
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,name = "rockchip,sound-micarray";
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,dai-link@0 {
			format = "pdm";
			cpu {
				sound-dai = <&pdm0>;
			};
			codec {
				sound-dai = <&es7202>;
			};
		};
	};

	es8326_sound: es8326-sound {
		status = "disabled";
		compatible = "rockchip,multicodecs-card";
		rockchip,card-name = "rockchip-es8326";
		rockchip,format = "i2s";
		rockchip,mclk-fs = <256>;
		rockchip,cpu = <&i2s1_8ch>;
		rockchip,codec = <&es8326>;
		rockchip,audio-routing =
		"Headphone", "HPOL",
		"Headphone", "HPOR",
		"Headphone", "Headphone Power",
		"Headphone", "Headphone Power",
		"MIC1", "Headset Mic",
		"MIC2", "Main Mic";
	};

	es8388_sound: es8388-sound {
		status = "okay";
		compatible = "rockchip,multicodecs-card";
		rockchip,card-name = "rockchip-es8388";
		hp-det-gpio = <&gpio1 RK_PD0 GPIO_ACTIVE_LOW>;
		io-channels = <&saradc 3>;
		io-channel-names = "adc-detect";
		keyup-threshold-microvolt = <1800000>;
		poll-interval = <100>;
		spk-con-gpio = <&gpio4 RK_PA5 GPIO_ACTIVE_HIGH>;
		hp-con-gpio = <&gpio4 RK_PA4 GPIO_ACTIVE_HIGH>;
		rockchip,format = "i2s";
		rockchip,mclk-fs = <256>;
		rockchip,cpu = <&i2s0_8ch>;
		rockchip,codec = <&es8388>;
		rockchip,audio-routing =
			"Headphone", "LOUT1",
			"Headphone", "ROUT1",
			"Speaker", "LOUT2",
			"Speaker", "ROUT2",
			"Headphone", "Headphone Power",
			"Headphone", "Headphone Power",
			"Speaker", "Speaker Power",
			"Speaker", "Speaker Power",
			"LINPUT1", "Main Mic",
			"LINPUT2", "Main Mic",
			"RINPUT1", "Headset Mic",
			"RINPUT2", "Headset Mic";
		pinctrl-names = "default";
		pinctrl-0 = <&hp_det>;
		play-pause-key {
			label = "playpause";
			linux,code = <KEY_PLAYPAUSE>;
			press-threshold-microvolt = <2000>;
		};
	};

	fan: pwm-fan {
		compatible = "pwm-fan";
		#cooling-cells = <2>;
		pwms = <&pwm11 0 50000 0>;
		cooling-levels = <0 50 100 150 200 255>;
		rockchip,temp-trips = <
			50000	1
			55000	2
			60000	3
			65000	4
			70000	5
		>;
	};

	hall_sensor: hall-mh248 {
		compatible = "hall-mh248";
		pinctrl-names = "default";
		pinctrl-0 = <&mh248_irq_gpio>;
		irq-gpio = <&gpio0 RK_PD4 IRQ_TYPE_EDGE_BOTH>;
		hall-active = <1>;
		status = "okay";
	};

	panel-edp {
		compatible = "simple-panel";
		backlight = <&backlight>;
		power-supply = <&vcc3v3_lcd_edp>;
		prepare-delay-ms = <120>;
		enable-delay-ms = <120>;
		unprepare-delay-ms = <120>;
		disable-delay-ms = <120>;
		width-mm = <120>;
		height-mm = <160>;

		panel-timing {
			clock-frequency = <200000000>;
			hactive = <1536>;
			vactive = <2048>;
			hfront-porch = <12>;
			hsync-len = <16>;
			hback-porch = <48>;
			vfront-porch = <8>;
			vsync-len = <4>;
			vback-porch = <8>;
			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <0>;
			pixelclk-active = <0>;
		};

		port {
			panel_in_edp: endpoint {
				remote-endpoint = <&edp_out_panel>;
			};
		};
	};

	vbus5v0_typec: vbus5v0-typec {
		compatible = "regulator-fixed";
		regulator-name = "vbus5v0_typec";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio1 RK_PA1 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&typec5v_pwren>;
	};

	vcc3v3_lcd_edp: vcc3v3-lcd-edp {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_lcd_edp";
		gpio = <&gpio1 RK_PA5 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
		vin-supply = <&vcc_3v3_s3>;
	};

	vcc3v3_pcie20: vcc3v3-pcie20 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_pcie20";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		enable-active-high;
		gpios = <&gpio4 RK_PB1 GPIO_ACTIVE_HIGH>;
		startup-delay-us = <5000>;
		vin-supply = <&vcc12v_dcin>;
	};

	vcc5v0_host: vcc5v0-host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio1 RK_PB1 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_host_en>;
	};

	wireless_bluetooth: wireless-bluetooth {
		compatible = "bluetooth-platdata";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		uart_rts_gpios = <&gpio3 RK_PA4 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart8m1_rtsn>, <&bt_gpio>;
		pinctrl-1 = <&uart8_gpios>;
		BT,reset_gpio    = <&gpio3 RK_PB7 GPIO_ACTIVE_HIGH>;
		BT,wake_gpio     = <&gpio3 RK_PC1 GPIO_ACTIVE_HIGH>;
		BT,wake_host_irq = <&gpio3 RK_PC0 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless_wlan: wireless-wlan {
		compatible = "wlan-platdata";
		wifi_chip_type = "ap6275p";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_host_wake_irq>, <&wifi_poweren_gpio>;
		WIFI,host_wake_irq = <&gpio0 RK_PA0 GPIO_ACTIVE_HIGH>;
		WIFI,poweren_gpio = <&gpio0 RK_PC7 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};
};

&backlight {
	pwms = <&pwm12 0 25000 0>;
	power-supply = <&vcc3v3_lcd_edp>;
	status = "okay";
};

&combphy0_ps {
	status = "okay";
};

&dp0 {
	status = "okay";
};

&dp0_in_vp1 {
	status = "okay";
};

&dp0_sound{
	status = "okay";
};

&edp0 {
	force-hpd;
	status = "okay";

	ports {
		port@1 {
			reg = <1>;

			edp_out_panel: endpoint {
				remote-endpoint = <&panel_in_edp>;
			};
		};
	};
};

&edp0_in_vp2 {
	status = "okay";
};

&hdptxphy0 {
	/* Single Vdiff Training Table for power reduction (optional) */
	training-table = /bits/ 8 <
		/* voltage swing 0, pre-emphasis 0->3 */
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		/* voltage swing 1, pre-emphasis 0->2 */
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		/* voltage swing 2, pre-emphasis 0->1 */
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		/* voltage swing 3, pre-emphasis 0 */
		0x0d 0x00 0x00 0x00 0x00 0x00
	>;
	status = "okay";
};

&i2c3 {
	status = "okay";

	es8388: es8388@11 {
		status = "okay";
		#sound-dai-cells = <0>;
		compatible = "everest,es8388", "everest,es8323";
		reg = <0x11>;
		clocks = <&mclkout_i2s0>;
		clock-names = "mclk";
		assigned-clocks = <&mclkout_i2s0>;
		assigned-clock-rates = <12288000>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s0_mclk>;
	};

	es8326: es8326@18 {
		status = "disabled";
		#sound-dai-cells = <0>;
		compatible = "everest,es8326";
		reg = <0x18>;
		clocks = <&mclkout_i2s1>;
		clock-names = "mclk";
		assigned-clocks = <&mclkout_i2s1>;
		assigned-clock-rates = <12288000>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s1m0_mclk>;
		mclk-rate = <12288000>;
		mic1-src = [22];
		mic2-src = [44];
		jack-pol = [0e];
	};

	es7202: es7202@32 {
		status = "okay";
		#sound-dai-cells = <0>;
		compatible = "ES7202_PDM_ADC_1";
		power-supply = <&vcc_1v8_s0>;	/* only 1v8 or 3v3, default is 3v3 */
		reg = <0x32>;
	};

	aw883xx_1: aw883xx@34 {
		compatible = "awinic,aw883xx_smartpa";
		reg = <0x34>;
		#sound-dai-cells = <0>;
		reset-gpio = <&gpio4 RK_PB3 GPIO_ACTIVE_HIGH>;
		pinctrl-0 = <&aw_rst1_gpio>;
		pinctrl-names = "default";
		sound-channel = <0>;
		re-min = <1000>;
		re-max= <40000>;
		status = "disabled";
	};

	aw883xx_2: aw883xx@35 {
		compatible = "awinic,aw883xx_smartpa";
		reg = <0x35>;
		#sound-dai-cells = <0>;
		reset-gpio = <&gpio1 RK_PA5 GPIO_ACTIVE_HIGH>;
		pinctrl-0 = <&aw_rst2_gpio>;
		pinctrl-names = "default";
		sound-channel = <1>;
		re-min = <1000>;
		re-max= <40000>;
		status = "disabled";
	};
};

&i2c4 {
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4m3_xfer>;
	status = "okay";

	aw883xx_3: aw883xx@34 {
		compatible = "awinic,aw883xx_smartpa";
		reg = <0x34>;
		#sound-dai-cells = <0>;
		reset-gpio = <&gpio3 RK_PC5 GPIO_ACTIVE_HIGH>;
		pinctrl-0 = <&aw_rst3_gpio>;
		pinctrl-names = "default";
		sound-channel = <2>;
		re-min = <1000>;
		re-max= <40000>;
		status = "disabled";
	};

	aw883xx_4: aw883xx@35 {
		compatible = "awinic,aw883xx_smartpa";
		reg = <0x35>;
		#sound-dai-cells = <0>;
		reset-gpio = <&gpio1 RK_PC4 GPIO_ACTIVE_HIGH>;
		pinctrl-0 = <&aw_rst4_gpio>;
		pinctrl-names = "default";
		sound-channel = <3>;
		re-min = <1000>;
		re-max= <40000>;
		status = "disabled";
	};

	gsl3673@40 {
		compatible = "GSL,GSL3673";
		reg = <0x40>;
		screen_max_x = <1536>;
		screen_max_y = <2048>;
		irq_gpio_number = <&gpio1 RK_PB5 IRQ_TYPE_LEVEL_LOW>;
		rst_gpio_number = <&gpio1 RK_PB4 GPIO_ACTIVE_HIGH>;
	};
};

&i2c5 {
	status = "okay";

	ls_stk3332: light@47 {
		compatible = "ls_stk3332";
		status = "disabled";
		reg = <0x47>;
		type = <SENSOR_TYPE_LIGHT>;
		irq_enable = <0>;
		als_threshold_high = <100>;
		als_threshold_low = <10>;
		als_ctrl_gain = <2>; /* 0:x1 1:x4 2:x16 3:x64 */
		poll_delay_ms = <100>;
	};

	ps_stk3332: proximity@47 {
		compatible = "ps_stk3332";
		status = "disabled";
		reg = <0x47>;
		type = <SENSOR_TYPE_PROXIMITY>;
		//pinctrl-names = "default";
		//pinctrl-0 = <&gpio3_c6>;
		//irq-gpio = <&gpio3 RK_PC6 IRQ_TYPE_LEVEL_LOW>;
		//irq_enable = <1>;
		ps_threshold_high = <0x200>;
		ps_threshold_low = <0x100>;
		ps_ctrl_gain = <3>; /* 0:x1 1:x2 2:x5 3:x8 */
		ps_led_current = <4>; /* 0:3.125mA 1:6.25mA 2:12.5mA 3:25mA 4:50mA 5:100mA*/
		poll_delay_ms = <100>;
	};

	mpu6500_acc: mpu_acc@68 {
		compatible = "mpu6500_acc";
		reg = <0x68>;
		irq-gpio = <&gpio3 RK_PB4 IRQ_TYPE_EDGE_RISING>;
		irq_enable = <0>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_ACCEL>;
		layout = <5>;
	};

	mpu6500_gyro: mpu_gyro@68 {
		compatible = "mpu6500_gyro";
		reg = <0x68>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_GYROSCOPE>;
		layout = <5>;
	};
};

&i2c8 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c8m2_xfer>;

	usbc0: fusb302@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PD3 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&usbc0_int>;
		vbus-supply = <&vbus5v0_typec>;
		status = "okay";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				usbc0_role_sw: endpoint@0 {
					remote-endpoint = <&dwc3_0_role_switch>;
				};
			};
		};

		usb_con: connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			data-role = "dual";
			power-role = "dual";
			try-power-role = "sink";
			op-sink-microwatt = <1000000>;
			sink-pdos =
				<PDO_FIXED(5000, 1000, PDO_FIXED_USB_COMM)>;
			source-pdos =
				<PDO_FIXED(5000, 3000, PDO_FIXED_USB_COMM)>;

			altmodes {
				#address-cells = <1>;
				#size-cells = <0>;

				altmode@0 {
					reg = <0>;
					svid = <0xff01>;
					vdo = <0xffffffff>;
				};
			};

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
					usbc0_orien_sw: endpoint {
						remote-endpoint = <&usbdp_phy0_orientation_switch>;
					};
				};

				port@1 {
					reg = <1>;
					dp_altmode_mux: endpoint {
						remote-endpoint = <&usbdp_phy0_dp_altmode_mux>;
					};
				};
			};
		};
	};

	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "hym8563";
		pinctrl-names = "default";
		pinctrl-0 = <&hym8563_int>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PC4 IRQ_TYPE_LEVEL_LOW>;
		wakeup-source;
	};
};

&pcie2x1l1 {
	reset-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
	vpcie3v3-supply = <&vcc3v3_pcie20>;
	status = "okay";
};

&pcie2x1l2 {
	reset-gpios = <&gpio4 RK_PC1 GPIO_ACTIVE_HIGH>;
	rockchip,skip-scan-in-resume;
	status = "okay";
};

&pdm0 {
	status = "okay";
};

&pinctrl {
	headphone {
		hp_det: hp-det {
			rockchip,pins = <1 RK_PD0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hym8563 {
		hym8563_int: hym8563-int {
			rockchip,pins = <0 RK_PC4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	lcd {
		lcd_rst_gpio: lcd-rst-gpio {
			rockchip,pins = <1 RK_PA4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	aw883x {
		aw_rst1_gpio: aw-rst1-gpio {
			rockchip,pins = <4 RK_PB3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
		aw_rst2_gpio: aw-rst2-gpio {
			rockchip,pins = <1 RK_PA5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
		aw_rst3_gpio: aw-rst3-gpio {
			rockchip,pins = <3 RK_PC5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
		aw_rst4_gpio: aw-rst4-gpio {
			rockchip,pins = <1 RK_PC4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sensor {
		mh248_irq_gpio: mh248_irq_gpio {
			rockchip,pins = <0 RK_PD4 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		mpu6500_irq_gpio: mpu6500_irq_gpio {
			rockchip,pins = <3 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb {
		vcc5v0_host_en: vcc5v0-host-en {
			rockchip,pins = <1 RK_PB1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb-typec {
		usbc0_int: usbc0-int {
			rockchip,pins = <0 RK_PD3 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		typec5v_pwren: typec5v-pwren {
			rockchip,pins = <1 RK_PA1 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-bluetooth {
		uart8_gpios: uart8-gpios {
			rockchip,pins = <3 RK_PA4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_gpio: bt-gpio {
			rockchip,pins =
				<3 RK_PB7 RK_FUNC_GPIO &pcfg_pull_none>,
				<3 RK_PC1 RK_FUNC_GPIO &pcfg_pull_up>,
				<3 RK_PC0 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};

	wireless-wlan {
		wifi_host_wake_irq: wifi-host-wake-irq {
			rockchip,pins = <0 RK_PA0 RK_FUNC_GPIO &pcfg_pull_down>;
		};

		wifi_poweren_gpio: wifi-poweren-gpio {
			rockchip,pins = <0 RK_PC7 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&pwm3 {
	compatible = "rockchip,remotectl-pwm";
	pinctrl-names = "default";
	pinctrl-0 = <&pwm3m3_pins>;
	remote_pwm_id = <3>;
	handle_cpu_id = <1>;
	remote_support_psci = <0>;
	status = "okay";

	ir_key1 {
		rockchip,usercode = <0x4040>;
		rockchip,key_table =
			<0xf2   KEY_REPLY>,
			<0xba   KEY_BACK>,
			<0xf4   KEY_UP>,
			<0xf1   KEY_DOWN>,
			<0xef   KEY_LEFT>,
			<0xee   KEY_RIGHT>,
			<0xbd   KEY_HOME>,
			<0xea   KEY_VOLUMEUP>,
			<0xe3   KEY_VOLUMEDOWN>,
			<0xe2   KEY_SEARCH>,
			<0xb2   KEY_POWER>,
			<0xbc   KEY_MUTE>,
			<0xec   KEY_MENU>,
			<0xbf   0x190>,
			<0xe0   0x191>,
			<0xe1   0x192>,
			<0xe9   183>,
			<0xe6   248>,
			<0xe8   185>,
			<0xe7   186>,
			<0xf0   388>,
			<0xbe   0x175>;
	};

	ir_key2 {
		rockchip,usercode = <0xff00>;
		rockchip,key_table =
			<0xf9   KEY_HOME>,
			<0xbf   KEY_BACK>,
			<0xfb   KEY_MENU>,
			<0xaa   KEY_REPLY>,
			<0xb9   KEY_UP>,
			<0xe9   KEY_DOWN>,
			<0xb8   KEY_LEFT>,
			<0xea   KEY_RIGHT>,
			<0xeb   KEY_VOLUMEDOWN>,
			<0xef   KEY_VOLUMEUP>,
			<0xf7   KEY_MUTE>,
			<0xe7   KEY_POWER>,
			<0xfc   KEY_POWER>,
			<0xa9   KEY_VOLUMEDOWN>,
			<0xa8   KEY_PLAYPAUSE>,
			<0xe0   KEY_VOLUMEDOWN>,
			<0xa5   KEY_VOLUMEDOWN>,
			<0xab   183>,
			<0xb7   388>,
			<0xe8   388>,
			<0xf8   184>,
			<0xaf   185>,
			<0xed   KEY_VOLUMEDOWN>,
			<0xee   186>,
			<0xb3   KEY_VOLUMEDOWN>,
			<0xf1   KEY_VOLUMEDOWN>,
			<0xf2   KEY_VOLUMEDOWN>,
			<0xf3   KEY_SEARCH>,
			<0xb4   KEY_VOLUMEDOWN>,
			<0xa4   KEY_SETUP>,
			<0xbe   KEY_SEARCH>;
	};

	ir_key3 {
		rockchip,usercode = <0x1dcc>;
		rockchip,key_table =
			<0xee   KEY_REPLY>,
			<0xf0   KEY_BACK>,
			<0xf8   KEY_UP>,
			<0xbb   KEY_DOWN>,
			<0xef   KEY_LEFT>,
			<0xed   KEY_RIGHT>,
			<0xfc   KEY_HOME>,
			<0xf1   KEY_VOLUMEUP>,
			<0xfd   KEY_VOLUMEDOWN>,
			<0xb7   KEY_SEARCH>,
			<0xff   KEY_POWER>,
			<0xf3   KEY_MUTE>,
			<0xbf   KEY_MENU>,
			<0xf9   0x191>,
			<0xf5   0x192>,
			<0xb3   388>,
			<0xbe   KEY_1>,
			<0xba   KEY_2>,
			<0xb2   KEY_3>,
			<0xbd   KEY_4>,
			<0xf9   KEY_5>,
			<0xb1   KEY_6>,
			<0xfc   KEY_7>,
			<0xf8   KEY_8>,
			<0xb0   KEY_9>,
			<0xb6   KEY_0>,
			<0xb5   KEY_BACKSPACE>;
	};
};

&pwm11 {
	pinctrl-0 = <&pwm11m1_pins>;
	status = "okay";
};

&pwm12 {
	pinctrl-0 = <&pwm12m1_pins>;
	status = "okay";
};

&route_edp0 {
	connect = <&vp2_out_edp0>;
	status = "okay";
};

&sdmmc {
	status = "okay";
};

&spdif_tx1 {
	status = "disabled";
	pinctrl-names = "default";
	pinctrl-0 = <&spdif1m1_tx>;
};

&spdif_tx1_dc {
	status = "okay";
};

&spdif_tx1_sound {
	status = "okay";
};

&spdif_tx2 {
	status = "okay";
};

&u2phy0_otg {
	rockchip,typec-vbus-det;
};

&u2phy2_host {
	phy-supply = <&vcc5v0_host>;
};

&u2phy3_host {
	phy-supply = <&vcc5v0_host>;
};

&uart8 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart8m1_xfer &uart8m1_ctsn>;
};

&usbdp_phy0 {
	orientation-switch;
	svid = <0xff01>;
	sbu1-dc-gpios = <&gpio1 RK_PB6 GPIO_ACTIVE_HIGH>;
	sbu2-dc-gpios = <&gpio1 RK_PB7 GPIO_ACTIVE_HIGH>;

	port {
		#address-cells = <1>;
		#size-cells = <0>;
		usbdp_phy0_orientation_switch: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&usbc0_orien_sw>;
		};

		usbdp_phy0_dp_altmode_mux: endpoint@1 {
			reg = <1>;
			remote-endpoint = <&dp_altmode_mux>;
		};
	};
};

&usbdrd_dwc3_0 {
	usb-role-switch;
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		dwc3_0_role_switch: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&usbc0_role_sw>;
		};
	};
};

&usbhost3_0 {
	status = "disabled";
};

&usbhost_dwc3_0 {
	status = "disabled";
};

/* vp0 & vp3 are not used on this board */
&vp0 {
	/delete-property/ rockchip,plane-mask;
	/delete-property/ rockchip,primary-plane;
};

&vp1 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER0 | 1 << ROCKCHIP_VOP2_ESMART0 |
				1 << ROCKCHIP_VOP2_CLUSTER1 | 1 << ROCKCHIP_VOP2_ESMART1)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART1>;
};

&vp2 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER2 | 1 << ROCKCHIP_VOP2_ESMART2 |
				1 << ROCKCHIP_VOP2_CLUSTER3 | 1 << ROCKCHIP_VOP2_ESMART3)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART2>;
};

&vp3 {
	/delete-property/ rockchip,plane-mask;
	/delete-property/ rockchip,primary-plane;
};
