// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
 *
 */

/ {
	max96712_dphy3_osc0: max96712-dphy3-oscillator@0 {
		compatible = "fixed-clock";
		#clock-cells = <1>;
		clock-frequency = <25000000>;
		clock-output-names = "max96712-dphy3-osc0";
	};
};

&csi2_dphy1_hw {
	status = "okay";
};

&csi2_dphy3 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_dphy3_in_max96712: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&max96712_dphy3_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidphy3_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi4_csi2_input>;
			};
		};
	};
};

&i2c6 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c6m3_xfer>;

	max96712_dphy3: max96712@29 {
		compatible = "maxim,max96712";
		status = "okay";
		reg = <0x29>;
		clock-names = "xvclk";
		clocks = <&max96712_dphy3_osc0 0>;
		pinctrl-names = "default";
		pinctrl-0 = <&max96712_dphy3_power>, <&max96712_dphy3_errb>, <&max96712_dphy3_lock>;
		power-domains = <&power RK3588_PD_VI>;
		rockchip,grf = <&sys_grf>;
		power-gpios = <&gpio4 RK_PA6 GPIO_ACTIVE_HIGH>;
		pocen-gpios = <&gpio3 RK_PB1 GPIO_ACTIVE_HIGH>;
		lock-gpios = <&gpio3 RK_PB4 GPIO_ACTIVE_HIGH>;
		auto-init-deskew-mask = <0x03>;
		frame-sync-period = <0>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "max96712";
		rockchip,camera-module-lens-name = "max96712";

		port {
			max96712_dphy3_out: endpoint {
				remote-endpoint = <&mipi_dphy3_in_max96712>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&mipi4_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi4_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidphy3_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi4_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi4_in>;
			};
		};
	};
};

&rkcif_mipi_lvds4 {
	status = "okay";
	/* parameters for do cif reset detecting:
	 * index0: monitor mode,
		   0 for idle,
		   1 for continue,
		   2 for trigger,
		   3 for hotplug (for nextchip)
	 * index1: the frame id to start timer,
		   min is 2
	 * index2: frame num of monitoring cycle
	 * index3: err time for keep monitoring
		   after finding out err (ms)
	 * index4: csi2 err reference val for resetting
	 */
	rockchip,cif-monitor = <3 2 1 1000 5>;

	port {
		cif_mipi4_in: endpoint {
			remote-endpoint = <&mipi4_csi2_output>;
		};
	};
};

&rkcif {
	status = "okay";
	rockchip,android-usb-camerahal-enable;
};

&rkcif_mmu {
	status = "okay";
};

&pinctrl {
	max96712-dphy3 {
		max96712_dphy3_power: max96712-dphy3-power {
			rockchip,pins = <4 RK_PA6 RK_FUNC_GPIO &pcfg_output_low>;
		};

		max96712_dphy3_errb: max96712-dphy3-errb {
			rockchip,pins = <0 RK_PC2 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};

		max96712_dphy3_lock: max96712-dphy3-lock {
			rockchip,pins = <3 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none_smt>;
		};
	};
};
