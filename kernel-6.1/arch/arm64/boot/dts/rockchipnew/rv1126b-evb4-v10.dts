// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 */

/dts-v1/;
#include "rv1126b.dtsi"
#include "rv1126b-evb.dtsi"
#include "rv1126b-evb-cam-csi0.dtsi"

/ {
	model = "Rockchip RV1126B EVB4 V10 Board";
	compatible = "rockchip,rv1126b-evb4-v10", "rockchip,rv1126b";

	sdio_pwrseq: sdio-pwrseq {
		compatible = "mmc-pwrseq-simple";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_enable_h>;

		/*
		 * On the module itself this is one of these (depending
		 * on the actual card populated):
		 * - SDIO_RESET_L_WL_REG_ON
		 * - PDN (power down when low)
		 */
		reset-gpios = <&gpio0 RK_PB2 GPIO_ACTIVE_LOW>;
	};

	vcc_mipi: vcc-mipi {
		compatible = "regulator-fixed";
		regulator-name = "vcc_mipi";
		gpio = <&gpio0 RK_PC6 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-boot-on;
	};

	vcc5v0_sys: vcc5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		vin-supply = <&vcc12v_dcin>;
		status = "okay";
	};

	vbus5v0_typec: vbus5v0-typec {
		compatible = "regulator-fixed";
		regulator-name = "vbus5v0_typec";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio5 RK_PD6 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_sys>;
		pinctrl-names = "default";
		pinctrl-0 = <&typec5v_pwren>;
	};

	vcc5v0_host: vcc5v0-host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio6 RK_PA3 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_sys>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_host_en>;
	};

	vdd_npu: vdd-npu {
		compatible = "pwm-regulator";
		pwms = <&pwm0_8ch_0 0 25000 1>;
		regulator-name = "vdd_npu";
		regulator-init-microvolt = <950000>;
		regulator-min-microvolt = <750000>;
		regulator-max-microvolt = <1100000>;
		regulator-always-on;
		regulator-boot-on;
		enable-gpios = <&gpio0 RK_PA3 GPIO_ACTIVE_HIGH>;
		pwm-supply = <&vcc5v0_sys>;
	};

	wireless-bluetooth {
		compatible = "bluetooth-platdata";
		uart_rts_gpios = <&gpio3 RK_PA6 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart2m0_rtsn_pins>;
		pinctrl-1 = <&uart2_gpios>;
		BT,power_gpio = <&gpio0 RK_PA0 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless-wlan {
		compatible = "wlan-platdata";
		rockchip,grf = <&grf>;
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_wake_host>;
		wifi_chip_type = "rk96x";
		WIFI,host_wake_irq = <&gpio0 RK_PB1 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};
};

&backlight {
	pwms = <&pwm0_8ch_3 0 25000 0>;
};

&cpu0 {
	cpu-supply = <&vdd_cpu>;
};

&display_subsystem {
	status = "okay";
};

&dsi {
	status = "okay";
};

&dsi_in_vop {
	status = "okay";
};

&dsi_panel {
	power-supply = <&vcc_mipi>;
};

&emmc {
	bus-width = <8>;
	cap-mmc-highspeed;
	non-removable;
	mmc-hs200-1_8v;
	rockchip,default-sample-phase = <90>;
	no-sdio;
	no-sd;
	status = "okay";
};

&i2c0 {
	status = "okay";

	rk801: rk801@27 {
		compatible = "rockchip,rk801";
		status = "okay";
		reg = <0x27>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PC0 IRQ_TYPE_LEVEL_LOW>;
		pwrctrl-gpios = <&gpio0 RK_PC1 GPIO_ACTIVE_HIGH>;

		pinctrl-names = "default";
		pinctrl-0 = <&pmic_int>;
		rockchip,system-power-controller;
		wakeup-source;

		vcc1-supply = <&vcc12v_dcin>;
		vcc2-supply = <&vcc12v_dcin>;
		vcc3-supply = <&vcc5v0_sys>;
		vcc4-supply = <&vcc5v0_sys>;
		vcc5-supply = <&vcc3v3_sys>;
		vcc6-supply = <&vcc3v3_sys>;
		vcc7-supply = <&vcc3v3_sys>;

		regulators {
			vdd_cpu: DCDC_REG1 {
				regulator-name = "vdd_cpu";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <1500000>;
				regulator-initial-mode = <0x1>;
				regulator-boot-on;
				regulator-always-on;
				regulator-state-mem {
					regulator-mode = <0x2>;
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <950000>;
				};
			};

			vcc3v3_sys: DCDC_REG2 {
				regulator-name = "vcc3v3_sys";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
				regulator-initial-mode = <0x1>;
				regulator-boot-on;
				regulator-always-on;
				regulator-state-mem {
					regulator-mode = <0x2>;
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <3300000>;
				};
			};

			vcc_ddr: DCDC_REG3 {
				regulator-name = "vcc_ddr";
				regulator-boot-on;
				regulator-always-on;
				regulator-state-mem {
					regulator-mode = <0x2>;
					regulator-on-in-suspend;
				};
			};

			vdd_logic: DCDC_REG4 {
				regulator-name = "vdd_logic";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <1500000>;
				regulator-initial-mode = <0x1>;
				regulator-boot-on;
				regulator-always-on;
				regulator-state-mem {
					regulator-mode = <0x2>;
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <5000000>;
				};
			};

			vdd0v9_sys: LDO_REG1 {
				regulator-name = "vdd0v9_sys";
				regulator-min-microvolt = <900000>;
				regulator-max-microvolt = <900000>;
				regulator-boot-on;
				regulator-always-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <900000>;
				};
			};

			vcc_1v8: LDO_REG2 {
				regulator-name = "vcc_1v8";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <1800000>;
				regulator-boot-on;
				regulator-always-on;
				regulator-state-mem {
					regulator-on-in-suspend;
					regulator-suspend-microvolt = <1800000>;
				};
			};

			vcc_3v3: SWITCH_REG1 {
				regulator-name = "vcc_3v3";
				regulator-boot-on;
				regulator-always-on;
				regulator-state-mem {
					regulator-on-in-suspend;
				};
			};
		};
	};
};

&i2c2 {
	status = "okay";

	gt1x: gt1x@14 {
		compatible = "goodix,gt1x";
		reg = <0x14>;
		pinctrl-names = "default";
		power-supply = <&vcc_mipi>;
		goodix,rst-gpio = <&gpio0 RK_PC5 GPIO_ACTIVE_HIGH>;
		goodix,irq-gpio = <&gpio0 RK_PA7 IRQ_TYPE_LEVEL_LOW>;
	};
};

&mipi_dphy {
	status = "okay";
};

&pinctrl {
	pmic {
		pmic_int: pmic-int {
			rockchip,pins = <0 RK_PC0 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sdio-pwrseq {
		wifi_enable_h: wifi-enable-h {
			rockchip,pins = <0 RK_PB2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb {
		typec5v_pwren: typec5v-pwren {
			rockchip,pins = <5 RK_PD6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		vcc5v0_host_en: vcc5v0-host-en {
			rockchip,pins = <6 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-bluetooth {
		uart2_gpios: uart2-gpios {
			rockchip,pins = <3 RK_PA6 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	wireless-wlan {
		wifi_wake_host: wifi-wake-host {
			rockchip,pins = <0 RK_PB1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&pwm0_8ch_0 {
	status = "okay";
};

&pwm0_8ch_3 {
	status = "okay";
};

&rknpu {
	rknpu-supply = <&vdd_npu>;
};

&route_dsi {
	status = "okay";
};

&sdmmc1 {
	bus-width = <4>;
	cap-sd-highspeed;
	no-sd;
	no-mmc;
	max-frequency = <200000000>;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc1_clk_pins &sdmmc1_cmd_pins &sdmmc1_bus4_pins>;
	keep-power-in-suspend;
	non-removable;
	mmc-pwrseq = <&sdio_pwrseq>;
	//sd-uhs-sdr104;
	status = "okay";
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart2m0_xfer_pins &uart2m0_ctsn_pins>;
};

&usb2phy_host {
	phy-supply = <&vcc5v0_host>;
};

&usb2phy_otg {
	vbus-supply = <&vbus5v0_typec>;
};

&usb_drd_dwc3 {
	dr_mode = "otg";
	extcon = <&usb2phy>;
};
