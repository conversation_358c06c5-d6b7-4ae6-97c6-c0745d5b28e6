// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 */

#include "dt-bindings/usb/pd.h"
#include "rk3588s.dtsi"
#include "rk3588s-evb.dtsi"
#include "rk3588s-rk806-dual.dtsi"

/ {
	combophy_avdd0v85: combophy-avdd0v85 {
		compatible = "regulator-fixed";
		regulator-name = "combophy_avdd0v85";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <850000>;
		regulator-max-microvolt = <850000>;
		vin-supply = <&vdd_0v85_s0>;
	};

	combophy_avdd1v8: combophy-avdd1v8 {
		compatible = "regulator-fixed";
		regulator-name = "combophy_avdd1v8";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		vin-supply = <&avcc_1v8_s0>;
	};

	fan: pwm-fan {
		compatible = "pwm-fan";
		#cooling-cells = <2>;
		pwms = <&pwm7 0 50000 0>;
		cooling-levels = <0 50 100 150 200 255>;
		rockchip,temp-trips = <
			50000	1
			55000	2
			60000	3
			65000	4
			70000	5
		>;
	};

	vcc3v3_lcd_n: vcc3v3-lcd0-n {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_lcd0_n";
		regulator-boot-on;
		enable-active-high;
		gpio = <&gpio1 RK_PA6 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc_1v8_s0>;
	};

	vcc3v3_pcie20: vcc3v3-pcie20 {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_pcie20";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		enable-active-high;
		gpios = <&gpio1 RK_PB2 GPIO_ACTIVE_HIGH>;
		startup-delay-us = <5000>;
		vin-supply = <&vcc12v_dcin>;
	};

	vbus5v0_typec: vbus5v0-typec {
		compatible = "regulator-fixed";
		regulator-name = "vbus5v0_typec";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio1 RK_PB3 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&typec5v_pwren>;
	};
};

&combphy0_ps {
	status = "okay";
};

&dp0 {
	status = "okay";
};

&dp0_in_vp2 {
	status = "okay";
};

/*
 * mipi_dcphy0 needs to be enabled
 * when dsi0 is enabled
 */
&dsi0 {
	status = "disabled";
};

&dsi0_in_vp2 {
	status = "disabled";
};

&dsi0_in_vp3 {
	status = "okay";
};

/*
 * mipi_dcphy1 needs to be enabled
 * when dsi1 is enabled
 */
&dsi1 {
	status = "disabled";
};

&dsi1_in_vp2 {
	status = "disabled";
};

&dsi1_in_vp3 {
	status = "disabled";
};

&i2c2 {
	status = "okay";

	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "hym8563";
		pinctrl-names = "default";
		pinctrl-0 = <&hym8563_int>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PD4 IRQ_TYPE_LEVEL_LOW>;
		wakeup-source;
	};
};

&i2c4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4m3_xfer>;

	gt1x: gt1x@14 {
		compatible = "goodix,gt1x";
		reg = <0x14>;
		pinctrl-names = "default";
		pinctrl-0 = <&touch_gpio>;
		goodix,rst-gpio = <&gpio1 RK_PB4 GPIO_ACTIVE_HIGH>;
		goodix,irq-gpio = <&gpio1 RK_PB5 IRQ_TYPE_LEVEL_LOW>;
		power-supply = <&vcc3v3_lcd_n>;
	};
};

&i2c8 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c8m2_xfer>;

	usbc0: fusb302@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PD3 IRQ_TYPE_LEVEL_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&usbc0_int>;
		vbus-supply = <&vbus5v0_typec>;
		status = "okay";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				usbc0_role_sw: endpoint@0 {
					remote-endpoint = <&dwc3_0_role_switch>;
				};
			};
		};

		usb_con: connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			data-role = "dual";
			power-role = "dual";
			try-power-role = "sink";
			op-sink-microwatt = <1000000>;
			sink-pdos =
				<PDO_FIXED(5000, 1000, PDO_FIXED_USB_COMM)>;
			source-pdos =
				<PDO_FIXED(5000, 3000, PDO_FIXED_USB_COMM)>;

			altmodes {
				#address-cells = <1>;
				#size-cells = <0>;

				altmode@0 {
					reg = <0>;
					svid = <0xff01>;
					vdo = <0xffffffff>;
				};
			};

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
					usbc0_orien_sw: endpoint {
						remote-endpoint = <&usbdp_phy0_orientation_switch>;
					};
				};

				port@1 {
					reg = <1>;
					dp_altmode_mux: endpoint {
						remote-endpoint = <&usbdp_phy0_dp_altmode_mux>;
					};
				};
			};
		};
	};
};

&mipi_dcphy0 {
	status = "disabled";
};

&mipi_dcphy1 {
	status = "disabled";
};

&pcie2x1l2 {
	reset-gpios = <&gpio3 RK_PD1 GPIO_ACTIVE_HIGH>;
	vpcie3v3-supply = <&vcc3v3_pcie20>;
	status = "okay";
};

&pinctrl {
	hym8563 {
		hym8563_int: hym8563-int {
			rockchip,pins = <0 RK_PC4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	touch {
		touch_gpio: touch-gpio {
			rockchip,pins =
				<1 RK_PB4 RK_FUNC_GPIO &pcfg_pull_up>,
				<1 RK_PB5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb-typec {
		usbc0_int: usbc0-int {
			rockchip,pins = <0 RK_PD3 RK_FUNC_GPIO &pcfg_pull_up>;
		};

		typec5v_pwren: typec5v-pwren {
			rockchip,pins = <1 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};

&pwm11 {
	status = "okay";
};

&route_dsi0 {
	status = "okay";
	connect = <&vp3_out_dsi0>;
};

&route_dsi1 {
	status = "disabled";
	connect = <&vp3_out_dsi1>;
};

&sdmmc {
	status = "okay";
	vmmc-supply = <&vcc_3v3_sd_s0>;
};

&sata2 {
	status = "okay";
};

&u2phy0_otg {
	rockchip,typec-vbus-det;
};

&u2phy2 {
	status = "disabled";
};

&u2phy3 {
	status = "disabled";
};

&u2phy2_host {
	status = "disabled";
};

&u2phy3_host {
	status = "disabled";
};

&usb_host0_ehci {
	status = "disabled";
};

&usb_host0_ohci {
	status = "disabled";
};

&usb_host1_ehci {
	status = "disabled";
};

&usb_host1_ohci {
	status = "disabled";
};

&usbdp_phy0 {
	orientation-switch;
	svid = <0xff01>;
	sbu1-dc-gpios = <&gpio1 RK_PB6 GPIO_ACTIVE_HIGH>;
	sbu2-dc-gpios = <&gpio1 RK_PB7 GPIO_ACTIVE_HIGH>;

	port {
		#address-cells = <1>;
		#size-cells = <0>;
		usbdp_phy0_orientation_switch: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&usbc0_orien_sw>;
		};

		usbdp_phy0_dp_altmode_mux: endpoint@1 {
			reg = <1>;
			remote-endpoint = <&dp_altmode_mux>;
		};
	};
};

&usbdrd_dwc3_0 {
	usb-role-switch;
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		dwc3_0_role_switch: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&usbc0_role_sw>;
		};
	};
};

&usbhost3_0 {
	status = "disabled";
};

&usbhost_dwc3_0 {
	status = "disabled";
};
