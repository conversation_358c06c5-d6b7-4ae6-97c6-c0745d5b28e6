// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2022 Rockchip Electronics Co., Ltd.
 *
 */
/ {
	vcc_mipidcphy0: vcc-mipidcphy0-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio1 RK_PB3 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&mipidcphy0_pwr>;
		regulator-name = "vcc_mipidcphy0";
		enable-active-high;
		regulator-boot-on;
		regulator-always-on;
	};
};

&csi2_dcphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_in_ucam0: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&s5k3l6_out0>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidcphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

&i2c6 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c6m4_xfer>;

	fp5510: fp5510@c {
		compatible = "fitipower,fp5510";
		status = "okay";
		reg = <0x0c>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
	};

	s5k3l6: s5k3l6@10 {
		compatible = "samsung,s5k3l6xx";
		reg = <0x10>;
		clocks = <&cru CLK_MIPI_CAMARAOUT_M1>;
		clock-names = "xvclk";
		power-domains = <&power RK3588_PD_VI>;
		pinctrl-names = "default";
		pinctrl-0 = <&mipim1_camera1_clk>;
		// power-gpios = <&gpio1 RK_PB3 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&gpio4 RK_PA6 GPIO_ACTIVE_HIGH>;
		//pwdn-gpios = <&gpio1 RK_PA0 GPIO_ACTIVE_HIGH>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "default";
		rockchip,camera-module-lens-name = "default";
		lens-focus = <&fp5510>;
		port {
			s5k3l6_out0: endpoint {
				remote-endpoint = <&mipi_in_ucam0>;
				data-lanes = <1 2 3 4>;
			};
		};
	};
};

&mipi_dcphy0 {
	status = "okay";
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidcphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in0>;
			};
		};
	};
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in0: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";

	port {
		mipi_lvds_sditf: endpoint {
			remote-endpoint = <&isp0_vir0>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp0 {
	status = "okay";
};

&isp0_mmu {
	status = "okay";
};

&rkisp0_vir0 {
	status = "okay";

	port {
		#address-cells = <1>;
		#size-cells = <0>;

		isp0_vir0: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf>;
		};
	};
};

&pinctrl {
	cam {
		mipidcphy0_pwr: mipidcphy0-pwr {
			rockchip,pins =
				/* camera power en */
				<1 RK_PB3 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};
};
