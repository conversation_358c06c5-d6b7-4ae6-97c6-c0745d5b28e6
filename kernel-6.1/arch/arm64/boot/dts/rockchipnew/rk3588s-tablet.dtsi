// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2021 Rockchip Electronics Co., Ltd.
 *
 */

#include <dt-bindings/usb/pd.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pwm/pwm.h>
#include <dt-bindings/pinctrl/rockchip.h>
#include <dt-bindings/input/rk-input.h>
#include <dt-bindings/display/drm_mipi_dsi.h>
#include <dt-bindings/display/rockchip_vop.h>
#include <dt-bindings/sensor-dev.h>
#include "rk3588s.dtsi"
#include "rk3588-android.dtsi"
#include "rk3588s-rk806-dual.dtsi"

/ {
	adc_keys: adc-keys {
		compatible = "adc-keys";
		io-channels = <&saradc 1>;
		io-channel-names = "buttons";
		keyup-threshold-microvolt = <1800000>;
		poll-interval = <100>;

		vol-up-key {
			label = "volume up";
			linux,code = <KEY_VOLUMEUP>;
			press-threshold-microvolt = <17000>;
		};

		vol-down-key {
			label = "volume down";
			linux,code = <KEY_VOLUMEDOWN>;
			press-threshold-microvolt = <417000>;
		};
	};

	backlight: backlight {
		compatible = "pwm-backlight";
		pwms = <&pwm12 0 25000 0>;
		brightness-levels = <
			  0  20  20  21  21  22  22  23
			 23  24  24  25  25  26  26  27
			 27  28  28  29  29  30  30  31
			 31  32  32  33  33  34  34  35
			 35  36  36  37  37  38  38  39
			 40  41  42  43  44  45  46  47
			 48  49  50  51  52  53  54  55
			 56  57  58  59  60  61  62  63
			 64  65  66  67  68  69  70  71
			 72  73  74  75  76  77  78  79
			 80  81  82  83  84  85  86  87
			 88  89  90  91  92  93  94  95
			 96  97  98  99 100 101 102 103
			104 105 106 107 108 109 110 111
			112 113 114 115 116 117 118 119
			120 121 122 123 124 125 126 127
			128 129 130 131 132 133 134 135
			136 137 138 139 140 141 142 143
			144 145 146 147 148 149 150 151
			152 153 154 155 156 157 158 159
			160 161 162 163 164 165 166 167
			168 169 170 171 172 173 174 175
			176 177 178 179 180 181 182 183
			184 185 186 187 188 189 190 191
			192 193 194 195 196 197 198 199
			200 201 202 203 204 205 206 207
			208 209 210 211 212 213 214 215
			216 217 218 219 220 221 222 223
			224 225 226 227 228 229 230 231
			232 233 234 235 236 237 238 239
			240 241 242 243 244 245 246 247
			248 249 250 251 252 253 254 255
		>;
		default-brightness-level = <200>;
	};

	battery: battery {
		compatible = "simple-battery";
		charge-full-design-microamp-hours = <4500000>;
	};

	bt_sco: bt-sco {
		status = "disabled";
		compatible = "delta,dfbmcs320";
		#sound-dai-cells = <1>;
	};

	bt_sound: bt-sound {
		status = "disabled";
		compatible = "simple-audio-card";
		simple-audio-card,format = "dsp_a";
		simple-audio-card,bitclock-inversion;
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,name = "rockchip,bt";
		simple-audio-card,cpu {
			sound-dai = <&i2s2_2ch>;
		};
		simple-audio-card,codec {
			sound-dai = <&bt_sco 1>;
		};
	};

	charge-animation {
		compatible = "rockchip,uboot-charge";
		rockchip,uboot-charge-on = <1>;
		rockchip,android-charge-on = <0>;
		rockchip,uboot-low-power-voltage = <6800>;
		rockchip,screen-on-voltage = <6900>;
		rockchip,uboot-exit-charge-level = <2>;
		rockchip,uboot-exit-charge-auto = <0>;
		rockchip,system-suspend = <1>;
		regulator-on-in-mem = <&vdd_log_s0>, <&vcc_2v0_pldo_s3>,
		      <&vdd2_ddr_s3>, <&vcc_1v1_nldo_s3>,
		      <&vdd1_1v8_ddr_s3>, <&vcc_1v8_s3>,
		      <&master_pldo6_s3>, <&vdd_0v75_s3>,
		      <&vdd2l_0v9_ddr_s3>, <&vdd_1v8_pll_s0>, <&pldo6_s3>;

		regulator-off-in-mem = <&vdd_gpu_s0>, <&vdd_npu_s0>,
		       <&vdd_vdenc_s0>, <&vdd_gpu_mem_s0>, <&vdd_npu_mem_s0>,
		       <&vdd_vdenc_mem_s0>, <&avcc_1v8_s0>, <&vcc_3v3_s0>,
		       <&vccio_sd_s0>, <&master_nldo3>, <&avdd_0v75_s0>,
		       <&vdd_0v85_s0>, <&vdd_cpu_big1_s0>, <&vdd_cpu_big0_s0>,
		       <&vdd_cpu_lit_s0>, <&vdd_cpu_big1_mem_s0>, <&vdd_cpu_big0_mem_s0>,
		       <&vcc_1v8_s0>, <&vdd_cpu_lit_mem_s0>, <&vddq_ddr_s0>,
		       <&vdd_ddr_s0>, <&vcc_1v8_cam_s0>, <&avdd1v8_ddr_pll_s0>,
		       <&vcc_3v3_sd_s0>, <&vcc_2v8_cam_s0>, <&vdd_0v75_pll_s0>,
		       <&vdd_ddr_pll_s0>, <&slave_nldo3>, <&avdd_1v2_cam_s0>,
		       <&avdd_1v2_s0>, <&vcc_3v3_s3>;
		status = "okay";
	};

	dp0_sound: dp0-sound {
		status = "okay";
		compatible = "rockchip,hdmi";
		rockchip,card-name= "rockchip-dp0";
		rockchip,mclk-fs = <512>;
		rockchip,cpu = <&spdif_tx2>;
		rockchip,codec = <&dp0 1>;
		rockchip,jack-det;
	};

	es7202_sound_micarray: es7202-sound-micarray {
		status = "okay";
		compatible = "simple-audio-card";
		simple-audio-card,format = "i2s";
		simple-audio-card,name = "rockchip,sound-micarray";
		simple-audio-card,mclk-fs = <256>;
		simple-audio-card,dai-link@0 {
			format = "pdm";
			cpu {
				sound-dai = <&pdm0>;
			};
			codec {
				sound-dai = <&es7202>;
			};
		};
	};

	es8388_sound: es8388-sound {
		status = "okay";
		compatible = "rockchip,multicodecs-card";
		rockchip,card-name = "rockchip,es8388-codec";
		hp-det-gpio = <&gpio1 RK_PD0 GPIO_ACTIVE_LOW>;
		io-channels = <&saradc 3>;
		io-channel-names = "adc-detect";
		keyup-threshold-microvolt = <1800000>;
		poll-interval = <100>;
		spk-con-gpio = <&gpio4 RK_PA5 GPIO_ACTIVE_HIGH>;
		hp-con-gpio = <&gpio4 RK_PA4 GPIO_ACTIVE_HIGH>;
		rockchip,format = "i2s";
		rockchip,mclk-fs = <256>;
		rockchip,cpu = <&i2s0_8ch>;
		rockchip,codec = <&es8388>;
		rockchip,audio-routing =
			"Headphone", "LOUT1",
			"Headphone", "ROUT1",
			"Speaker", "LOUT2",
			"Speaker", "ROUT2",
			"Headphone", "Headphone Power",
			"Headphone", "Headphone Power",
			"Speaker", "Speaker Power",
			"Speaker", "Speaker Power",
			"LINPUT1", "Main Mic",
			"LINPUT2", "Main Mic",
			"RINPUT1", "Headset Mic",
			"RINPUT2", "Headset Mic";
		pinctrl-names = "default";
		pinctrl-0 = <&hp_det>;
		play-pause-key {
			label = "playpause";
			linux,code = <KEY_PLAYPAUSE>;
			press-threshold-microvolt = <2000>;
		};
	};

	hall_sensor: hall-mh248 {
		compatible = "hall-mh248";
		pinctrl-names = "default";
		pinctrl-0 = <&mh248_irq_gpio>;
		irq-gpio = <&gpio1 RK_PA1 IRQ_TYPE_EDGE_BOTH>;
		hall-active = <1>;
		status = "okay";
	};

	panel-edp {
		compatible = "innolux,p120zdg-bf4", "simple-panel";
		backlight = <&backlight>;
		power-supply = <&vcc3v3_lcd_edp>;
		prepare-delay-ms = <120>;
		enable-delay-ms = <120>;
		unprepare-delay-ms = <500>;
		disable-delay-ms = <120>;
		width-mm = <254>;
		height-mm = <169>;

		panel-timing {
			clock-frequency = <206000000>;
			hactive = <2160>;
			vactive = <1440>;
			hfront-porch = <48>;
			hsync-len = <32>;
			hback-porch = <80>;
			vfront-porch = <3>;
			vsync-len = <10>;
			vback-porch = <27>;
			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <0>;
			pixelclk-active = <0>;
		};

		port {
			panel_in_edp: endpoint {
				remote-endpoint = <&edp0_out>;
			};
		};
	};

	vcc3v3_lcd_edp: vcc3v3-lcd-edp {
		compatible = "regulator-fixed";
		regulator-name = "vcc3v3_lcd_edp";
		regulator-boot-on;
		gpio = <&gpio1 RK_PB1 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&vcc_3v3_s3>;
	};

	vcc5v0_host: vcc5v0-host {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_host";
		regulator-boot-on;
		regulator-always-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		enable-active-high;
		gpio = <&gpio4 RK_PB0 GPIO_ACTIVE_HIGH>;
		vin-supply = <&vcc5v0_usb>;
		pinctrl-names = "default";
		pinctrl-0 = <&vcc5v0_host_en>;
	};

	vcc5v0_sys: vcc5v0-sys {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_sys";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	vcc5v0_usb: vcc5v0-usb {
		compatible = "regulator-fixed";
		regulator-name = "vcc5v0_usb";
		regulator-always-on;
		regulator-boot-on;
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
	};

	vcc_mipidcphy1: vcc-mipidcphy1-regulator {
		compatible = "regulator-fixed";
		gpio = <&gpio1 RK_PB2 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&mipidcphy1_pwr>;
		regulator-name = "vcc_mipidcphy1";
		enable-active-high;
		regulator-always-on;
		regulator-boot-on;
	};

	wireless_bluetooth: wireless-bluetooth {
		compatible = "bluetooth-platdata";
		clocks = <&hym8563>;
		clock-names = "ext_clock";
		uart_rts_gpios = <&gpio3 RK_PC2 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default", "rts_gpio";
		pinctrl-0 = <&uart7m1_rtsn>,  <&bt_reset_gpio>, <&bt_wake_gpio>, <&bt_irq_gpio>;
		pinctrl-1 = <&uart7_gpios>;
		BT,reset_gpio    = <&gpio0 RK_PD4 GPIO_ACTIVE_HIGH>;
		BT,wake_gpio     = <&gpio0 RK_PB6 GPIO_ACTIVE_HIGH>;
		BT,wake_host_irq = <&gpio0 RK_PB5 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};

	wireless_wlan: wireless-wlan {
		compatible = "wlan-platdata";
		wifi_chip_type = "ap6275p";
		pinctrl-names = "default";
		pinctrl-0 = <&wifi_host_wake_irq>, <&wifi_poweren_gpio>;
		WIFI,host_wake_irq = <&gpio0 RK_PA0 GPIO_ACTIVE_HIGH>;
		WIFI,poweren_gpio = <&gpio0 RK_PC7 GPIO_ACTIVE_HIGH>;
		status = "okay";
	};
};

&av1d {
	status = "okay";
};

&av1d_mmu {
	status = "okay";
};

&avdd_1v2_cam_s0 {
	regulator-min-microvolt = <1350000>;
	regulator-max-microvolt = <1350000>;
	regulator-ramp-delay = <12500>;
};

&combphy0_ps {
	status = "okay";
};

&cpu_l0 {
	cpu-supply = <&vdd_cpu_lit_s0>;
	mem-supply = <&vdd_cpu_lit_mem_s0>;
};

&cpu_b0 {
	cpu-supply = <&vdd_cpu_big0_s0>;
	mem-supply = <&vdd_cpu_big0_mem_s0>;
};

&cpu_b2 {
	cpu-supply = <&vdd_cpu_big1_s0>;
	mem-supply = <&vdd_cpu_big1_mem_s0>;
};

&dp0 {
	status = "okay";
};

&dp0_out {
	link-frequencies = /bits/ 64 <5400000000>;
};

&dp0_in_vp1 {
	status = "okay";
};

&edp0 {
	support-psr;
	force-hpd;
	status = "okay";
};

&edp0_in_vp2 {
	status = "okay";
};

&edp0_out {
	remote-endpoint = <&panel_in_edp>;
};

&fiq_debugger {
	pinctrl-0 = <&uart2m1_xfer>;
};

&gpu {
	mali-supply = <&vdd_gpu_s0>;
	mem-supply = <&vdd_gpu_mem_s0>;
	status = "okay";
};

&hdptxphy0 {
	/* Single Vdiff Training Table for power reduction (optional) */
	training-table = /bits/ 8 <
		/* voltage swing 0, pre-emphasis 0->3 */
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		/* voltage swing 1, pre-emphasis 0->2 */
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		/* voltage swing 2, pre-emphasis 0->1 */
		0x0d 0x00 0x00 0x00 0x00 0x00
		0x0d 0x00 0x00 0x00 0x00 0x00
		/* voltage swing 3, pre-emphasis 0 */
		0x0d 0x00 0x00 0x00 0x00 0x00
	>;
	status = "okay";
};

&i2c2 {
	status = "okay";

	hym8563: hym8563@51 {
		compatible = "haoyu,hym8563";
		reg = <0x51>;
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "hym8563";
		pinctrl-names = "default";
		pinctrl-0 = <&rtc_int>;

		interrupt-parent = <&gpio0>;
		interrupts = <RK_PC4 IRQ_TYPE_LEVEL_LOW>;
		wakeup-source;
	};

	cw2015@62 {
		status = "okay";
		compatible = "cellwise,cw2015";
		reg = <0x62>;
		cellwise,battery-profile = /bits/ 8
			<0x17 0x67 0x6C 0x66 0x65 0x64 0x61 0x5B
			 0x5F 0x75 0x49 0x52 0x50 0x51 0x48 0x3D
			 0x34 0x2C 0x29 0x21 0x23 0x2D 0x40 0x49
			 0x25 0x5C 0x0B 0x85 0x10 0x1F 0x31 0x49
			 0x58 0x5E 0x63 0x6C 0x3E 0x1D 0x9A 0x35
			 0x0A 0x33 0x15 0x3B 0x70 0x99 0xAB 0x17
			 0x40 0x75 0x99 0xC4 0x80 0xB5 0xDE 0xCB
			 0x2F 0x00 0x64 0xA5 0xB5 0x00 0xF8 0x39>;
		cellwise,dual-cell = <1>;
		cellwise,monitor-interval-ms = <5000>;
		monitored-battery = <&battery>;
		power-supplies = <&bq25703>;
	};

	bq25703: bq25703@6b {
		status = "okay";
		compatible = "ti,bq25703";
		reg = <0x6b>;
		ti,usb-charger-detection = <&usbc0>;

		interrupt-parent = <&gpio0>;
		interrupts = <RK_PD2 IRQ_TYPE_LEVEL_LOW>;
		otg-mode-en-gpios = <&gpio4 RK_PA6 GPIO_ACTIVE_HIGH>;
		pinctrl-names = "default";
		pinctrl-0 = <&charger_ok>;
		extcon = <&u2phy0>;
		ti,charge-current = <2500000>;
		ti,max-input-voltage = <20000000>;
		ti,max-input-current = <6000000>;
		ti,max-charge-voltage = <8750000>;
		ti,input-current = <500000>;
		ti,input-current-sdp = <500000>;
		ti,input-current-dcp = <2000000>;
		ti,input-current-cdp = <2000000>;
		ti,minimum-sys-voltage = <7400000>;
		ti,otg-voltage = <5000000>;
		ti,otg-current = <1500000>;
		pd-charge-only = <0>;
		regulators {
			vbus5v0_typec: vbus5v0-typec {
				regulator-compatible = "otg-vbus";
				regulator-name = "vbus5v0_typec";
			};
		};
	};
};

&i2c3 {
	status = "okay";

	es8388: es8388@11 {
		status = "okay";
		#sound-dai-cells = <0>;
		compatible = "everest,es8388", "everest,es8323";
		reg = <0x11>;
		clocks = <&mclkout_i2s0>;
		clock-names = "mclk";
		assigned-clocks = <&mclkout_i2s0>;
		assigned-clock-rates = <12288000>;
		pinctrl-names = "default";
		pinctrl-0 = <&i2s0_mclk>;
	};

	es7202: es7202@32 {
		status = "okay";
		#sound-dai-cells = <0>;
		compatible = "ES7202_PDM_ADC_1";
		power-supply = <&vcc_1v8_s0>;	/* only 1v8 or 3v3, default is 3v3 */
		reg = <0x32>;
	};
};

&i2c4 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c4m3_xfer>;

	elan_touch: elan_ktf@10 {
		status = "okay";
		compatible = "elan,ektf";
		reg = <0x10>;
		pinctrl-names = "default";
		pinctrl-0 = <&touch_gpio>;
		elan,rst-gpio = <&gpio1 RK_PB4 GPIO_ACTIVE_HIGH>;
		elan,irq-gpio = <&gpio1 RK_PB5 IRQ_TYPE_LEVEL_LOW>;
		chip_type = <0x01>;	/* 1:HID IIC, 0: NORMAL IIC */
		report_type = <0x01>;	/* 1:B protocol, 0:A protocol */
	};
};

&i2c5 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c5m0_xfer>;

	mpu6500_acc: mpu_acc@68 {
		status = "okay";
		compatible = "mpu6500_acc";
		reg = <0x68>;
		irq-gpio = <&gpio3 RK_PB4 IRQ_TYPE_EDGE_RISING>;
		irq_enable = <0>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_ACCEL>;
		layout = <5>;
	};

	mpu6500_gyro: mpu_gyro@68 {
		status = "okay";
		compatible = "mpu6500_gyro";
		reg = <0x68>;
		poll_delay_ms = <30>;
		type = <SENSOR_TYPE_GYROSCOPE>;
		layout = <5>;
	};
};

&i2c6 {
	status = "disabled";
};

&i2c7 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c7m2_xfer>;

	aw8601: aw8601@c {
		compatible = "awinic,aw8601";
		status = "okay";
		reg = <0x0c>;
		rockchip,vcm-start-current = <56>;
		rockchip,vcm-rated-current = <96>;
		rockchip,vcm-step-mode = <4>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
	};

	ov13855: ov13855@10 {
		compatible = "ovti,ov13855";
		status = "okay";
		reg = <0x10>;
		clocks = <&cru CLK_MIPI_CAMARAOUT_M2>;
		clock-names = "xvclk";
		power-domains = <&power RK3588_PD_VI>;
		pinctrl-names = "default";
		pinctrl-0 = <&mipim1_camera2_clk>;
		rockchip,grf = <&sys_grf>;
		reset-gpios = <&gpio3 RK_PC5 GPIO_ACTIVE_HIGH>;
		pwdn-gpios = <&gpio3 RK_PD5 GPIO_ACTIVE_HIGH>;
		rockchip,camera-module-index = <1>;
		rockchip,camera-module-facing = "front";
		rockchip,camera-module-name = "CMK-OT2016-FV1";
		rockchip,camera-module-lens-name = "default";
		port {
			ov13855_out: endpoint {
				remote-endpoint = <&mipi_in_ucam1>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	ov50c40: ov50c40@36 {
		compatible = "ovti,ov50c40";
		status = "okay";
		reg = <0x36>;
		clocks = <&cru CLK_MIPI_CAMARAOUT_M1>;
		clock-names = "xvclk";
		power-domains = <&power RK3588_PD_VI>;
		pinctrl-names = "default";
		pinctrl-0 = <&mipim1_camera1_clk>;
		rockchip,grf = <&sys_grf>;
		reset-gpios = <&gpio3 RK_PC6 GPIO_ACTIVE_LOW>;
		pwdn-gpios = <&gpio3 RK_PD4 GPIO_ACTIVE_LOW>;
		rockchip,camera-module-index = <0>;
		rockchip,camera-module-facing = "back";
		rockchip,camera-module-name = "HZGA06";
		rockchip,camera-module-lens-name = "ZE0082C1";
		eeprom-ctrl = <&otp_eeprom>;
		lens-focus = <&aw8601>;
		port {
			ov50c40_out: endpoint {
				remote-endpoint = <&mipi_in_ov50c40>;
				data-lanes = <1 2 3 4>;
			};
		};
	};

	otp_eeprom: otp_eeprom@50 {
		compatible = "rk,otp_eeprom";
		status = "okay";
		reg = <0x50>;
	};
};

&csi2_dcphy0 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_in_ov50c40: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ov50c40_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidcphy0_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi0_csi2_input>;
			};
		};
	};
};

&csi2_dcphy1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;
		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi_in_ucam1: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&ov13855_out>;
				data-lanes = <1 2 3 4>;
			};
		};
		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			csidcphy1_out: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&mipi1_csi2_input>;
			};
		};
	};
};

&mipi_dcphy0 {
	status = "okay";
};

&mipi_dcphy1 {
	status = "okay";
};

&mipi0_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidcphy0_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi0_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in0>;
			};
		};
	};
};

&mipi1_csi2 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi1_csi2_input: endpoint@1 {
				reg = <1>;
				remote-endpoint = <&csidcphy1_out>;
			};
		};

		port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			mipi1_csi2_output: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&cif_mipi_in1>;
			};
		};
	};
};

&i2c8 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&i2c8m2_xfer>;

	usbc0: fusb302@22 {
		compatible = "fcs,fusb302";
		reg = <0x22>;
		interrupt-parent = <&gpio0>;
		interrupts = <RK_PD3 IRQ_TYPE_LEVEL_LOW>;
		int-n-gpios = <&gpio0 RK_PD3 GPIO_ACTIVE_LOW>;
		pinctrl-names = "default";
		pinctrl-0 = <&usbc0_int>;
		vbus-supply = <&vbus5v0_typec>;
		status = "okay";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				usbc0_role_sw: endpoint@0 {
					remote-endpoint = <&dwc3_0_role_switch>;
				};
			};
		};

		usb_con: connector {
			compatible = "usb-c-connector";
			label = "USB-C";
			data-role = "dual";
			power-role = "dual";
			try-power-role = "sink";
			op-sink-microwatt = <1000000>;
			sink-pdos =
				<PDO_FIXED(5000, 3000, PDO_FIXED_USB_COMM)
				 PDO_FIXED(9000, 3000, PDO_FIXED_USB_COMM)
				 PDO_FIXED(12000, 3000, PDO_FIXED_USB_COMM)>;
			source-pdos =
				<PDO_FIXED(5000, 3000, PDO_FIXED_USB_COMM)>;

			altmodes {
				#address-cells = <1>;
				#size-cells = <0>;

				altmode@0 {
					reg = <0>;
					svid = <0xff01>;
					vdo = <0xffffffff>;
				};
			};

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
					usbc0_orien_sw: endpoint {
						remote-endpoint = <&usbdp_phy0_orientation_switch>;
					};
				};

				port@1 {
					reg = <1>;
					dp_altmode_mux: endpoint {
						remote-endpoint = <&usbdp_phy0_dp_altmode_mux>;
					};
				};
			};
		};
	};
};

&i2s0_8ch {
	status = "okay";
	rockchip,trcm-sync-tx-only;
	pinctrl-0 = <&i2s0_lrck
		     &i2s0_sclk
		     &i2s0_sdi0
		     &i2s0_sdo0>;
};

&i2s2_2ch {
	pinctrl-0 = <&i2s2m1_lrck &i2s2m1_sclk &i2s2m1_sdi &i2s2m1_sdo>;
	rockchip,bclk-fs = <32>;
	status = "disabled";
};

&iep {
	status = "okay";
};

&iep_mmu {
	status = "okay";
};

&jpegd {
	status = "okay";
};

&jpegd_mmu {
	status = "okay";
};

&jpege_ccu {
	status = "okay";
};

&jpege0 {
	status = "okay";
};

&jpege0_mmu {
	status = "okay";
};

&jpege1 {
	status = "okay";
};

&jpege1_mmu {
	status = "okay";
};

&jpege2 {
	status = "okay";
};

&jpege2_mmu {
	status = "okay";
};

&jpege3 {
	status = "okay";
};

&jpege3_mmu {
	status = "okay";
};

&mpp_srv {
	status = "okay";
};

&pcie2x1l2 {
	reset-gpios = <&gpio4 RK_PC1 GPIO_ACTIVE_HIGH>;
	rockchip,skip-scan-in-resume;
	status = "okay";
};

&pdm0 {
	rockchip,path-map = <2 0 1 3>;
	status = "okay";
};

&pinctrl {
	cam {
		mipidcphy1_pwr: mipidcphy1-pwr {
			rockchip,pins =
				/* camera power en */
				<1 RK_PB2 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	charger {
		charger_ok: charger_ok {
			rockchip,pins = <0 RK_PD2 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	headphone {
		hp_det: hp-det {
			rockchip,pins = <1 RK_PD0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	hym8563 {
		rtc_int: rtc-int {
			rockchip,pins = <0 RK_PC4 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	sensor {
		mpu6500_irq_gpio: mpu6500-irq-gpio {
			rockchip,pins = <3 RK_PB4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		mh248_irq_gpio: mh248-irq-gpio {
			rockchip,pins = <1 RK_PA1 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	touch {
		touch_gpio: touch-gpio {
			rockchip,pins =
				<1 RK_PB4 RK_FUNC_GPIO &pcfg_pull_up>,
				<1 RK_PB5 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	usb {
		vcc5v0_host_en: vcc5v0-host-en {
			rockchip,pins = <4 RK_PB0 RK_FUNC_GPIO &pcfg_pull_none>;
		};
	};

	usb-typec {
		usbc0_int: usbc0-int {
			rockchip,pins = <0 RK_PD3 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};

	wireless-bluetooth {
		uart7_gpios: uart7-gpios {
			rockchip,pins = <3 RK_PC2 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_reset_gpio: bt-reset-gpio {
			rockchip,pins = <0 RK_PD4 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_wake_gpio: bt-wake-gpio {
			rockchip,pins = <0 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
		};

		bt_irq_gpio: bt-irq-gpio {
			rockchip,pins = <0 RK_PB5 RK_FUNC_GPIO &pcfg_pull_down>;
		};
	};

	wireless-wlan {
		wifi_host_wake_irq: wifi-host-wake-irq {
			rockchip,pins = <0 RK_PA0 RK_FUNC_GPIO &pcfg_pull_down>;
		};

		wifi_poweren_gpio: wifi-poweren-gpio {
			rockchip,pins = <0 RK_PC7 RK_FUNC_GPIO &pcfg_pull_up>;
		};
	};
};

&pwm12 {
	pinctrl-0 = <&pwm12m1_pins>;
	status = "okay";
};

&rga3_core0 {
	status = "okay";
};

&rga3_0_mmu {
	status = "okay";
};

&rga3_core1 {
	status = "okay";
};

&rga3_1_mmu {
	status = "okay";
};

&rga2 {
	status = "okay";
};

&rkcif {
	status = "okay";
};

&rkcif_mipi_lvds {
	status = "okay";

	port {
		cif_mipi_in0: endpoint {
			remote-endpoint = <&mipi0_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds_sditf {
	status = "okay";

	port {
		mipi_lvds_sditf: endpoint {
			remote-endpoint = <&isp1_in1>;
		};
	};
};

&rkcif_mipi_lvds1 {
	status = "okay";

	port {
		cif_mipi_in1: endpoint {
			remote-endpoint = <&mipi1_csi2_output>;
		};
	};
};

&rkcif_mipi_lvds1_sditf {
	status = "okay";

	port {
		mipi1_lvds_sditf: endpoint {
			remote-endpoint = <&isp1_in2>;
		};
	};
};

&rkcif_mmu {
	status = "okay";
};

&rkisp_unite {
	status = "okay";

};

&rkisp_unite_mmu {
	status = "okay";
};

&rkisp0_vir0 {
	status = "okay";
	/*
	 * dual isp process image case
	 * other rkisp hw and virtual nodes should disabled
	 */
	rockchip,hw = <&rkisp_unite>;
	port {
		#address-cells = <1>;
		#size-cells = <0>;

		isp1_in1: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&mipi_lvds_sditf>;
		};
		isp1_in2: endpoint@1 {
			reg = <1>;
			remote-endpoint = <&mipi1_lvds_sditf>;
		};
	};
};

&rknpu {
	rknpu-supply = <&vdd_npu_s0>;
	mem-supply = <&vdd_npu_mem_s0>;
	status = "okay";
};

&rknpu_mmu {
	status = "okay";
};

&rkvdec_ccu {
	status = "okay";
};

&rkvdec0 {
	status = "okay";
};

&rkvdec0_mmu {
	status = "okay";
};

&rkvdec1 {
	status = "okay";
};

&rkvdec1_mmu {
	status = "okay";
};

&rkvenc_ccu {
	status = "okay";
};

&rkvenc0 {
	venc-supply = <&vdd_vdenc_s0>;
	mem-supply = <&vdd_vdenc_mem_s0>;
	status = "okay";
};

&rkvenc0_mmu {
	status = "okay";
};

&rkvenc1 {
	venc-supply = <&vdd_vdenc_s0>;
	mem-supply = <&vdd_vdenc_mem_s0>;
	status = "okay";
};

&rkvenc1_mmu {
	status = "okay";
};

&rockchip_suspend {
	status = "okay";
	rockchip,sleep-debug-en = <1>;
};

&route_edp0 {
	connect = <&vp2_out_edp0>;
	status = "okay";
};

&saradc {
	status = "okay";
	vref-supply = <&avcc_1v8_s0>;
};

&sdhci {
	bus-width = <8>;
	no-sdio;
	no-sd;
	non-removable;
	max-frequency = <200000000>;
	mmc-hs400-1_8v;
	mmc-hs400-enhanced-strobe;
	status = "okay";
};

&sdmmc {
	max-frequency = <150000000>;
	no-sdio;
	no-mmc;
	bus-width = <4>;
	cap-mmc-highspeed;
	cap-sd-highspeed;
	disable-wp;
	sd-uhs-sdr104;
	vmmc-supply = <&vcc_3v3_sd_s0>;
	vqmmc-supply = <&vccio_sd_s0>;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc_bus4 &sdmmc_clk &sdmmc_cmd &sdmmc_det>;
	status = "okay";
};

&spdif_tx2 {
	status = "okay";
};

&tsadc {
	status = "okay";
};

&uart7 {
	pinctrl-names = "default";
	pinctrl-0 = <&uart7m1_xfer &uart7m1_ctsn>;
	status = "okay";
};

&u2phy0 {
	status = "okay";
};

&u2phy2 {
	status = "okay";
};

&u2phy0_otg {
	rockchip,typec-vbus-det;
	status = "okay";
};

&u2phy2_host {
	status = "okay";
	phy-supply = <&vcc5v0_host>;
};

&usb_host0_ehci {
	status = "okay";
};

&usb_host0_ohci {
	status = "okay";
};

&usbdp_phy0 {
	orientation-switch;
	svid = <0xff01>;
	sbu1-dc-gpios = <&gpio1 RK_PB6 GPIO_ACTIVE_HIGH>;
	sbu2-dc-gpios = <&gpio1 RK_PB7 GPIO_ACTIVE_HIGH>;
	status = "okay";

	port {
		#address-cells = <1>;
		#size-cells = <0>;
		usbdp_phy0_orientation_switch: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&usbc0_orien_sw>;
		};

		usbdp_phy0_dp_altmode_mux: endpoint@1 {
			reg = <1>;
			remote-endpoint = <&dp_altmode_mux>;
		};
	};
};

&usbdp_phy0_dp {
	status = "okay";
};

&usbdp_phy0_u3 {
	status = "okay";
};

&usbdrd3_0 {
	status = "okay";
};

&usbdrd_dwc3_0 {
	dr_mode = "otg";
	status = "okay";

	usb-role-switch;
	port {
		#address-cells = <1>;
		#size-cells = <0>;
		dwc3_0_role_switch: endpoint@0 {
			reg = <0>;
			remote-endpoint = <&usbc0_role_sw>;
		};
	};
};

&usbhost3_0 {
	status = "disabled";
};

&usbhost_dwc3_0 {
	status = "disabled";
};

&vdpu {
	status = "okay";
};

&vdpu_mmu {
	status = "okay";
};

&vepu {
	status = "okay";
};

&vop {
	status = "okay";
	vop-supply = <&vdd_log_s0>;
};

&vop_mmu {
	status = "okay";
};

&vp1 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER0 | 1 << ROCKCHIP_VOP2_ESMART0 |
				1 << ROCKCHIP_VOP2_CLUSTER1 | 1 << ROCKCHIP_VOP2_ESMART1)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART1>;
};

&vp2 {
	rockchip,plane-mask = <(1 << ROCKCHIP_VOP2_CLUSTER2 | 1 << ROCKCHIP_VOP2_ESMART2 |
				1 << ROCKCHIP_VOP2_CLUSTER3 | 1 << ROCKCHIP_VOP2_ESMART3)>;
	rockchip,primary-plane = <ROCKCHIP_VOP2_ESMART2>;
};
