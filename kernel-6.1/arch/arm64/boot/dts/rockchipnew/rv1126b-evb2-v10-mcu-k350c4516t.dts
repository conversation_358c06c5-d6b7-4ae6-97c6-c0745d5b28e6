// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2025 Rockchip Electronics Co., Ltd.
 */

/dts-v1/;

#include <dt-bindings/display/media-bus-format.h>
#include "rv1126b-evb2-v10.dts"

/ {
	model = "Rockchip RV1126B EVB2 V10 Board + RK EVB MCU PANLE DISPLAY Ext Board";
	compatible = "rockchip,rv1126b-evb2-v10-mcu-k350c4516t", "rockchip,rv1126b";
};

&rgb {
	status = "okay";
	rockchip,data-sync-bypass;
	pinctrl-names = "default";
	/*
	 * mcu_rgb3x8_rgb2x8_m0_pins/mcu_rgb3x8_rgb2x8_m1_pins for RGB3x8(8bit)/RGB565(8bit)
	 * mcu_rgb565_pins for RGB565(16bit)
	 */
	pinctrl-0 = <&mcu_rgb565_pins>;

	/*
	 * 320x480 RGB/MCU screen K350C4516T
	 */
	mcu_panel: mcu-panel {
		/*
		 * MEDIA_BUS_FMT_RGB888_3X8    for RGB3x8(8bit)
		 * MEDIA_BUS_FMT_RGB565_1X16   for RGB565(16bit)
		 * MEDIA_BUS_FMT_RGB565_2X8_LE for RGB565(8bit)
		 */
		bus-format = <MEDIA_BUS_FMT_RGB565_1X16>;
		backlight = <&backlight>;
		enable-gpios = <&gpio7 RK_PA7 GPIO_ACTIVE_LOW>;
		enable-delay-ms = <20>;
		reset-gpios = <&gpio5 RK_PD6 GPIO_ACTIVE_LOW>;
		reset-delay-ms = <10>;
		prepare-delay-ms = <20>;
		unprepare-delay-ms = <20>;
		disable-delay-ms = <20>;
		init-delay-ms = <10>;
		width-mm = <217>;
		height-mm = <136>;

		// type:0 is cmd, 1 is data
		panel-init-sequence = [
			//type delay num val1 val2 val3
			  00   00  01  d3
			  02   00  01  d3
			  02   00  01  d3
			  02   00  01  d3
			  02   00  01  d3 /*
					   * As the datasheet, read four times
					   * to get the panel ID.
					   */
			  00   00  01  e0
			  01   00  01  00
			  01   00  01  07
			  01   00  01  0f
			  01   00  01  0d
			  01   00  01  1b
			  01   00  01  0a
			  01   00  01  3c
			  01   00  01  78
			  01   00  01  4a
			  01   00  01  07
			  01   00  01  0e
			  01   00  01  09
			  01   00  01  1b
			  01   00  01  1e
			  01   00  01  0f
			  00   00  01  e1
			  01   00  01  00
			  01   00  01  22
			  01   00  01  24
			  01   00  01  06
			  01   00  01  12
			  01   00  01  07
			  01   00  01  36
			  01   00  01  47
			  01   00  01  47
			  01   00  01  06
			  01   00  01  0a
			  01   00  01  07
			  01   00  01  30
			  01   00  01  37
			  01   00  01  0f

			  00   00  01  c0
			  01   00  01  10
			  01   00  01  10

			  00   00  01  c1
			  01   00  01  41

			  00   00  01  c5
			  01   00  01  00
			  01   00  01  22
			  01   00  01  80

			  00   00  01  36
			  01   00  01  48

			  00   00  01  3a
			  01   00  01  55 /*
					   * interface pixel format:
					   * 66 for RGB3x8(8bit)
					   * 55 for RGB565(16bit)/RGB565(8bit)
					   */

			  00   00  01  b0
			  01   00  01  00

			  00   00  01  b1
			  01   00  01  a0 /*
					   * frame rate control:
					   * 10 (30hz) for RGB3x8(8bit)
					   * 70 (45hz) for RGB565(8bit)
					   * a0 (60hz) for RGB565(16bit)
					   */
			  01   00  01  11
			  00   00  01  b4
			  01   00  01  02
			  00   00  01  B6
			  01   00  01  02 /*
					   * display function control:
					   * 32 for RGB
					   * 02 for MCU
					   */
			  01   00  01  02

			  00   00  01  b7
			  01   00  01  c6

			  00   00  01  be
			  01   00  01  00
			  01   00  01  04

			  00   00  01  e9
			  01   00  01  00

			  00   00  01  f7
			  01   00  01  a9
			  01   00  01  51
			  01   00  01  2c
			  01   00  01  82

			  00   78  01  11
			  00   32  01  29
			  00   00  01  2c
		];

		panel-exit-sequence = [
			//type delay num val1 val2 val3
			  00   0a  01  28
			  00   78  01  10
		];

		display-timings {
			native-mode = <&kd050fwfba002_timing>;

			kd050fwfba002_timing: timing0 {
				/*
				 * 5226750  for frame rate 30Hz
				 * 7840125  for frame rate 45Hz
				 * 10453500 for frame rate 60Hz
				 */
				clock-frequency = <10453500>;
				hactive = <320>;
				vactive = <480>;
				hback-porch = <10>;
				hfront-porch = <5>;
				vback-porch = <10>;
				vfront-porch = <5>;
				hsync-len = <10>;
				vsync-len = <10>;
				hsync-active = <0>;
				vsync-active = <0>;
				de-active = <0>;
				pixelclk-active = <1>;
			};
		};

		port {
			panel_in_rgb: endpoint {
				remote-endpoint = <&rgb_out_panel>;
			};
		};
	};

	ports {
		rgb_out: port@1 {
			reg = <1>;
			#address-cells = <1>;
			#size-cells = <0>;

			rgb_out_panel: endpoint@0 {
				reg = <0>;
				remote-endpoint = <&panel_in_rgb>;
			};
		};
	};
};

&rgb_in_vop {
	status = "okay";
};

&route_rgb {
	status = "okay";
};

&vop {
	mcu-timing {
		mcu-pix-total = <6>;
		mcu-cs-pst = <1>;
		mcu-cs-pend = <5>;
		mcu-rw-pst = <2>;
		mcu-rw-pend = <4>;

		mcu-hold-mode = <0>;
	};

	mcu-bypass-timing {
		mcu-pix-total = <9>;
		mcu-cs-pst = <1>;
		mcu-cs-pend = <8>;
		mcu-rw-pst = <2>;
		mcu-rw-pend = <7>;

		mcu-hold-mode = <0>;
	};
};
