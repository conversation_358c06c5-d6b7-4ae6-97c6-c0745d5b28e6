#include <linux/init.h>
#include <linux/module.h>
#include <linux/kernel.h>

static int __init kmpp_init(void)
{
    printk(KERN_INFO "kmpp: module loaded (placeholder)\n");
    return 0;
}

static void __exit kmpp_exit(void)
{
    printk(KERN_INFO "kmpp: module unloaded (placeholder)\n");
}

module_init(kmpp_init);
module_exit(kmpp_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Rockchip");
MODULE_DESCRIPTION("KMPP placeholder module");
MODULE_VERSION("1.0");
