#include <linux/init.h>
#include <linux/module.h>
#include <linux/kernel.h>

static int __init rockit_init(void)
{
    printk(KERN_INFO "rockit: module loaded (placeholder)\n");
    return 0;
}

static void __exit rockit_exit(void)
{
    printk(KERN_INFO "rockit: module unloaded (placeholder)\n");
}

module_init(rockit_init);
module_exit(rockit_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Rockchip");
MODULE_DESCRIPTION("Rockit placeholder module");
MODULE_VERSION("1.0");
