cmake_minimum_required(VERSION 3.10)

project(ipc_drv_ko)

if(DEFINED FOR_BUILDROOT)

    add_custom_target(build_for_buildroot ALL
        COMMAND make -C ${CMAKE_SOURCE_DIR}/for_buildroot/kmpp
        COMMAND make -C ${CMAKE_SOURCE_DIR}/for_buildroot/rockit
    )
    set(IPC_KO "${CMAKE_SOURCE_DIR}/for_buildroot/rockit/release_rockit-ko_rv1126b_arm32/rockit.ko" "${CMAKE_SOURCE_DIR}/for_buildroot/kmpp/release_kmpp_rv1126b_arm32/kmpp.ko" "${CMAKE_SOURCE_DIR}/for_buildroot/insmod_ko.sh")
    message(STATUS "FOR_BUILDROOT is defined, setting up install target.")
    install(FILES ${IPC_KO} DESTINATION "lib/module")
endif()
