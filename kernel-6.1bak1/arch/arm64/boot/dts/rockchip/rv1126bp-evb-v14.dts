/* DTS-XLATE-RV1126B */
// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright (c) 2020 Rockchip Electronics Co., Ltd.
 */
/dts-v1/;
#include "rv1126bp.dtsi"
#include "rv1126-evb-v13.dtsi"
#include "rv1126-dram-default-timing.dtsi"
/ {
	model = "Rockchip RV1126B-P EVB V14 Board";
	compatible = "rockchip,rv1126bp-evb-v14", "rockchip,rv1126b";
	chosen {
		bootargs = "earlycon=uart8250,mmio32,0x20810000 console=ttyFIQ0 rw root=PARTUUID=614e0000-0000 rootfstype=ext4 rootwait snd_soc_core.prealloc_buffer_size_kbytes=16 coherent_pool=32K";
	};
};


&fspi0 {
	status = "okay";

	flash@0 {
		compatible = "spi-nand";
		reg = <0>;
		spi-max-frequency = <75000000>;
		spi-rx-bus-width = <4>;
		spi-tx-bus-width = <1>;
		pinctrl-names = "default";
		pinctrl-0 = <&fspi0_pins>;
	};
};

&wireless-bluetooth {
	compatible = "bluetooth-platdata";
	uart_rts_gpios = <&gpio3 RK_PA6 GPIO_ACTIVE_LOW>;
	pinctrl-names = "default", "rts_gpio";
	pinctrl-0 = <&uart2m0_rtsn_pins>;
	pinctrl-1 = <&uart2_gpios>;
	BT,power_gpio = <&gpio0 RK_PB1 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&wireless-wlan {
	compatible = "wlan-platdata";
	rockchip,grf = <&grf>;
	pinctrl-names = "default";
	pinctrl-0 = <&wifi_wake_host>;
	wifi_chip_type = "rk96x";
	WIFI,host_wake_irq = <&gpio0 RK_PB2 GPIO_ACTIVE_HIGH>;
	status = "okay";
};

&vcc_sys {
	compatible = "regulator-fixed";
	regulator-name = "vcc_sys";
	regulator-always-on;
	regulator-boot-on;
	regulator-min-microvolt = <3800000>;
	regulator-max-microvolt = <3800000>;
};

&vcc5v0_sys {
	compatible = "regulator-fixed";
	regulator-name = "vcc5v0_sys";
	regulator-always-on;
	regulator-boot-on;
	regulator-min-microvolt = <5000000>;
	regulator-max-microvolt = <5000000>;
	vin-supply = <&vcc_sys>;
};

&vdd_log {
	compatible = "regulator-fixed";
	regulator-name = "vdd_log";
	regulator-always-on;
	regulator-boot-on;
	regulator-min-microvolt = <900000>;
	regulator-max-microvolt = <900000>;
	vin-supply = <&vcc5v0_sys>;
};

&dram {
	status = "okay";
	dram_timing = <&dram_timing_default>;
};

&vdd_npu {
	compatible = "regulator-fixed";
	regulator-name = "vdd_npu";
	regulator-always-on;
	regulator-boot-on;
	regulator-min-microvolt = <850000>;
	regulator-max-microvolt = <950000>;
};

&vdd_vepu {
	compatible = "regulator-fixed";
	regulator-name = "vdd_vepu";
	regulator-always-on;
	regulator-boot-on;
	regulator-min-microvolt = <850000>;
	regulator-max-microvolt = <950000>;
};

&gic {
	interrupt-controller;
	#interrupt-cells = <3>;
	status = "okay";
};

&uart2 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&uart2m0_pins>;
};

&spi0 {
	status = "okay";
	pinctrl-names = "default";
	pinctrl-0 = <&spi0_pins>;
};

&sdmmc {
	status = "okay";
	bus-width = <4>;
	sd-uhs-sdr104;
	sd-uhs-sdr50;
	sd-uhs-ddr50;
	pinctrl-names = "default";
	pinctrl-0 = <&sdmmc0_pins>;
};

&sdio {
	status = "okay";
	bus-width = <4>;
	sd-uhs-sdr104;
	sd-uhs-sdr50;
	sd-uhs-ddr50;
	pinctrl-names = "default";
	pinctrl-0 = <&sdio_pins>;
};

&sdmmc2 {
	status = "okay";
	bus-width = <4>;
	cap-sd-highspeed;
	cd-gpios = <&gpio2 RK_PB4 GPIO_ACTIVE_LOW>;
	vmmc-supply = <&vcc_sd>;
};

&usb_drd_dwc3 {
	status = "okay";
	extcon = <&u2phy0>;
};

&u2phy0 {
	status = "okay";
	vbus-supply = <&vcc5v0_sys>;
};

&vcc_sd {
	compatible = "regulator-fixed";
	regulator-name = "vcc_sd";
	regulator-always-on;
	regulator-boot-on;
	regulator-min-microvolt = <3300000>;
	regulator-max-microvolt = <3300000>;
};